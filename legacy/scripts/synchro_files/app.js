'use strict';

const async = require('async');

const utils = require('./utils');

const configFile = process.argv[2];
const config = require(`./${configFile}`);
if (!config) {
  return console.error(`${new Date()} - No config file found ! `, configFile);
}

let lockFileName;

console.log(`${new Date()} - Starting `, configFile);
async.series([
  (serieCB) => utils.checkLockFilePresence(config, serieCB),
  (serieCB) => utils.createLockFile(config, (err, data) => {
    lockFileName = data;
    return serieCB(err);
  }),
  (serieCB) => utils.launchSynchro(config, serieCB),
  (serieCB) => utils.removeLockFile(lockFileName, serieCB),
], (err) => {
  if (err) {
    return console.error(`${new Date()} - ${err} - `, configFile);
  }
  console.log(`${new Date()} - Finished `, configFile);
});
