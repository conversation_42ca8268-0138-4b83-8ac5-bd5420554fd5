{"_args": [["available-typed-arrays@1.0.5", "/home/<USER>/src/github.com/Precogs-com/microservices/scripts/buyergateway/synchro_files"]], "_from": "available-typed-arrays@1.0.5", "_id": "available-typed-arrays@1.0.5", "_inBundle": false, "_integrity": "sha512-DMD0KiN46eipeziST1LPP/STfDU0sufISXmjSgvVsoU2tqxctQeASejWcfNtxYKqETM1UxQ8sp2OrSBWpHY6sw==", "_location": "/available-typed-arrays", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "available-typed-arrays@1.0.5", "name": "available-typed-arrays", "escapedName": "available-typed-arrays", "rawSpec": "1.0.5", "saveSpec": null, "fetchSpec": "1.0.5"}, "_requiredBy": ["/is-typed-array", "/which-typed-array"], "_resolved": "https://registry.npmjs.org/available-typed-arrays/-/available-typed-arrays-1.0.5.tgz", "_spec": "1.0.5", "_where": "/home/<USER>/src/github.com/Precogs-com/microservices/scripts/buyergateway/synchro_files", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "auto-changelog": {"output": "CHANGELOG.md", "template": "<PERSON><PERSON><PERSON><PERSON>", "unreleased": false, "commitLimit": false, "backfillLimit": false, "hideCredit": true}, "bugs": {"url": "https://github.com/inspect-js/available-typed-arrays/issues"}, "description": "Returns an array of Typed Array names that are available in the current environment", "devDependencies": {"@ljharb/eslint-config": "^18.0.0", "array.prototype.every": "^1.1.2", "aud": "^1.1.5", "auto-changelog": "^2.3.0", "eslint": "^7.32.0", "evalmd": "^0.0.19", "isarray": "^2.0.5", "nyc": "^10.3.2", "safe-publish-latest": "^1.1.4", "tape": "^5.3.1"}, "engines": {"node": ">= 0.4"}, "exports": {".": [{"default": "./index.js"}, "./index.js"], "./package": "./package.json", "./package.json": "./package.json"}, "funding": {"url": "https://github.com/sponsors/ljharb"}, "homepage": "https://github.com/inspect-js/available-typed-arrays#readme", "keywords": ["typed", "arrays", "Float32Array", "Float64Array", "Int8Array", "Int16Array", "Int32Array", "Uint8Array", "Uint8ClampedArray", "Uint16Array", "Uint32Array", "BigInt64Array", "BigUint64Array"], "license": "MIT", "main": "index.js", "name": "available-typed-arrays", "repository": {"type": "git", "url": "git+https://github.com/inspect-js/available-typed-arrays.git"}, "scripts": {"lint": "eslint --ext=js,mjs .", "posttest": "aud --production", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\"", "prelint": "evalmd README.md", "prepublish": "not-in-publish || npm run prepublishOnly", "prepublishOnly": "safe-publish-latest", "pretest": "npm run lint", "test": "npm run tests-only && npm run test:harmony", "test:harmony": "nyc node --harmony --es-staging test", "tests-only": "nyc tape 'test/**/*.js'", "version": "auto-changelog && git add CHANGELOG.md"}, "sideEffects": false, "type": "commonjs", "version": "1.0.5"}