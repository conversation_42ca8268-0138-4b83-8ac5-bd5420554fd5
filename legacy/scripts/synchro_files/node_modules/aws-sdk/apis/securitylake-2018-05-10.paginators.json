{"pagination": {"GetDatalakeStatus": {"input_token": "nextToken", "output_token": "nextToken", "limit_key": "maxAccountResults", "result_key": "accountSourcesList"}, "ListDatalakeExceptions": {"input_token": "nextToken", "output_token": "nextToken", "limit_key": "maxFailures", "result_key": "nonRetryableFailures"}, "ListLogSources": {"input_token": "nextToken", "output_token": "nextToken", "limit_key": "maxResults", "result_key": "regionSourceTypesAccountsList"}, "ListSubscribers": {"input_token": "nextToken", "output_token": "nextToken", "limit_key": "maxResults", "result_key": "subscribers"}}}