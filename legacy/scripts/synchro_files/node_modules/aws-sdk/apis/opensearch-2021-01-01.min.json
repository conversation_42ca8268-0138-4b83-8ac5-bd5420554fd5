{"version": "2.0", "metadata": {"apiVersion": "2021-01-01", "endpointPrefix": "es", "protocol": "rest-json", "serviceFullName": "Amazon OpenSearch Service", "serviceId": "OpenSearch", "signatureVersion": "v4", "uid": "opensearch-2021-01-01"}, "operations": {"AcceptInboundConnection": {"http": {"method": "PUT", "requestUri": "/2021-01-01/opensearch/cc/inboundConnection/{ConnectionId}/accept"}, "input": {"type": "structure", "required": ["ConnectionId"], "members": {"ConnectionId": {"location": "uri", "locationName": "ConnectionId"}}}, "output": {"type": "structure", "members": {"Connection": {"shape": "S4"}}}}, "AddTags": {"http": {"requestUri": "/2021-01-01/tags"}, "input": {"type": "structure", "required": ["ARN", "TagList"], "members": {"ARN": {}, "TagList": {"shape": "Sg"}}}}, "AssociatePackage": {"http": {"requestUri": "/2021-01-01/packages/associate/{PackageID}/{DomainName}"}, "input": {"type": "structure", "required": ["PackageID", "DomainName"], "members": {"PackageID": {"location": "uri", "locationName": "PackageID"}, "DomainName": {"location": "uri", "locationName": "DomainName"}}}, "output": {"type": "structure", "members": {"DomainPackageDetails": {"shape": "Sn"}}}}, "AuthorizeVpcEndpointAccess": {"http": {"requestUri": "/2021-01-01/opensearch/domain/{DomainName}/authorizeVpcEndpointAccess"}, "input": {"type": "structure", "required": ["DomainName", "Account"], "members": {"DomainName": {"location": "uri", "locationName": "DomainName"}, "Account": {}}}, "output": {"type": "structure", "required": ["AuthorizedPrincipal"], "members": {"AuthorizedPrincipal": {"shape": "S10"}}}}, "CancelServiceSoftwareUpdate": {"http": {"requestUri": "/2021-01-01/opensearch/serviceSoftwareUpdate/cancel"}, "input": {"type": "structure", "required": ["DomainName"], "members": {"DomainName": {}}}, "output": {"type": "structure", "members": {"ServiceSoftwareOptions": {"shape": "S15"}}}}, "CreateDomain": {"http": {"requestUri": "/2021-01-01/opensearch/domain"}, "input": {"type": "structure", "required": ["DomainName"], "members": {"DomainName": {}, "EngineVersion": {}, "ClusterConfig": {"shape": "S1b"}, "EBSOptions": {"shape": "S1h"}, "AccessPolicies": {}, "SnapshotOptions": {"shape": "S1k"}, "VPCOptions": {"shape": "S1l"}, "CognitoOptions": {"shape": "S1n"}, "EncryptionAtRestOptions": {"shape": "S1r"}, "NodeToNodeEncryptionOptions": {"shape": "S1t"}, "AdvancedOptions": {"shape": "S1u"}, "LogPublishingOptions": {"shape": "S1v"}, "DomainEndpointOptions": {"shape": "S1z"}, "AdvancedSecurityOptions": {"shape": "S22"}, "TagList": {"shape": "Sg"}, "AutoTuneOptions": {"type": "structure", "members": {"DesiredState": {}, "MaintenanceSchedules": {"shape": "S2d"}}}}}, "output": {"type": "structure", "members": {"DomainStatus": {"shape": "S2k"}}}}, "CreateOutboundConnection": {"http": {"requestUri": "/2021-01-01/opensearch/cc/outboundConnection"}, "input": {"type": "structure", "required": ["LocalDomainInfo", "RemoteDomainInfo", "ConnectionAlias"], "members": {"LocalDomainInfo": {"shape": "S5"}, "RemoteDomainInfo": {"shape": "S5"}, "ConnectionAlias": {}, "ConnectionMode": {}}}, "output": {"type": "structure", "members": {"LocalDomainInfo": {"shape": "S5"}, "RemoteDomainInfo": {"shape": "S5"}, "ConnectionAlias": {}, "ConnectionStatus": {"shape": "S30"}, "ConnectionId": {}, "ConnectionMode": {}, "ConnectionProperties": {"shape": "S32"}}}}, "CreatePackage": {"http": {"requestUri": "/2021-01-01/packages"}, "input": {"type": "structure", "required": ["PackageName", "PackageType", "PackageSource"], "members": {"PackageName": {}, "PackageType": {}, "PackageDescription": {}, "PackageSource": {"shape": "S36"}}}, "output": {"type": "structure", "members": {"PackageDetails": {"shape": "S3a"}}}}, "CreateVpcEndpoint": {"http": {"requestUri": "/2021-01-01/opensearch/vpcEndpoints"}, "input": {"type": "structure", "required": ["DomainArn", "VpcOptions"], "members": {"DomainArn": {}, "VpcOptions": {"shape": "S1l"}, "ClientToken": {}}}, "output": {"type": "structure", "required": ["VpcEndpoint"], "members": {"VpcEndpoint": {"shape": "S3h"}}}}, "DeleteDomain": {"http": {"method": "DELETE", "requestUri": "/2021-01-01/opensearch/domain/{DomainName}"}, "input": {"type": "structure", "required": ["DomainName"], "members": {"DomainName": {"location": "uri", "locationName": "DomainName"}}}, "output": {"type": "structure", "members": {"DomainStatus": {"shape": "S2k"}}}}, "DeleteInboundConnection": {"http": {"method": "DELETE", "requestUri": "/2021-01-01/opensearch/cc/inboundConnection/{ConnectionId}"}, "input": {"type": "structure", "required": ["ConnectionId"], "members": {"ConnectionId": {"location": "uri", "locationName": "ConnectionId"}}}, "output": {"type": "structure", "members": {"Connection": {"shape": "S4"}}}}, "DeleteOutboundConnection": {"http": {"method": "DELETE", "requestUri": "/2021-01-01/opensearch/cc/outboundConnection/{ConnectionId}"}, "input": {"type": "structure", "required": ["ConnectionId"], "members": {"ConnectionId": {"location": "uri", "locationName": "ConnectionId"}}}, "output": {"type": "structure", "members": {"Connection": {"shape": "S3q"}}}}, "DeletePackage": {"http": {"method": "DELETE", "requestUri": "/2021-01-01/packages/{PackageID}"}, "input": {"type": "structure", "required": ["PackageID"], "members": {"PackageID": {"location": "uri", "locationName": "PackageID"}}}, "output": {"type": "structure", "members": {"PackageDetails": {"shape": "S3a"}}}}, "DeleteVpcEndpoint": {"http": {"method": "DELETE", "requestUri": "/2021-01-01/opensearch/vpcEndpoints/{VpcEndpointId}"}, "input": {"type": "structure", "required": ["VpcEndpointId"], "members": {"VpcEndpointId": {"location": "uri", "locationName": "VpcEndpointId"}}}, "output": {"type": "structure", "required": ["VpcEndpointSummary"], "members": {"VpcEndpointSummary": {"shape": "S3v"}}}}, "DescribeDomain": {"http": {"method": "GET", "requestUri": "/2021-01-01/opensearch/domain/{DomainName}"}, "input": {"type": "structure", "required": ["DomainName"], "members": {"DomainName": {"location": "uri", "locationName": "DomainName"}}}, "output": {"type": "structure", "required": ["DomainStatus"], "members": {"DomainStatus": {"shape": "S2k"}}}}, "DescribeDomainAutoTunes": {"http": {"method": "GET", "requestUri": "/2021-01-01/opensearch/domain/{DomainName}/autoTunes"}, "input": {"type": "structure", "required": ["DomainName"], "members": {"DomainName": {"location": "uri", "locationName": "DomainName"}, "MaxResults": {"type": "integer"}, "NextToken": {}}}, "output": {"type": "structure", "members": {"AutoTunes": {"type": "list", "member": {"type": "structure", "members": {"AutoTuneType": {}, "AutoTuneDetails": {"type": "structure", "members": {"ScheduledAutoTuneDetails": {"type": "structure", "members": {"Date": {"type": "timestamp"}, "ActionType": {}, "Action": {}, "Severity": {}}}}}}}}, "NextToken": {}}}}, "DescribeDomainChangeProgress": {"http": {"method": "GET", "requestUri": "/2021-01-01/opensearch/domain/{DomainName}/progress"}, "input": {"type": "structure", "required": ["DomainName"], "members": {"DomainName": {"location": "uri", "locationName": "DomainName"}, "ChangeId": {"location": "querystring", "locationName": "changeid"}}}, "output": {"type": "structure", "members": {"ChangeProgressStatus": {"type": "structure", "members": {"ChangeId": {}, "StartTime": {"type": "timestamp"}, "Status": {}, "PendingProperties": {"shape": "S1m"}, "CompletedProperties": {"shape": "S1m"}, "TotalNumberOfStages": {"type": "integer"}, "ChangeProgressStages": {"type": "list", "member": {"type": "structure", "members": {"Name": {}, "Status": {}, "Description": {}, "LastUpdated": {"type": "timestamp"}}}}}}}}}, "DescribeDomainConfig": {"http": {"method": "GET", "requestUri": "/2021-01-01/opensearch/domain/{DomainName}/config"}, "input": {"type": "structure", "required": ["DomainName"], "members": {"DomainName": {"location": "uri", "locationName": "DomainName"}}}, "output": {"type": "structure", "required": ["DomainConfig"], "members": {"DomainConfig": {"shape": "S4o"}}}}, "DescribeDomains": {"http": {"requestUri": "/2021-01-01/opensearch/domain-info"}, "input": {"type": "structure", "required": ["DomainNames"], "members": {"DomainNames": {"type": "list", "member": {}}}}, "output": {"type": "structure", "required": ["DomainStatusList"], "members": {"DomainStatusList": {"type": "list", "member": {"shape": "S2k"}}}}}, "DescribeDryRunProgress": {"http": {"method": "GET", "requestUri": "/2021-01-01/opensearch/domain/{DomainName}/dryRun"}, "input": {"type": "structure", "required": ["DomainName"], "members": {"DomainName": {"location": "uri", "locationName": "DomainName"}, "DryRunId": {"location": "querystring", "locationName": "dryRunId"}, "LoadDryRunConfig": {"location": "querystring", "locationName": "loadDryRunConfig", "type": "boolean"}}}, "output": {"type": "structure", "members": {"DryRunProgressStatus": {"shape": "S5f"}, "DryRunConfig": {"shape": "S2k"}, "DryRunResults": {"shape": "S5i"}}}}, "DescribeInboundConnections": {"http": {"requestUri": "/2021-01-01/opensearch/cc/inboundConnection/search"}, "input": {"type": "structure", "members": {"Filters": {"shape": "S5l"}, "MaxResults": {"type": "integer"}, "NextToken": {}}}, "output": {"type": "structure", "members": {"Connections": {"type": "list", "member": {"shape": "S4"}}, "NextToken": {}}}}, "DescribeInstanceTypeLimits": {"http": {"method": "GET", "requestUri": "/2021-01-01/opensearch/instanceTypeLimits/{EngineVersion}/{InstanceType}"}, "input": {"type": "structure", "required": ["InstanceType", "EngineVersion"], "members": {"DomainName": {"location": "querystring", "locationName": "domainName"}, "InstanceType": {"location": "uri", "locationName": "InstanceType"}, "EngineVersion": {"location": "uri", "locationName": "EngineVersion"}}}, "output": {"type": "structure", "members": {"LimitsByRole": {"type": "map", "key": {}, "value": {"type": "structure", "members": {"StorageTypes": {"type": "list", "member": {"type": "structure", "members": {"StorageTypeName": {}, "StorageSubTypeName": {}, "StorageTypeLimits": {"type": "list", "member": {"type": "structure", "members": {"LimitName": {}, "LimitValues": {"shape": "S63"}}}}}}}, "InstanceLimits": {"type": "structure", "members": {"InstanceCountLimits": {"type": "structure", "members": {"MinimumInstanceCount": {"type": "integer"}, "MaximumInstanceCount": {"type": "integer"}}}}}, "AdditionalLimits": {"type": "list", "member": {"type": "structure", "members": {"LimitName": {}, "LimitValues": {"shape": "S63"}}}}}}}}}}, "DescribeOutboundConnections": {"http": {"requestUri": "/2021-01-01/opensearch/cc/outboundConnection/search"}, "input": {"type": "structure", "members": {"Filters": {"shape": "S5l"}, "MaxResults": {"type": "integer"}, "NextToken": {}}}, "output": {"type": "structure", "members": {"Connections": {"type": "list", "member": {"shape": "S3q"}}, "NextToken": {}}}}, "DescribePackages": {"http": {"requestUri": "/2021-01-01/packages/describe"}, "input": {"type": "structure", "members": {"Filters": {"type": "list", "member": {"type": "structure", "members": {"Name": {}, "Value": {"type": "list", "member": {}}}}}, "MaxResults": {"type": "integer"}, "NextToken": {}}}, "output": {"type": "structure", "members": {"PackageDetailsList": {"type": "list", "member": {"shape": "S3a"}}, "NextToken": {}}}}, "DescribeReservedInstanceOfferings": {"http": {"method": "GET", "requestUri": "/2021-01-01/opensearch/reservedInstanceOfferings"}, "input": {"type": "structure", "members": {"ReservedInstanceOfferingId": {"location": "querystring", "locationName": "offeringId"}, "MaxResults": {"location": "querystring", "locationName": "maxResults", "type": "integer"}, "NextToken": {"location": "querystring", "locationName": "nextToken"}}}, "output": {"type": "structure", "members": {"NextToken": {}, "ReservedInstanceOfferings": {"type": "list", "member": {"type": "structure", "members": {"ReservedInstanceOfferingId": {}, "InstanceType": {}, "Duration": {"type": "integer"}, "FixedPrice": {"type": "double"}, "UsagePrice": {"type": "double"}, "CurrencyCode": {}, "PaymentOption": {}, "RecurringCharges": {"shape": "S6t"}}}}}}}, "DescribeReservedInstances": {"http": {"method": "GET", "requestUri": "/2021-01-01/opensearch/reservedInstances"}, "input": {"type": "structure", "members": {"ReservedInstanceId": {"location": "querystring", "locationName": "reservationId"}, "MaxResults": {"location": "querystring", "locationName": "maxResults", "type": "integer"}, "NextToken": {"location": "querystring", "locationName": "nextToken"}}}, "output": {"type": "structure", "members": {"NextToken": {}, "ReservedInstances": {"type": "list", "member": {"type": "structure", "members": {"ReservationName": {}, "ReservedInstanceId": {}, "BillingSubscriptionId": {"type": "long"}, "ReservedInstanceOfferingId": {}, "InstanceType": {}, "StartTime": {"type": "timestamp"}, "Duration": {"type": "integer"}, "FixedPrice": {"type": "double"}, "UsagePrice": {"type": "double"}, "CurrencyCode": {}, "InstanceCount": {"type": "integer"}, "State": {}, "PaymentOption": {}, "RecurringCharges": {"shape": "S6t"}}}}}}}, "DescribeVpcEndpoints": {"http": {"requestUri": "/2021-01-01/opensearch/vpcEndpoints/describe"}, "input": {"type": "structure", "required": ["VpcEndpointIds"], "members": {"VpcEndpointIds": {"type": "list", "member": {}}}}, "output": {"type": "structure", "required": ["VpcEndpoints", "VpcEndpointErrors"], "members": {"VpcEndpoints": {"type": "list", "member": {"shape": "S3h"}}, "VpcEndpointErrors": {"type": "list", "member": {"type": "structure", "members": {"VpcEndpointId": {}, "ErrorCode": {}, "ErrorMessage": {}}}}}}}, "DissociatePackage": {"http": {"requestUri": "/2021-01-01/packages/dissociate/{PackageID}/{DomainName}"}, "input": {"type": "structure", "required": ["PackageID", "DomainName"], "members": {"PackageID": {"location": "uri", "locationName": "PackageID"}, "DomainName": {"location": "uri", "locationName": "DomainName"}}}, "output": {"type": "structure", "members": {"DomainPackageDetails": {"shape": "Sn"}}}}, "GetCompatibleVersions": {"http": {"method": "GET", "requestUri": "/2021-01-01/opensearch/compatibleVersions"}, "input": {"type": "structure", "members": {"DomainName": {"location": "querystring", "locationName": "domainName"}}}, "output": {"type": "structure", "members": {"CompatibleVersions": {"type": "list", "member": {"type": "structure", "members": {"SourceVersion": {}, "TargetVersions": {"shape": "S7e"}}}}}}}, "GetPackageVersionHistory": {"http": {"method": "GET", "requestUri": "/2021-01-01/packages/{PackageID}/history"}, "input": {"type": "structure", "required": ["PackageID"], "members": {"PackageID": {"location": "uri", "locationName": "PackageID"}, "MaxResults": {"location": "querystring", "locationName": "maxResults", "type": "integer"}, "NextToken": {"location": "querystring", "locationName": "nextToken"}}}, "output": {"type": "structure", "members": {"PackageID": {}, "PackageVersionHistoryList": {"type": "list", "member": {"type": "structure", "members": {"PackageVersion": {}, "CommitMessage": {}, "CreatedAt": {"type": "timestamp"}}}}, "NextToken": {}}}}, "GetUpgradeHistory": {"http": {"method": "GET", "requestUri": "/2021-01-01/opensearch/upgradeDomain/{DomainName}/history"}, "input": {"type": "structure", "required": ["DomainName"], "members": {"DomainName": {"location": "uri", "locationName": "DomainName"}, "MaxResults": {"location": "querystring", "locationName": "maxResults", "type": "integer"}, "NextToken": {"location": "querystring", "locationName": "nextToken"}}}, "output": {"type": "structure", "members": {"UpgradeHistories": {"type": "list", "member": {"type": "structure", "members": {"UpgradeName": {}, "StartTimestamp": {"type": "timestamp"}, "UpgradeStatus": {}, "StepsList": {"type": "list", "member": {"type": "structure", "members": {"UpgradeStep": {}, "UpgradeStepStatus": {}, "Issues": {"type": "list", "member": {}}, "ProgressPercent": {"type": "double"}}}}}}}, "NextToken": {}}}}, "GetUpgradeStatus": {"http": {"method": "GET", "requestUri": "/2021-01-01/opensearch/upgradeDomain/{DomainName}/status"}, "input": {"type": "structure", "required": ["DomainName"], "members": {"DomainName": {"location": "uri", "locationName": "DomainName"}}}, "output": {"type": "structure", "members": {"UpgradeStep": {}, "StepStatus": {}, "UpgradeName": {}}}}, "ListDomainNames": {"http": {"method": "GET", "requestUri": "/2021-01-01/domain"}, "input": {"type": "structure", "members": {"EngineType": {"location": "querystring", "locationName": "engineType"}}}, "output": {"type": "structure", "members": {"DomainNames": {"type": "list", "member": {"type": "structure", "members": {"DomainName": {}, "EngineType": {}}}}}}}, "ListDomainsForPackage": {"http": {"method": "GET", "requestUri": "/2021-01-01/packages/{PackageID}/domains"}, "input": {"type": "structure", "required": ["PackageID"], "members": {"PackageID": {"location": "uri", "locationName": "PackageID"}, "MaxResults": {"location": "querystring", "locationName": "maxResults", "type": "integer"}, "NextToken": {"location": "querystring", "locationName": "nextToken"}}}, "output": {"type": "structure", "members": {"DomainPackageDetailsList": {"shape": "S85"}, "NextToken": {}}}}, "ListInstanceTypeDetails": {"http": {"method": "GET", "requestUri": "/2021-01-01/opensearch/instanceTypeDetails/{EngineVersion}"}, "input": {"type": "structure", "required": ["EngineVersion"], "members": {"EngineVersion": {"location": "uri", "locationName": "EngineVersion"}, "DomainName": {"location": "querystring", "locationName": "domainName"}, "MaxResults": {"location": "querystring", "locationName": "maxResults", "type": "integer"}, "NextToken": {"location": "querystring", "locationName": "nextToken"}}}, "output": {"type": "structure", "members": {"InstanceTypeDetails": {"type": "list", "member": {"type": "structure", "members": {"InstanceType": {}, "EncryptionEnabled": {"type": "boolean"}, "CognitoEnabled": {"type": "boolean"}, "AppLogsEnabled": {"type": "boolean"}, "AdvancedSecurityEnabled": {"type": "boolean"}, "WarmEnabled": {"type": "boolean"}, "InstanceRole": {"type": "list", "member": {}}}}}, "NextToken": {}}}}, "ListPackagesForDomain": {"http": {"method": "GET", "requestUri": "/2021-01-01/domain/{DomainName}/packages"}, "input": {"type": "structure", "required": ["DomainName"], "members": {"DomainName": {"location": "uri", "locationName": "DomainName"}, "MaxResults": {"location": "querystring", "locationName": "maxResults", "type": "integer"}, "NextToken": {"location": "querystring", "locationName": "nextToken"}}}, "output": {"type": "structure", "members": {"DomainPackageDetailsList": {"shape": "S85"}, "NextToken": {}}}}, "ListTags": {"http": {"method": "GET", "requestUri": "/2021-01-01/tags/"}, "input": {"type": "structure", "required": ["ARN"], "members": {"ARN": {"location": "querystring", "locationName": "arn"}}}, "output": {"type": "structure", "members": {"TagList": {"shape": "Sg"}}}}, "ListVersions": {"http": {"method": "GET", "requestUri": "/2021-01-01/opensearch/versions"}, "input": {"type": "structure", "members": {"MaxResults": {"location": "querystring", "locationName": "maxResults", "type": "integer"}, "NextToken": {"location": "querystring", "locationName": "nextToken"}}}, "output": {"type": "structure", "members": {"Versions": {"shape": "S7e"}, "NextToken": {}}}}, "ListVpcEndpointAccess": {"http": {"method": "GET", "requestUri": "/2021-01-01/opensearch/domain/{DomainName}/listVpcEndpointAccess"}, "input": {"type": "structure", "required": ["DomainName"], "members": {"DomainName": {"location": "uri", "locationName": "DomainName"}, "NextToken": {"location": "querystring", "locationName": "nextToken"}}}, "output": {"type": "structure", "required": ["AuthorizedPrincipalList", "NextToken"], "members": {"AuthorizedPrincipalList": {"type": "list", "member": {"shape": "S10"}}, "NextToken": {}}}}, "ListVpcEndpoints": {"http": {"method": "GET", "requestUri": "/2021-01-01/opensearch/vpcEndpoints"}, "input": {"type": "structure", "members": {"NextToken": {"location": "querystring", "locationName": "nextToken"}}}, "output": {"type": "structure", "required": ["VpcEndpointSummaryList", "NextToken"], "members": {"VpcEndpointSummaryList": {"shape": "S8m"}, "NextToken": {}}}}, "ListVpcEndpointsForDomain": {"http": {"method": "GET", "requestUri": "/2021-01-01/opensearch/domain/{DomainName}/vpcEndpoints"}, "input": {"type": "structure", "required": ["DomainName"], "members": {"DomainName": {"location": "uri", "locationName": "DomainName"}, "NextToken": {"location": "querystring", "locationName": "nextToken"}}}, "output": {"type": "structure", "required": ["VpcEndpointSummaryList", "NextToken"], "members": {"VpcEndpointSummaryList": {"shape": "S8m"}, "NextToken": {}}}}, "PurchaseReservedInstanceOffering": {"http": {"requestUri": "/2021-01-01/opensearch/purchaseReservedInstanceOffering"}, "input": {"type": "structure", "required": ["ReservedInstanceOfferingId", "ReservationName"], "members": {"ReservedInstanceOfferingId": {}, "ReservationName": {}, "InstanceCount": {"type": "integer"}}}, "output": {"type": "structure", "members": {"ReservedInstanceId": {}, "ReservationName": {}}}}, "RejectInboundConnection": {"http": {"method": "PUT", "requestUri": "/2021-01-01/opensearch/cc/inboundConnection/{ConnectionId}/reject"}, "input": {"type": "structure", "required": ["ConnectionId"], "members": {"ConnectionId": {"location": "uri", "locationName": "ConnectionId"}}}, "output": {"type": "structure", "members": {"Connection": {"shape": "S4"}}}}, "RemoveTags": {"http": {"requestUri": "/2021-01-01/tags-removal"}, "input": {"type": "structure", "required": ["ARN", "TagKeys"], "members": {"ARN": {}, "TagKeys": {"shape": "S1m"}}}}, "RevokeVpcEndpointAccess": {"http": {"requestUri": "/2021-01-01/opensearch/domain/{DomainName}/revokeVpcEndpointAccess"}, "input": {"type": "structure", "required": ["DomainName", "Account"], "members": {"DomainName": {"location": "uri", "locationName": "DomainName"}, "Account": {}}}, "output": {"type": "structure", "members": {}}}, "StartServiceSoftwareUpdate": {"http": {"requestUri": "/2021-01-01/opensearch/serviceSoftwareUpdate/start"}, "input": {"type": "structure", "required": ["DomainName"], "members": {"DomainName": {}}}, "output": {"type": "structure", "members": {"ServiceSoftwareOptions": {"shape": "S15"}}}}, "UpdateDomainConfig": {"http": {"requestUri": "/2021-01-01/opensearch/domain/{DomainName}/config"}, "input": {"type": "structure", "required": ["DomainName"], "members": {"DomainName": {"location": "uri", "locationName": "DomainName"}, "ClusterConfig": {"shape": "S1b"}, "EBSOptions": {"shape": "S1h"}, "SnapshotOptions": {"shape": "S1k"}, "VPCOptions": {"shape": "S1l"}, "CognitoOptions": {"shape": "S1n"}, "AdvancedOptions": {"shape": "S1u"}, "AccessPolicies": {}, "LogPublishingOptions": {"shape": "S1v"}, "EncryptionAtRestOptions": {"shape": "S1r"}, "DomainEndpointOptions": {"shape": "S1z"}, "NodeToNodeEncryptionOptions": {"shape": "S1t"}, "AdvancedSecurityOptions": {"shape": "S22"}, "AutoTuneOptions": {"shape": "S56"}, "DryRun": {"type": "boolean"}, "DryRunMode": {}}}, "output": {"type": "structure", "required": ["DomainConfig"], "members": {"DomainConfig": {"shape": "S4o"}, "DryRunResults": {"shape": "S5i"}, "DryRunProgressStatus": {"shape": "S5f"}}}}, "UpdatePackage": {"http": {"requestUri": "/2021-01-01/packages/update"}, "input": {"type": "structure", "required": ["PackageID", "PackageSource"], "members": {"PackageID": {}, "PackageSource": {"shape": "S36"}, "PackageDescription": {}, "CommitMessage": {}}}, "output": {"type": "structure", "members": {"PackageDetails": {"shape": "S3a"}}}}, "UpdateVpcEndpoint": {"http": {"requestUri": "/2021-01-01/opensearch/vpcEndpoints/update"}, "input": {"type": "structure", "required": ["VpcEndpointId", "VpcOptions"], "members": {"VpcEndpointId": {}, "VpcOptions": {"shape": "S1l"}}}, "output": {"type": "structure", "required": ["VpcEndpoint"], "members": {"VpcEndpoint": {"shape": "S3h"}}}}, "UpgradeDomain": {"http": {"requestUri": "/2021-01-01/opensearch/upgradeDomain"}, "input": {"type": "structure", "required": ["DomainName", "TargetVersion"], "members": {"DomainName": {}, "TargetVersion": {}, "PerformCheckOnly": {"type": "boolean"}, "AdvancedOptions": {"shape": "S1u"}}}, "output": {"type": "structure", "members": {"UpgradeId": {}, "DomainName": {}, "TargetVersion": {}, "PerformCheckOnly": {"type": "boolean"}, "AdvancedOptions": {"shape": "S1u"}, "ChangeProgressDetails": {"shape": "S2u"}}}}}, "shapes": {"S4": {"type": "structure", "members": {"LocalDomainInfo": {"shape": "S5"}, "RemoteDomainInfo": {"shape": "S5"}, "ConnectionId": {}, "ConnectionStatus": {"type": "structure", "members": {"StatusCode": {}, "Message": {}}}, "ConnectionMode": {}}}, "S5": {"type": "structure", "members": {"AWSDomainInformation": {"type": "structure", "required": ["DomainName"], "members": {"OwnerId": {}, "DomainName": {}, "Region": {}}}}}, "Sg": {"type": "list", "member": {"type": "structure", "required": ["Key", "Value"], "members": {"Key": {}, "Value": {}}}}, "Sn": {"type": "structure", "members": {"PackageID": {}, "PackageName": {}, "PackageType": {}, "LastUpdated": {"type": "timestamp"}, "DomainName": {}, "DomainPackageStatus": {}, "PackageVersion": {}, "ReferencePath": {}, "ErrorDetails": {"shape": "Su"}}}, "Su": {"type": "structure", "members": {"ErrorType": {}, "ErrorMessage": {}}}, "S10": {"type": "structure", "members": {"PrincipalType": {}, "Principal": {}}}, "S15": {"type": "structure", "members": {"CurrentVersion": {}, "NewVersion": {}, "UpdateAvailable": {"type": "boolean"}, "Cancellable": {"type": "boolean"}, "UpdateStatus": {}, "Description": {}, "AutomatedUpdateDate": {"type": "timestamp"}, "OptionalDeployment": {"type": "boolean"}}}, "S1b": {"type": "structure", "members": {"InstanceType": {}, "InstanceCount": {"type": "integer"}, "DedicatedMasterEnabled": {"type": "boolean"}, "ZoneAwarenessEnabled": {"type": "boolean"}, "ZoneAwarenessConfig": {"type": "structure", "members": {"AvailabilityZoneCount": {"type": "integer"}}}, "DedicatedMasterType": {}, "DedicatedMasterCount": {"type": "integer"}, "WarmEnabled": {"type": "boolean"}, "WarmType": {}, "WarmCount": {"type": "integer"}, "ColdStorageOptions": {"type": "structure", "required": ["Enabled"], "members": {"Enabled": {"type": "boolean"}}}}}, "S1h": {"type": "structure", "members": {"EBSEnabled": {"type": "boolean"}, "VolumeType": {}, "VolumeSize": {"type": "integer"}, "Iops": {"type": "integer"}, "Throughput": {"type": "integer"}}}, "S1k": {"type": "structure", "members": {"AutomatedSnapshotStartHour": {"type": "integer"}}}, "S1l": {"type": "structure", "members": {"SubnetIds": {"shape": "S1m"}, "SecurityGroupIds": {"shape": "S1m"}}}, "S1m": {"type": "list", "member": {}}, "S1n": {"type": "structure", "members": {"Enabled": {"type": "boolean"}, "UserPoolId": {}, "IdentityPoolId": {}, "RoleArn": {}}}, "S1r": {"type": "structure", "members": {"Enabled": {"type": "boolean"}, "KmsKeyId": {}}}, "S1t": {"type": "structure", "members": {"Enabled": {"type": "boolean"}}}, "S1u": {"type": "map", "key": {}, "value": {}}, "S1v": {"type": "map", "key": {}, "value": {"type": "structure", "members": {"CloudWatchLogsLogGroupArn": {}, "Enabled": {"type": "boolean"}}}}, "S1z": {"type": "structure", "members": {"EnforceHTTPS": {"type": "boolean"}, "TLSSecurityPolicy": {}, "CustomEndpointEnabled": {"type": "boolean"}, "CustomEndpoint": {}, "CustomEndpointCertificateArn": {}}}, "S22": {"type": "structure", "members": {"Enabled": {"type": "boolean"}, "InternalUserDatabaseEnabled": {"type": "boolean"}, "MasterUserOptions": {"type": "structure", "members": {"MasterUserARN": {}, "MasterUserName": {"shape": "S24"}, "MasterUserPassword": {"type": "string", "sensitive": true}}}, "SAMLOptions": {"type": "structure", "members": {"Enabled": {"type": "boolean"}, "Idp": {"shape": "S27"}, "MasterUserName": {"shape": "S24"}, "MasterBackendRole": {}, "SubjectKey": {}, "RolesKey": {}, "SessionTimeoutMinutes": {"type": "integer"}}}, "AnonymousAuthEnabled": {"type": "boolean"}}}, "S24": {"type": "string", "sensitive": true}, "S27": {"type": "structure", "required": ["Metada<PERSON><PERSON><PERSON><PERSON>", "EntityId"], "members": {"MetadataContent": {}, "EntityId": {}}}, "S2d": {"type": "list", "member": {"type": "structure", "members": {"StartAt": {"type": "timestamp"}, "Duration": {"type": "structure", "members": {"Value": {"type": "long"}, "Unit": {}}}, "CronExpressionForRecurrence": {}}}}, "S2k": {"type": "structure", "required": ["DomainId", "DomainName", "ARN", "ClusterConfig"], "members": {"DomainId": {}, "DomainName": {}, "ARN": {}, "Created": {"type": "boolean"}, "Deleted": {"type": "boolean"}, "Endpoint": {}, "Endpoints": {"type": "map", "key": {}, "value": {}}, "Processing": {"type": "boolean"}, "UpgradeProcessing": {"type": "boolean"}, "EngineVersion": {}, "ClusterConfig": {"shape": "S1b"}, "EBSOptions": {"shape": "S1h"}, "AccessPolicies": {}, "SnapshotOptions": {"shape": "S1k"}, "VPCOptions": {"shape": "S2o"}, "CognitoOptions": {"shape": "S1n"}, "EncryptionAtRestOptions": {"shape": "S1r"}, "NodeToNodeEncryptionOptions": {"shape": "S1t"}, "AdvancedOptions": {"shape": "S1u"}, "LogPublishingOptions": {"shape": "S1v"}, "ServiceSoftwareOptions": {"shape": "S15"}, "DomainEndpointOptions": {"shape": "S1z"}, "AdvancedSecurityOptions": {"shape": "S2p"}, "AutoTuneOptions": {"type": "structure", "members": {"State": {}, "ErrorMessage": {}}}, "ChangeProgressDetails": {"shape": "S2u"}}}, "S2o": {"type": "structure", "members": {"VPCId": {}, "SubnetIds": {"shape": "S1m"}, "AvailabilityZones": {"shape": "S1m"}, "SecurityGroupIds": {"shape": "S1m"}}}, "S2p": {"type": "structure", "members": {"Enabled": {"type": "boolean"}, "InternalUserDatabaseEnabled": {"type": "boolean"}, "SAMLOptions": {"type": "structure", "members": {"Enabled": {"type": "boolean"}, "Idp": {"shape": "S27"}, "SubjectKey": {}, "RolesKey": {}, "SessionTimeoutMinutes": {"type": "integer"}}}, "AnonymousAuthDisableDate": {"type": "timestamp"}, "AnonymousAuthEnabled": {"type": "boolean"}}}, "S2u": {"type": "structure", "members": {"ChangeId": {}, "Message": {}}}, "S30": {"type": "structure", "members": {"StatusCode": {}, "Message": {}}}, "S32": {"type": "structure", "members": {"Endpoint": {}}}, "S36": {"type": "structure", "members": {"S3BucketName": {}, "S3Key": {}}}, "S3a": {"type": "structure", "members": {"PackageID": {}, "PackageName": {}, "PackageType": {}, "PackageDescription": {}, "PackageStatus": {}, "CreatedAt": {"type": "timestamp"}, "LastUpdatedAt": {"type": "timestamp"}, "AvailablePackageVersion": {}, "ErrorDetails": {"shape": "Su"}}}, "S3h": {"type": "structure", "members": {"VpcEndpointId": {}, "VpcEndpointOwner": {}, "DomainArn": {}, "VpcOptions": {"shape": "S2o"}, "Status": {}, "Endpoint": {}}}, "S3q": {"type": "structure", "members": {"LocalDomainInfo": {"shape": "S5"}, "RemoteDomainInfo": {"shape": "S5"}, "ConnectionId": {}, "ConnectionAlias": {}, "ConnectionStatus": {"shape": "S30"}, "ConnectionMode": {}, "ConnectionProperties": {"shape": "S32"}}}, "S3v": {"type": "structure", "members": {"VpcEndpointId": {}, "VpcEndpointOwner": {}, "DomainArn": {}, "Status": {}}}, "S4o": {"type": "structure", "members": {"EngineVersion": {"type": "structure", "required": ["Options", "Status"], "members": {"Options": {}, "Status": {"shape": "S4q"}}}, "ClusterConfig": {"type": "structure", "required": ["Options", "Status"], "members": {"Options": {"shape": "S1b"}, "Status": {"shape": "S4q"}}}, "EBSOptions": {"type": "structure", "required": ["Options", "Status"], "members": {"Options": {"shape": "S1h"}, "Status": {"shape": "S4q"}}}, "AccessPolicies": {"type": "structure", "required": ["Options", "Status"], "members": {"Options": {}, "Status": {"shape": "S4q"}}}, "SnapshotOptions": {"type": "structure", "required": ["Options", "Status"], "members": {"Options": {"shape": "S1k"}, "Status": {"shape": "S4q"}}}, "VPCOptions": {"type": "structure", "required": ["Options", "Status"], "members": {"Options": {"shape": "S2o"}, "Status": {"shape": "S4q"}}}, "CognitoOptions": {"type": "structure", "required": ["Options", "Status"], "members": {"Options": {"shape": "S1n"}, "Status": {"shape": "S4q"}}}, "EncryptionAtRestOptions": {"type": "structure", "required": ["Options", "Status"], "members": {"Options": {"shape": "S1r"}, "Status": {"shape": "S4q"}}}, "NodeToNodeEncryptionOptions": {"type": "structure", "required": ["Options", "Status"], "members": {"Options": {"shape": "S1t"}, "Status": {"shape": "S4q"}}}, "AdvancedOptions": {"type": "structure", "required": ["Options", "Status"], "members": {"Options": {"shape": "S1u"}, "Status": {"shape": "S4q"}}}, "LogPublishingOptions": {"type": "structure", "members": {"Options": {"shape": "S1v"}, "Status": {"shape": "S4q"}}}, "DomainEndpointOptions": {"type": "structure", "required": ["Options", "Status"], "members": {"Options": {"shape": "S1z"}, "Status": {"shape": "S4q"}}}, "AdvancedSecurityOptions": {"type": "structure", "required": ["Options", "Status"], "members": {"Options": {"shape": "S2p"}, "Status": {"shape": "S4q"}}}, "AutoTuneOptions": {"type": "structure", "members": {"Options": {"shape": "S56"}, "Status": {"type": "structure", "required": ["CreationDate", "UpdateDate", "State"], "members": {"CreationDate": {"type": "timestamp"}, "UpdateDate": {"type": "timestamp"}, "UpdateVersion": {"type": "integer"}, "State": {}, "ErrorMessage": {}, "PendingDeletion": {"type": "boolean"}}}}}, "ChangeProgressDetails": {"shape": "S2u"}}}, "S4q": {"type": "structure", "required": ["CreationDate", "UpdateDate", "State"], "members": {"CreationDate": {"type": "timestamp"}, "UpdateDate": {"type": "timestamp"}, "UpdateVersion": {"type": "integer"}, "State": {}, "PendingDeletion": {"type": "boolean"}}}, "S56": {"type": "structure", "members": {"DesiredState": {}, "RollbackOnDisable": {}, "MaintenanceSchedules": {"shape": "S2d"}}}, "S5f": {"type": "structure", "required": ["DryRunId", "DryRunStatus", "CreationDate", "UpdateDate"], "members": {"DryRunId": {}, "DryRunStatus": {}, "CreationDate": {}, "UpdateDate": {}, "ValidationFailures": {"type": "list", "member": {"type": "structure", "members": {"Code": {}, "Message": {}}}}}}, "S5i": {"type": "structure", "members": {"DeploymentType": {}, "Message": {}}}, "S5l": {"type": "list", "member": {"type": "structure", "members": {"Name": {}, "Values": {"type": "list", "member": {}}}}}, "S63": {"type": "list", "member": {}}, "S6t": {"type": "list", "member": {"type": "structure", "members": {"RecurringChargeAmount": {"type": "double"}, "RecurringChargeFrequency": {}}}}, "S7e": {"type": "list", "member": {}}, "S85": {"type": "list", "member": {"shape": "Sn"}}, "S8m": {"type": "list", "member": {"shape": "S3v"}}}}