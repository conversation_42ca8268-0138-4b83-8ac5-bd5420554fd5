{"version": "2.0", "metadata": {"apiVersion": "2020-07-14", "endpointPrefix": "ivs", "jsonVersion": "1.1", "protocol": "rest-json", "serviceAbbreviation": "Amazon IVS", "serviceFullName": "Amazon Interactive Video Service", "serviceId": "ivs", "signatureVersion": "v4", "signingName": "ivs", "uid": "ivs-2020-07-14"}, "operations": {"BatchGetChannel": {"http": {"requestUri": "/BatchGetChannel", "responseCode": 200}, "input": {"type": "structure", "required": ["arns"], "members": {"arns": {"type": "list", "member": {}}}}, "output": {"type": "structure", "members": {"channels": {"type": "list", "member": {"shape": "S6"}}, "errors": {"shape": "Sh"}}}}, "BatchGetStreamKey": {"http": {"requestUri": "/BatchGetStreamKey", "responseCode": 200}, "input": {"type": "structure", "required": ["arns"], "members": {"arns": {"type": "list", "member": {}}}}, "output": {"type": "structure", "members": {"errors": {"shape": "Sh"}, "streamKeys": {"type": "list", "member": {"shape": "<PERSON>"}}}}}, "CreateChannel": {"http": {"requestUri": "/CreateChannel", "responseCode": 200}, "input": {"type": "structure", "members": {"authorized": {"type": "boolean"}, "latencyMode": {}, "name": {}, "recordingConfigurationArn": {}, "tags": {"shape": "Sd"}, "type": {}}}, "output": {"type": "structure", "members": {"channel": {"shape": "S6"}, "streamKey": {"shape": "<PERSON>"}}}}, "CreateRecordingConfiguration": {"http": {"requestUri": "/CreateRecordingConfiguration", "responseCode": 200}, "input": {"type": "structure", "required": ["destinationConfiguration"], "members": {"destinationConfiguration": {"shape": "Sx"}, "name": {}, "recordingReconnectWindowSeconds": {"type": "integer"}, "tags": {"shape": "Sd"}, "thumbnailConfiguration": {"shape": "S12"}}}, "output": {"type": "structure", "members": {"recordingConfiguration": {"shape": "S16"}}}}, "CreateStreamKey": {"http": {"requestUri": "/CreateStreamKey", "responseCode": 200}, "input": {"type": "structure", "required": ["channelArn"], "members": {"channelArn": {}, "tags": {"shape": "Sd"}}}, "output": {"type": "structure", "members": {"streamKey": {"shape": "<PERSON>"}}}}, "DeleteChannel": {"http": {"requestUri": "/DeleteChannel", "responseCode": 204}, "input": {"type": "structure", "required": ["arn"], "members": {"arn": {}}}}, "DeletePlaybackKeyPair": {"http": {"requestUri": "/DeletePlaybackKeyPair", "responseCode": 200}, "input": {"type": "structure", "required": ["arn"], "members": {"arn": {}}}, "output": {"type": "structure", "members": {}}}, "DeleteRecordingConfiguration": {"http": {"requestUri": "/DeleteRecordingConfiguration", "responseCode": 200}, "input": {"type": "structure", "required": ["arn"], "members": {"arn": {}}}}, "DeleteStreamKey": {"http": {"requestUri": "/DeleteStreamKey", "responseCode": 204}, "input": {"type": "structure", "required": ["arn"], "members": {"arn": {}}}}, "GetChannel": {"http": {"requestUri": "/GetChannel", "responseCode": 200}, "input": {"type": "structure", "required": ["arn"], "members": {"arn": {}}}, "output": {"type": "structure", "members": {"channel": {"shape": "S6"}}}}, "GetPlaybackKeyPair": {"http": {"requestUri": "/GetPlaybackKeyPair", "responseCode": 200}, "input": {"type": "structure", "required": ["arn"], "members": {"arn": {}}}, "output": {"type": "structure", "members": {"keyPair": {"shape": "S1l"}}}}, "GetRecordingConfiguration": {"http": {"requestUri": "/GetRecordingConfiguration", "responseCode": 200}, "input": {"type": "structure", "required": ["arn"], "members": {"arn": {}}}, "output": {"type": "structure", "members": {"recordingConfiguration": {"shape": "S16"}}}}, "GetStream": {"http": {"requestUri": "/GetStream", "responseCode": 200}, "input": {"type": "structure", "required": ["channelArn"], "members": {"channelArn": {}}}, "output": {"type": "structure", "members": {"stream": {"type": "structure", "members": {"channelArn": {}, "health": {}, "playbackUrl": {}, "startTime": {"shape": "S1u"}, "state": {}, "streamId": {}, "viewerCount": {"type": "long"}}}}}}, "GetStreamKey": {"http": {"requestUri": "/GetStreamKey", "responseCode": 200}, "input": {"type": "structure", "required": ["arn"], "members": {"arn": {}}}, "output": {"type": "structure", "members": {"streamKey": {"shape": "<PERSON>"}}}}, "GetStreamSession": {"http": {"requestUri": "/GetStreamSession", "responseCode": 200}, "input": {"type": "structure", "required": ["channelArn"], "members": {"channelArn": {}, "streamId": {}}}, "output": {"type": "structure", "members": {"streamSession": {"type": "structure", "members": {"channel": {"shape": "S6"}, "endTime": {"shape": "S23"}, "ingestConfiguration": {"type": "structure", "members": {"audio": {"type": "structure", "members": {"channels": {"type": "long"}, "codec": {}, "sampleRate": {"type": "long"}, "targetBitrate": {"type": "long"}}}, "video": {"type": "structure", "members": {"avcLevel": {}, "avcProfile": {}, "codec": {}, "encoder": {}, "targetBitrate": {"type": "long"}, "targetFramerate": {"type": "long"}, "videoHeight": {"type": "long"}, "videoWidth": {"type": "long"}}}}}, "recordingConfiguration": {"shape": "S16"}, "startTime": {"shape": "S23"}, "streamId": {}, "truncatedEvents": {"type": "list", "member": {"type": "structure", "members": {"eventTime": {"shape": "S23"}, "name": {}, "type": {}}}}}}}}}, "ImportPlaybackKeyPair": {"http": {"requestUri": "/ImportPlaybackKeyPair", "responseCode": 200}, "input": {"type": "structure", "required": ["publicKeyMaterial"], "members": {"name": {}, "publicKeyMaterial": {}, "tags": {"shape": "Sd"}}}, "output": {"type": "structure", "members": {"keyPair": {"shape": "S1l"}}}}, "ListChannels": {"http": {"requestUri": "/ListChannels", "responseCode": 200}, "input": {"type": "structure", "members": {"filterByName": {}, "filterByRecordingConfigurationArn": {}, "maxResults": {"type": "integer"}, "nextToken": {}}}, "output": {"type": "structure", "required": ["channels"], "members": {"channels": {"type": "list", "member": {"type": "structure", "members": {"arn": {}, "authorized": {"type": "boolean"}, "latencyMode": {}, "name": {}, "recordingConfigurationArn": {}, "tags": {"shape": "Sd"}}}}, "nextToken": {}}}}, "ListPlaybackKeyPairs": {"http": {"requestUri": "/ListPlaybackKeyPairs", "responseCode": 200}, "input": {"type": "structure", "members": {"maxResults": {"type": "integer"}, "nextToken": {}}}, "output": {"type": "structure", "required": ["keyPairs"], "members": {"keyPairs": {"type": "list", "member": {"type": "structure", "members": {"arn": {}, "name": {}, "tags": {"shape": "Sd"}}}}, "nextToken": {}}}}, "ListRecordingConfigurations": {"http": {"requestUri": "/ListRecordingConfigurations", "responseCode": 200}, "input": {"type": "structure", "members": {"maxResults": {"type": "integer"}, "nextToken": {}}}, "output": {"type": "structure", "required": ["recordingConfigurations"], "members": {"nextToken": {}, "recordingConfigurations": {"type": "list", "member": {"type": "structure", "required": ["arn", "destinationConfiguration", "state"], "members": {"arn": {}, "destinationConfiguration": {"shape": "Sx"}, "name": {}, "state": {}, "tags": {"shape": "Sd"}}}}}}}, "ListStreamKeys": {"http": {"requestUri": "/ListStreamKeys", "responseCode": 200}, "input": {"type": "structure", "required": ["channelArn"], "members": {"channelArn": {}, "maxResults": {"type": "integer"}, "nextToken": {}}}, "output": {"type": "structure", "required": ["streamKeys"], "members": {"nextToken": {}, "streamKeys": {"type": "list", "member": {"type": "structure", "members": {"arn": {}, "channelArn": {}, "tags": {"shape": "Sd"}}}}}}}, "ListStreamSessions": {"http": {"requestUri": "/ListStreamSessions", "responseCode": 200}, "input": {"type": "structure", "required": ["channelArn"], "members": {"channelArn": {}, "maxResults": {"type": "integer"}, "nextToken": {}}}, "output": {"type": "structure", "required": ["streamSessions"], "members": {"nextToken": {}, "streamSessions": {"type": "list", "member": {"type": "structure", "members": {"endTime": {"shape": "S23"}, "hasErrorEvent": {"type": "boolean"}, "startTime": {"shape": "S23"}, "streamId": {}}}}}}}, "ListStreams": {"http": {"requestUri": "/ListStreams", "responseCode": 200}, "input": {"type": "structure", "members": {"filterBy": {"type": "structure", "members": {"health": {}}}, "maxResults": {"type": "integer"}, "nextToken": {}}}, "output": {"type": "structure", "required": ["streams"], "members": {"nextToken": {}, "streams": {"type": "list", "member": {"type": "structure", "members": {"channelArn": {}, "health": {}, "startTime": {"shape": "S1u"}, "state": {}, "streamId": {}, "viewerCount": {"type": "long"}}}}}}}, "ListTagsForResource": {"http": {"method": "GET", "requestUri": "/tags/{resourceArn}", "responseCode": 200}, "input": {"type": "structure", "required": ["resourceArn"], "members": {"resourceArn": {"location": "uri", "locationName": "resourceArn"}}}, "output": {"type": "structure", "required": ["tags"], "members": {"tags": {"shape": "Sd"}}}}, "PutMetadata": {"http": {"requestUri": "/PutMetadata", "responseCode": 200}, "input": {"type": "structure", "required": ["channelArn", "metadata"], "members": {"channelArn": {}, "metadata": {"type": "string", "sensitive": true}}}}, "StopStream": {"http": {"requestUri": "/StopStream", "responseCode": 200}, "input": {"type": "structure", "required": ["channelArn"], "members": {"channelArn": {}}}, "output": {"type": "structure", "members": {}}}, "TagResource": {"http": {"requestUri": "/tags/{resourceArn}", "responseCode": 200}, "input": {"type": "structure", "required": ["resourceArn", "tags"], "members": {"resourceArn": {"location": "uri", "locationName": "resourceArn"}, "tags": {"shape": "Sd"}}}, "output": {"type": "structure", "members": {}}}, "UntagResource": {"http": {"method": "DELETE", "requestUri": "/tags/{resourceArn}", "responseCode": 200}, "input": {"type": "structure", "required": ["resourceArn", "tagKeys"], "members": {"resourceArn": {"location": "uri", "locationName": "resourceArn"}, "tagKeys": {"location": "querystring", "locationName": "tagKeys", "type": "list", "member": {}}}}, "output": {"type": "structure", "members": {}}, "idempotent": true}, "UpdateChannel": {"http": {"requestUri": "/UpdateChannel", "responseCode": 200}, "input": {"type": "structure", "required": ["arn"], "members": {"arn": {}, "authorized": {"type": "boolean"}, "latencyMode": {}, "name": {}, "recordingConfigurationArn": {}, "type": {}}}, "output": {"type": "structure", "members": {"channel": {"shape": "S6"}}}}}, "shapes": {"S6": {"type": "structure", "members": {"arn": {}, "authorized": {"type": "boolean"}, "ingestEndpoint": {}, "latencyMode": {}, "name": {}, "playbackUrl": {}, "recordingConfigurationArn": {}, "tags": {"shape": "Sd"}, "type": {}}}, "Sd": {"type": "map", "key": {}, "value": {}}, "Sh": {"type": "list", "member": {"type": "structure", "members": {"arn": {}, "code": {}, "message": {}}}}, "Sr": {"type": "structure", "members": {"arn": {}, "channelArn": {}, "tags": {"shape": "Sd"}, "value": {"type": "string", "sensitive": true}}}, "Sx": {"type": "structure", "members": {"s3": {"type": "structure", "required": ["bucketName"], "members": {"bucketName": {}}}}}, "S12": {"type": "structure", "members": {"recordingMode": {}, "targetIntervalSeconds": {"type": "long"}}}, "S16": {"type": "structure", "required": ["arn", "destinationConfiguration", "state"], "members": {"arn": {}, "destinationConfiguration": {"shape": "Sx"}, "name": {}, "recordingReconnectWindowSeconds": {"type": "integer"}, "state": {}, "tags": {"shape": "Sd"}, "thumbnailConfiguration": {"shape": "S12"}}}, "S1l": {"type": "structure", "members": {"arn": {}, "fingerprint": {}, "name": {}, "tags": {"shape": "Sd"}}}, "S1u": {"type": "timestamp", "timestampFormat": "iso8601"}, "S23": {"type": "timestamp", "timestampFormat": "iso8601"}}}