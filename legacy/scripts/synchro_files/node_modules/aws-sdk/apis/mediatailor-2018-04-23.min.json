{"version": "2.0", "metadata": {"apiVersion": "2018-04-23", "endpointPrefix": "api.mediatailor", "jsonVersion": "1.1", "protocol": "rest-json", "serviceAbbreviation": "MediaTailor", "serviceFullName": "AWS MediaTailor", "serviceId": "MediaTailor", "signatureVersion": "v4", "signingName": "mediatailor", "uid": "mediatailor-2018-04-23"}, "operations": {"ConfigureLogsForChannel": {"http": {"method": "PUT", "requestUri": "/configureLogs/channel", "responseCode": 200}, "input": {"type": "structure", "required": ["ChannelName", "LogTypes"], "members": {"ChannelName": {}, "LogTypes": {"shape": "S3"}}}, "output": {"type": "structure", "members": {"ChannelName": {}, "LogTypes": {"shape": "S3"}}}}, "ConfigureLogsForPlaybackConfiguration": {"http": {"method": "PUT", "requestUri": "/configureLogs/playbackConfiguration", "responseCode": 200}, "input": {"type": "structure", "required": ["PercentEnabled", "PlaybackConfigurationName"], "members": {"PercentEnabled": {"type": "integer"}, "PlaybackConfigurationName": {}}}, "output": {"type": "structure", "required": ["PercentEnabled"], "members": {"PercentEnabled": {"type": "integer"}, "PlaybackConfigurationName": {}}}, "idempotent": true}, "CreateChannel": {"http": {"requestUri": "/channel/{ChannelName}", "responseCode": 200}, "input": {"type": "structure", "required": ["ChannelName", "Outputs", "PlaybackMode"], "members": {"ChannelName": {"location": "uri", "locationName": "ChannelName"}, "FillerSlate": {"shape": "Sa"}, "Outputs": {"shape": "Sb"}, "PlaybackMode": {}, "Tags": {"shape": "Sg", "locationName": "tags"}, "Tier": {}}}, "output": {"type": "structure", "members": {"Arn": {}, "ChannelName": {}, "ChannelState": {}, "CreationTime": {"shape": "Sk"}, "FillerSlate": {"shape": "Sa"}, "LastModifiedTime": {"shape": "Sk"}, "Outputs": {"shape": "Sl"}, "PlaybackMode": {}, "Tags": {"shape": "Sg", "locationName": "tags"}, "Tier": {}}}, "idempotent": true}, "CreateLiveSource": {"http": {"requestUri": "/sourceLocation/{SourceLocationName}/liveSource/{LiveSourceName}", "responseCode": 200}, "input": {"type": "structure", "required": ["HttpPackageConfigurations", "LiveSourceName", "SourceLocationName"], "members": {"HttpPackageConfigurations": {"shape": "So"}, "LiveSourceName": {"location": "uri", "locationName": "LiveSourceName"}, "SourceLocationName": {"location": "uri", "locationName": "SourceLocationName"}, "Tags": {"shape": "Sg", "locationName": "tags"}}}, "output": {"type": "structure", "members": {"Arn": {}, "CreationTime": {"shape": "Sk"}, "HttpPackageConfigurations": {"shape": "So"}, "LastModifiedTime": {"shape": "Sk"}, "LiveSourceName": {}, "SourceLocationName": {}, "Tags": {"shape": "Sg", "locationName": "tags"}}}, "idempotent": true}, "CreatePrefetchSchedule": {"http": {"requestUri": "/prefetchSchedule/{PlaybackConfigurationName}/{Name}", "responseCode": 200}, "input": {"type": "structure", "required": ["Consumption", "Name", "PlaybackConfigurationName", "Retrieval"], "members": {"Consumption": {"shape": "St"}, "Name": {"location": "uri", "locationName": "Name"}, "PlaybackConfigurationName": {"location": "uri", "locationName": "PlaybackConfigurationName"}, "Retrieval": {"shape": "Sx"}, "StreamId": {}}}, "output": {"type": "structure", "members": {"Arn": {}, "Consumption": {"shape": "St"}, "Name": {}, "PlaybackConfigurationName": {}, "Retrieval": {"shape": "Sx"}, "StreamId": {}}}, "idempotent": true}, "CreateProgram": {"http": {"requestUri": "/channel/{ChannelName}/program/{ProgramName}", "responseCode": 200}, "input": {"type": "structure", "required": ["ChannelName", "ProgramName", "ScheduleConfiguration", "SourceLocationName"], "members": {"AdBreaks": {"shape": "S10"}, "ChannelName": {"location": "uri", "locationName": "ChannelName"}, "LiveSourceName": {}, "ProgramName": {"location": "uri", "locationName": "ProgramName"}, "ScheduleConfiguration": {"type": "structure", "required": ["Transition"], "members": {"ClipRange": {"shape": "S1b"}, "Transition": {"type": "structure", "required": ["RelativePosition", "Type"], "members": {"DurationMillis": {"type": "long"}, "RelativePosition": {}, "RelativeProgram": {}, "ScheduledStartTimeMillis": {"type": "long"}, "Type": {}}}}}, "SourceLocationName": {}, "VodSourceName": {}}}, "output": {"type": "structure", "members": {"AdBreaks": {"shape": "S10"}, "Arn": {}, "ChannelName": {}, "ClipRange": {"shape": "S1b"}, "CreationTime": {"shape": "Sk"}, "DurationMillis": {"type": "long"}, "LiveSourceName": {}, "ProgramName": {}, "ScheduledStartTime": {"shape": "Sk"}, "SourceLocationName": {}, "VodSourceName": {}}}, "idempotent": true}, "CreateSourceLocation": {"http": {"requestUri": "/sourceLocation/{SourceLocationName}", "responseCode": 200}, "input": {"type": "structure", "required": ["HttpConfiguration", "SourceLocationName"], "members": {"AccessConfiguration": {"shape": "S1g"}, "DefaultSegmentDeliveryConfiguration": {"shape": "S1j"}, "HttpConfiguration": {"shape": "S1k"}, "SegmentDeliveryConfigurations": {"shape": "S1l"}, "SourceLocationName": {"location": "uri", "locationName": "SourceLocationName"}, "Tags": {"shape": "Sg", "locationName": "tags"}}}, "output": {"type": "structure", "members": {"AccessConfiguration": {"shape": "S1g"}, "Arn": {}, "CreationTime": {"shape": "Sk"}, "DefaultSegmentDeliveryConfiguration": {"shape": "S1j"}, "HttpConfiguration": {"shape": "S1k"}, "LastModifiedTime": {"shape": "Sk"}, "SegmentDeliveryConfigurations": {"shape": "S1l"}, "SourceLocationName": {}, "Tags": {"shape": "Sg", "locationName": "tags"}}}, "idempotent": true}, "CreateVodSource": {"http": {"requestUri": "/sourceLocation/{SourceLocationName}/vodSource/{VodSourceName}", "responseCode": 200}, "input": {"type": "structure", "required": ["HttpPackageConfigurations", "SourceLocationName", "VodSourceName"], "members": {"HttpPackageConfigurations": {"shape": "So"}, "SourceLocationName": {"location": "uri", "locationName": "SourceLocationName"}, "Tags": {"shape": "Sg", "locationName": "tags"}, "VodSourceName": {"location": "uri", "locationName": "VodSourceName"}}}, "output": {"type": "structure", "members": {"Arn": {}, "CreationTime": {"shape": "Sk"}, "HttpPackageConfigurations": {"shape": "So"}, "LastModifiedTime": {"shape": "Sk"}, "SourceLocationName": {}, "Tags": {"shape": "Sg", "locationName": "tags"}, "VodSourceName": {}}}, "idempotent": true}, "DeleteChannel": {"http": {"method": "DELETE", "requestUri": "/channel/{ChannelName}", "responseCode": 200}, "input": {"type": "structure", "required": ["ChannelName"], "members": {"ChannelName": {"location": "uri", "locationName": "ChannelName"}}}, "output": {"type": "structure", "members": {}}, "idempotent": true}, "DeleteChannelPolicy": {"http": {"method": "DELETE", "requestUri": "/channel/{ChannelName}/policy", "responseCode": 200}, "input": {"type": "structure", "required": ["ChannelName"], "members": {"ChannelName": {"location": "uri", "locationName": "ChannelName"}}}, "output": {"type": "structure", "members": {}}, "idempotent": true}, "DeleteLiveSource": {"http": {"method": "DELETE", "requestUri": "/sourceLocation/{SourceLocationName}/liveSource/{LiveSourceName}", "responseCode": 200}, "input": {"type": "structure", "required": ["LiveSourceName", "SourceLocationName"], "members": {"LiveSourceName": {"location": "uri", "locationName": "LiveSourceName"}, "SourceLocationName": {"location": "uri", "locationName": "SourceLocationName"}}}, "output": {"type": "structure", "members": {}}, "idempotent": true}, "DeletePlaybackConfiguration": {"http": {"method": "DELETE", "requestUri": "/playbackConfiguration/{Name}", "responseCode": 204}, "input": {"type": "structure", "required": ["Name"], "members": {"Name": {"location": "uri", "locationName": "Name"}}}, "output": {"type": "structure", "members": {}}, "idempotent": true}, "DeletePrefetchSchedule": {"http": {"method": "DELETE", "requestUri": "/prefetchSchedule/{PlaybackConfigurationName}/{Name}", "responseCode": 204}, "input": {"type": "structure", "required": ["Name", "PlaybackConfigurationName"], "members": {"Name": {"location": "uri", "locationName": "Name"}, "PlaybackConfigurationName": {"location": "uri", "locationName": "PlaybackConfigurationName"}}}, "output": {"type": "structure", "members": {}}, "idempotent": true}, "DeleteProgram": {"http": {"method": "DELETE", "requestUri": "/channel/{ChannelName}/program/{ProgramName}", "responseCode": 200}, "input": {"type": "structure", "required": ["ChannelName", "ProgramName"], "members": {"ChannelName": {"location": "uri", "locationName": "ChannelName"}, "ProgramName": {"location": "uri", "locationName": "ProgramName"}}}, "output": {"type": "structure", "members": {}}, "idempotent": true}, "DeleteSourceLocation": {"http": {"method": "DELETE", "requestUri": "/sourceLocation/{SourceLocationName}", "responseCode": 200}, "input": {"type": "structure", "required": ["SourceLocationName"], "members": {"SourceLocationName": {"location": "uri", "locationName": "SourceLocationName"}}}, "output": {"type": "structure", "members": {}}, "idempotent": true}, "DeleteVodSource": {"http": {"method": "DELETE", "requestUri": "/sourceLocation/{SourceLocationName}/vodSource/{VodSourceName}", "responseCode": 200}, "input": {"type": "structure", "required": ["SourceLocationName", "VodSourceName"], "members": {"SourceLocationName": {"location": "uri", "locationName": "SourceLocationName"}, "VodSourceName": {"location": "uri", "locationName": "VodSourceName"}}}, "output": {"type": "structure", "members": {}}, "idempotent": true}, "DescribeChannel": {"http": {"method": "GET", "requestUri": "/channel/{ChannelName}", "responseCode": 200}, "input": {"type": "structure", "required": ["ChannelName"], "members": {"ChannelName": {"location": "uri", "locationName": "ChannelName"}}}, "output": {"type": "structure", "required": ["LogConfiguration"], "members": {"Arn": {}, "ChannelName": {}, "ChannelState": {}, "CreationTime": {"shape": "Sk"}, "FillerSlate": {"shape": "Sa"}, "LastModifiedTime": {"shape": "Sk"}, "LogConfiguration": {"shape": "S28"}, "Outputs": {"shape": "Sl"}, "PlaybackMode": {}, "Tags": {"shape": "Sg", "locationName": "tags"}, "Tier": {}}}}, "DescribeLiveSource": {"http": {"method": "GET", "requestUri": "/sourceLocation/{SourceLocationName}/liveSource/{LiveSourceName}", "responseCode": 200}, "input": {"type": "structure", "required": ["LiveSourceName", "SourceLocationName"], "members": {"LiveSourceName": {"location": "uri", "locationName": "LiveSourceName"}, "SourceLocationName": {"location": "uri", "locationName": "SourceLocationName"}}}, "output": {"type": "structure", "members": {"Arn": {}, "CreationTime": {"shape": "Sk"}, "HttpPackageConfigurations": {"shape": "So"}, "LastModifiedTime": {"shape": "Sk"}, "LiveSourceName": {}, "SourceLocationName": {}, "Tags": {"shape": "Sg", "locationName": "tags"}}}}, "DescribeProgram": {"http": {"method": "GET", "requestUri": "/channel/{ChannelName}/program/{ProgramName}", "responseCode": 200}, "input": {"type": "structure", "required": ["ChannelName", "ProgramName"], "members": {"ChannelName": {"location": "uri", "locationName": "ChannelName"}, "ProgramName": {"location": "uri", "locationName": "ProgramName"}}}, "output": {"type": "structure", "members": {"AdBreaks": {"shape": "S10"}, "Arn": {}, "ChannelName": {}, "ClipRange": {"shape": "S1b"}, "CreationTime": {"shape": "Sk"}, "DurationMillis": {"type": "long"}, "LiveSourceName": {}, "ProgramName": {}, "ScheduledStartTime": {"shape": "Sk"}, "SourceLocationName": {}, "VodSourceName": {}}}}, "DescribeSourceLocation": {"http": {"method": "GET", "requestUri": "/sourceLocation/{SourceLocationName}", "responseCode": 200}, "input": {"type": "structure", "required": ["SourceLocationName"], "members": {"SourceLocationName": {"location": "uri", "locationName": "SourceLocationName"}}}, "output": {"type": "structure", "members": {"AccessConfiguration": {"shape": "S1g"}, "Arn": {}, "CreationTime": {"shape": "Sk"}, "DefaultSegmentDeliveryConfiguration": {"shape": "S1j"}, "HttpConfiguration": {"shape": "S1k"}, "LastModifiedTime": {"shape": "Sk"}, "SegmentDeliveryConfigurations": {"shape": "S1l"}, "SourceLocationName": {}, "Tags": {"shape": "Sg", "locationName": "tags"}}}}, "DescribeVodSource": {"http": {"method": "GET", "requestUri": "/sourceLocation/{SourceLocationName}/vodSource/{VodSourceName}", "responseCode": 200}, "input": {"type": "structure", "required": ["SourceLocationName", "VodSourceName"], "members": {"SourceLocationName": {"location": "uri", "locationName": "SourceLocationName"}, "VodSourceName": {"location": "uri", "locationName": "VodSourceName"}}}, "output": {"type": "structure", "members": {"Arn": {}, "CreationTime": {"shape": "Sk"}, "HttpPackageConfigurations": {"shape": "So"}, "LastModifiedTime": {"shape": "Sk"}, "SourceLocationName": {}, "Tags": {"shape": "Sg", "locationName": "tags"}, "VodSourceName": {}}}}, "GetChannelPolicy": {"http": {"method": "GET", "requestUri": "/channel/{ChannelName}/policy", "responseCode": 200}, "input": {"type": "structure", "required": ["ChannelName"], "members": {"ChannelName": {"location": "uri", "locationName": "ChannelName"}}}, "output": {"type": "structure", "members": {"Policy": {}}}}, "GetChannelSchedule": {"http": {"method": "GET", "requestUri": "/channel/{ChannelName}/schedule", "responseCode": 200}, "input": {"type": "structure", "required": ["ChannelName"], "members": {"ChannelName": {"location": "uri", "locationName": "ChannelName"}, "DurationMinutes": {"location": "querystring", "locationName": "durationMinutes"}, "MaxResults": {"location": "querystring", "locationName": "maxResults", "type": "integer"}, "NextToken": {"location": "querystring", "locationName": "nextToken"}}}, "output": {"type": "structure", "members": {"Items": {"type": "list", "member": {"type": "structure", "required": ["<PERSON><PERSON>", "ChannelName", "ProgramName", "SourceLocationName"], "members": {"ApproximateDurationSeconds": {"type": "long"}, "ApproximateStartTime": {"shape": "Sk"}, "Arn": {}, "ChannelName": {}, "LiveSourceName": {}, "ProgramName": {}, "ScheduleAdBreaks": {"type": "list", "member": {"type": "structure", "members": {"ApproximateDurationSeconds": {"type": "long"}, "ApproximateStartTime": {"shape": "Sk"}, "SourceLocationName": {}, "VodSourceName": {}}}}, "ScheduleEntryType": {}, "SourceLocationName": {}, "VodSourceName": {}}}}, "NextToken": {}}}}, "GetPlaybackConfiguration": {"http": {"method": "GET", "requestUri": "/playbackConfiguration/{Name}", "responseCode": 200}, "input": {"type": "structure", "required": ["Name"], "members": {"Name": {"location": "uri", "locationName": "Name"}}}, "output": {"type": "structure", "members": {"AdDecisionServerUrl": {}, "AvailSuppression": {"shape": "S2u"}, "Bumper": {"shape": "S2w"}, "CdnConfiguration": {"shape": "S2x"}, "ConfigurationAliases": {"shape": "S2y"}, "DashConfiguration": {"shape": "S2z"}, "HlsConfiguration": {"shape": "S31"}, "LivePreRollConfiguration": {"shape": "S32"}, "LogConfiguration": {"shape": "S33"}, "ManifestProcessingRules": {"shape": "S34"}, "Name": {}, "PersonalizationThresholdSeconds": {"type": "integer"}, "PlaybackConfigurationArn": {}, "PlaybackEndpointPrefix": {}, "SessionInitializationEndpointPrefix": {}, "SlateAdUrl": {}, "Tags": {"shape": "Sg", "locationName": "tags"}, "TranscodeProfileName": {}, "VideoContentSourceUrl": {}}}}, "GetPrefetchSchedule": {"http": {"method": "GET", "requestUri": "/prefetchSchedule/{PlaybackConfigurationName}/{Name}", "responseCode": 200}, "input": {"type": "structure", "required": ["Name", "PlaybackConfigurationName"], "members": {"Name": {"location": "uri", "locationName": "Name"}, "PlaybackConfigurationName": {"location": "uri", "locationName": "PlaybackConfigurationName"}}}, "output": {"type": "structure", "members": {"Arn": {}, "Consumption": {"shape": "St"}, "Name": {}, "PlaybackConfigurationName": {}, "Retrieval": {"shape": "Sx"}, "StreamId": {}}}}, "ListAlerts": {"http": {"method": "GET", "requestUri": "/alerts", "responseCode": 200}, "input": {"type": "structure", "required": ["ResourceArn"], "members": {"MaxResults": {"location": "querystring", "locationName": "maxResults", "type": "integer"}, "NextToken": {"location": "querystring", "locationName": "nextToken"}, "ResourceArn": {"location": "querystring", "locationName": "resourceArn"}}}, "output": {"type": "structure", "members": {"Items": {"type": "list", "member": {"type": "structure", "required": ["AlertCode", "AlertM<PERSON>age", "LastModifiedTime", "RelatedResourceArns", "ResourceArn"], "members": {"AlertCode": {}, "AlertMessage": {}, "LastModifiedTime": {"shape": "Sk"}, "RelatedResourceArns": {"shape": "S3e"}, "ResourceArn": {}}}}, "NextToken": {}}}}, "ListChannels": {"http": {"method": "GET", "requestUri": "/channels", "responseCode": 200}, "input": {"type": "structure", "members": {"MaxResults": {"location": "querystring", "locationName": "maxResults", "type": "integer"}, "NextToken": {"location": "querystring", "locationName": "nextToken"}}}, "output": {"type": "structure", "members": {"Items": {"type": "list", "member": {"type": "structure", "required": ["<PERSON><PERSON>", "ChannelName", "ChannelState", "LogConfiguration", "Outputs", "PlaybackMode", "Tier"], "members": {"Arn": {}, "ChannelName": {}, "ChannelState": {}, "CreationTime": {"shape": "Sk"}, "FillerSlate": {"shape": "Sa"}, "LastModifiedTime": {"shape": "Sk"}, "LogConfiguration": {"shape": "S28"}, "Outputs": {"shape": "Sl"}, "PlaybackMode": {}, "Tags": {"shape": "Sg", "locationName": "tags"}, "Tier": {}}}}, "NextToken": {}}}}, "ListLiveSources": {"http": {"method": "GET", "requestUri": "/sourceLocation/{SourceLocationName}/liveSources", "responseCode": 200}, "input": {"type": "structure", "required": ["SourceLocationName"], "members": {"MaxResults": {"location": "querystring", "locationName": "maxResults", "type": "integer"}, "NextToken": {"location": "querystring", "locationName": "nextToken"}, "SourceLocationName": {"location": "uri", "locationName": "SourceLocationName"}}}, "output": {"type": "structure", "members": {"Items": {"type": "list", "member": {"type": "structure", "required": ["<PERSON><PERSON>", "HttpPackageConfigurations", "LiveSourceName", "SourceLocationName"], "members": {"Arn": {}, "CreationTime": {"shape": "Sk"}, "HttpPackageConfigurations": {"shape": "So"}, "LastModifiedTime": {"shape": "Sk"}, "LiveSourceName": {}, "SourceLocationName": {}, "Tags": {"shape": "Sg", "locationName": "tags"}}}}, "NextToken": {}}}}, "ListPlaybackConfigurations": {"http": {"method": "GET", "requestUri": "/playbackConfigurations", "responseCode": 200}, "input": {"type": "structure", "members": {"MaxResults": {"location": "querystring", "locationName": "MaxResults", "type": "integer"}, "NextToken": {"location": "querystring", "locationName": "NextToken"}}}, "output": {"type": "structure", "members": {"Items": {"type": "list", "member": {"type": "structure", "members": {"AdDecisionServerUrl": {}, "AvailSuppression": {"shape": "S2u"}, "Bumper": {"shape": "S2w"}, "CdnConfiguration": {"shape": "S2x"}, "ConfigurationAliases": {"shape": "S2y"}, "DashConfiguration": {"shape": "S2z"}, "HlsConfiguration": {"shape": "S31"}, "LivePreRollConfiguration": {"shape": "S32"}, "LogConfiguration": {"shape": "S33"}, "ManifestProcessingRules": {"shape": "S34"}, "Name": {}, "PersonalizationThresholdSeconds": {"type": "integer"}, "PlaybackConfigurationArn": {}, "PlaybackEndpointPrefix": {}, "SessionInitializationEndpointPrefix": {}, "SlateAdUrl": {}, "Tags": {"shape": "Sg", "locationName": "tags"}, "TranscodeProfileName": {}, "VideoContentSourceUrl": {}}}}, "NextToken": {}}}}, "ListPrefetchSchedules": {"http": {"requestUri": "/prefetchSchedule/{PlaybackConfigurationName}", "responseCode": 200}, "input": {"type": "structure", "required": ["PlaybackConfigurationName"], "members": {"MaxResults": {"type": "integer"}, "NextToken": {}, "PlaybackConfigurationName": {"location": "uri", "locationName": "PlaybackConfigurationName"}, "StreamId": {}}}, "output": {"type": "structure", "members": {"Items": {"type": "list", "member": {"type": "structure", "required": ["<PERSON><PERSON>", "Consumption", "Name", "PlaybackConfigurationName", "Retrieval"], "members": {"Arn": {}, "Consumption": {"shape": "St"}, "Name": {}, "PlaybackConfigurationName": {}, "Retrieval": {"shape": "Sx"}, "StreamId": {}}}}, "NextToken": {}}}}, "ListSourceLocations": {"http": {"method": "GET", "requestUri": "/sourceLocations", "responseCode": 200}, "input": {"type": "structure", "members": {"MaxResults": {"location": "querystring", "locationName": "maxResults", "type": "integer"}, "NextToken": {"location": "querystring", "locationName": "nextToken"}}}, "output": {"type": "structure", "members": {"Items": {"type": "list", "member": {"type": "structure", "required": ["<PERSON><PERSON>", "HttpConfiguration", "SourceLocationName"], "members": {"AccessConfiguration": {"shape": "S1g"}, "Arn": {}, "CreationTime": {"shape": "Sk"}, "DefaultSegmentDeliveryConfiguration": {"shape": "S1j"}, "HttpConfiguration": {"shape": "S1k"}, "LastModifiedTime": {"shape": "Sk"}, "SegmentDeliveryConfigurations": {"shape": "S1l"}, "SourceLocationName": {}, "Tags": {"shape": "Sg", "locationName": "tags"}}}}, "NextToken": {}}}}, "ListTagsForResource": {"http": {"method": "GET", "requestUri": "/tags/{ResourceArn}", "responseCode": 200}, "input": {"type": "structure", "required": ["ResourceArn"], "members": {"ResourceArn": {"location": "uri", "locationName": "ResourceArn"}}}, "output": {"type": "structure", "members": {"Tags": {"shape": "Sg", "locationName": "tags"}}}}, "ListVodSources": {"http": {"method": "GET", "requestUri": "/sourceLocation/{SourceLocationName}/vodSources", "responseCode": 200}, "input": {"type": "structure", "required": ["SourceLocationName"], "members": {"MaxResults": {"location": "querystring", "locationName": "maxResults", "type": "integer"}, "NextToken": {"location": "querystring", "locationName": "nextToken"}, "SourceLocationName": {"location": "uri", "locationName": "SourceLocationName"}}}, "output": {"type": "structure", "members": {"Items": {"type": "list", "member": {"type": "structure", "required": ["<PERSON><PERSON>", "HttpPackageConfigurations", "SourceLocationName", "VodSourceName"], "members": {"Arn": {}, "CreationTime": {"shape": "Sk"}, "HttpPackageConfigurations": {"shape": "So"}, "LastModifiedTime": {"shape": "Sk"}, "SourceLocationName": {}, "Tags": {"shape": "Sg", "locationName": "tags"}, "VodSourceName": {}}}}, "NextToken": {}}}}, "PutChannelPolicy": {"http": {"method": "PUT", "requestUri": "/channel/{ChannelName}/policy", "responseCode": 200}, "input": {"type": "structure", "required": ["ChannelName", "Policy"], "members": {"ChannelName": {"location": "uri", "locationName": "ChannelName"}, "Policy": {}}}, "output": {"type": "structure", "members": {}}, "idempotent": true}, "PutPlaybackConfiguration": {"http": {"method": "PUT", "requestUri": "/playbackConfiguration", "responseCode": 200}, "input": {"type": "structure", "required": ["Name"], "members": {"AdDecisionServerUrl": {}, "AvailSuppression": {"shape": "S2u"}, "Bumper": {"shape": "S2w"}, "CdnConfiguration": {"shape": "S2x"}, "ConfigurationAliases": {"type": "map", "key": {}, "value": {"shape": "Sg"}}, "DashConfiguration": {"type": "structure", "members": {"MpdLocation": {}, "OriginManifestType": {}}}, "LivePreRollConfiguration": {"shape": "S32"}, "ManifestProcessingRules": {"shape": "S34"}, "Name": {}, "PersonalizationThresholdSeconds": {"type": "integer"}, "SlateAdUrl": {}, "Tags": {"shape": "Sg", "locationName": "tags"}, "TranscodeProfileName": {}, "VideoContentSourceUrl": {}}}, "output": {"type": "structure", "members": {"AdDecisionServerUrl": {}, "AvailSuppression": {"shape": "S2u"}, "Bumper": {"shape": "S2w"}, "CdnConfiguration": {"shape": "S2x"}, "ConfigurationAliases": {"shape": "S2y"}, "DashConfiguration": {"shape": "S2z"}, "HlsConfiguration": {"shape": "S31"}, "LivePreRollConfiguration": {"shape": "S32"}, "LogConfiguration": {"shape": "S33"}, "ManifestProcessingRules": {"shape": "S34"}, "Name": {}, "PersonalizationThresholdSeconds": {"type": "integer"}, "PlaybackConfigurationArn": {}, "PlaybackEndpointPrefix": {}, "SessionInitializationEndpointPrefix": {}, "SlateAdUrl": {}, "Tags": {"shape": "Sg", "locationName": "tags"}, "TranscodeProfileName": {}, "VideoContentSourceUrl": {}}}, "idempotent": true}, "StartChannel": {"http": {"method": "PUT", "requestUri": "/channel/{ChannelName}/start", "responseCode": 200}, "input": {"type": "structure", "required": ["ChannelName"], "members": {"ChannelName": {"location": "uri", "locationName": "ChannelName"}}}, "output": {"type": "structure", "members": {}}, "idempotent": true}, "StopChannel": {"http": {"method": "PUT", "requestUri": "/channel/{ChannelName}/stop", "responseCode": 200}, "input": {"type": "structure", "required": ["ChannelName"], "members": {"ChannelName": {"location": "uri", "locationName": "ChannelName"}}}, "output": {"type": "structure", "members": {}}, "idempotent": true}, "TagResource": {"http": {"requestUri": "/tags/{ResourceArn}", "responseCode": 204}, "input": {"type": "structure", "required": ["ResourceArn", "Tags"], "members": {"ResourceArn": {"location": "uri", "locationName": "ResourceArn"}, "Tags": {"shape": "Sg", "locationName": "tags"}}}}, "UntagResource": {"http": {"method": "DELETE", "requestUri": "/tags/{ResourceArn}", "responseCode": 204}, "input": {"type": "structure", "required": ["ResourceArn", "TagKeys"], "members": {"ResourceArn": {"location": "uri", "locationName": "ResourceArn"}, "TagKeys": {"shape": "S3e", "location": "querystring", "locationName": "tagKeys"}}}, "idempotent": true}, "UpdateChannel": {"http": {"method": "PUT", "requestUri": "/channel/{ChannelName}", "responseCode": 200}, "input": {"type": "structure", "required": ["ChannelName", "Outputs"], "members": {"ChannelName": {"location": "uri", "locationName": "ChannelName"}, "FillerSlate": {"shape": "Sa"}, "Outputs": {"shape": "Sb"}}}, "output": {"type": "structure", "members": {"Arn": {}, "ChannelName": {}, "ChannelState": {}, "CreationTime": {"shape": "Sk"}, "FillerSlate": {"shape": "Sa"}, "LastModifiedTime": {"shape": "Sk"}, "Outputs": {"shape": "Sl"}, "PlaybackMode": {}, "Tags": {"shape": "Sg", "locationName": "tags"}, "Tier": {}}}, "idempotent": true}, "UpdateLiveSource": {"http": {"method": "PUT", "requestUri": "/sourceLocation/{SourceLocationName}/liveSource/{LiveSourceName}", "responseCode": 200}, "input": {"type": "structure", "required": ["HttpPackageConfigurations", "LiveSourceName", "SourceLocationName"], "members": {"HttpPackageConfigurations": {"shape": "So"}, "LiveSourceName": {"location": "uri", "locationName": "LiveSourceName"}, "SourceLocationName": {"location": "uri", "locationName": "SourceLocationName"}}}, "output": {"type": "structure", "members": {"Arn": {}, "CreationTime": {"shape": "Sk"}, "HttpPackageConfigurations": {"shape": "So"}, "LastModifiedTime": {"shape": "Sk"}, "LiveSourceName": {}, "SourceLocationName": {}, "Tags": {"shape": "Sg", "locationName": "tags"}}}, "idempotent": true}, "UpdateProgram": {"http": {"method": "PUT", "requestUri": "/channel/{ChannelName}/program/{ProgramName}", "responseCode": 200}, "input": {"type": "structure", "required": ["ChannelName", "ProgramName", "ScheduleConfiguration"], "members": {"AdBreaks": {"shape": "S10"}, "ChannelName": {"location": "uri", "locationName": "ChannelName"}, "ProgramName": {"location": "uri", "locationName": "ProgramName"}, "ScheduleConfiguration": {"type": "structure", "members": {"ClipRange": {"shape": "S1b"}, "Transition": {"type": "structure", "members": {"DurationMillis": {"type": "long"}, "ScheduledStartTimeMillis": {"type": "long"}}}}}}}, "output": {"type": "structure", "members": {"AdBreaks": {"shape": "S10"}, "Arn": {}, "ChannelName": {}, "ClipRange": {"shape": "S1b"}, "CreationTime": {"shape": "Sk"}, "DurationMillis": {"type": "long"}, "LiveSourceName": {}, "ProgramName": {}, "ScheduledStartTime": {"shape": "Sk"}, "SourceLocationName": {}, "VodSourceName": {}}}, "idempotent": true}, "UpdateSourceLocation": {"http": {"method": "PUT", "requestUri": "/sourceLocation/{SourceLocationName}", "responseCode": 200}, "input": {"type": "structure", "required": ["HttpConfiguration", "SourceLocationName"], "members": {"AccessConfiguration": {"shape": "S1g"}, "DefaultSegmentDeliveryConfiguration": {"shape": "S1j"}, "HttpConfiguration": {"shape": "S1k"}, "SegmentDeliveryConfigurations": {"shape": "S1l"}, "SourceLocationName": {"location": "uri", "locationName": "SourceLocationName"}}}, "output": {"type": "structure", "members": {"AccessConfiguration": {"shape": "S1g"}, "Arn": {}, "CreationTime": {"shape": "Sk"}, "DefaultSegmentDeliveryConfiguration": {"shape": "S1j"}, "HttpConfiguration": {"shape": "S1k"}, "LastModifiedTime": {"shape": "Sk"}, "SegmentDeliveryConfigurations": {"shape": "S1l"}, "SourceLocationName": {}, "Tags": {"shape": "Sg", "locationName": "tags"}}}, "idempotent": true}, "UpdateVodSource": {"http": {"method": "PUT", "requestUri": "/sourceLocation/{SourceLocationName}/vodSource/{VodSourceName}", "responseCode": 200}, "input": {"type": "structure", "required": ["HttpPackageConfigurations", "SourceLocationName", "VodSourceName"], "members": {"HttpPackageConfigurations": {"shape": "So"}, "SourceLocationName": {"location": "uri", "locationName": "SourceLocationName"}, "VodSourceName": {"location": "uri", "locationName": "VodSourceName"}}}, "output": {"type": "structure", "members": {"Arn": {}, "CreationTime": {"shape": "Sk"}, "HttpPackageConfigurations": {"shape": "So"}, "LastModifiedTime": {"shape": "Sk"}, "SourceLocationName": {}, "Tags": {"shape": "Sg", "locationName": "tags"}, "VodSourceName": {}}}, "idempotent": true}}, "shapes": {"S3": {"type": "list", "member": {}}, "Sa": {"type": "structure", "members": {"SourceLocationName": {}, "VodSourceName": {}}}, "Sb": {"type": "list", "member": {"type": "structure", "required": ["ManifestName", "SourceGroup"], "members": {"DashPlaylistSettings": {"shape": "Sd"}, "HlsPlaylistSettings": {"shape": "Se"}, "ManifestName": {}, "SourceGroup": {}}}}, "Sd": {"type": "structure", "members": {"ManifestWindowSeconds": {"type": "integer"}, "MinBufferTimeSeconds": {"type": "integer"}, "MinUpdatePeriodSeconds": {"type": "integer"}, "SuggestedPresentationDelaySeconds": {"type": "integer"}}}, "Se": {"type": "structure", "members": {"ManifestWindowSeconds": {"type": "integer"}}}, "Sg": {"type": "map", "key": {}, "value": {}}, "Sk": {"type": "timestamp", "timestampFormat": "unixTimestamp"}, "Sl": {"type": "list", "member": {"type": "structure", "required": ["ManifestName", "PlaybackUrl", "SourceGroup"], "members": {"DashPlaylistSettings": {"shape": "Sd"}, "HlsPlaylistSettings": {"shape": "Se"}, "ManifestName": {}, "PlaybackUrl": {}, "SourceGroup": {}}}}, "So": {"type": "list", "member": {"type": "structure", "required": ["Path", "SourceGroup", "Type"], "members": {"Path": {}, "SourceGroup": {}, "Type": {}}}}, "St": {"type": "structure", "required": ["EndTime"], "members": {"AvailMatchingCriteria": {"type": "list", "member": {"type": "structure", "required": ["DynamicVariable", "Operator"], "members": {"DynamicVariable": {}, "Operator": {}}}}, "EndTime": {"shape": "Sk"}, "StartTime": {"shape": "Sk"}}}, "Sx": {"type": "structure", "required": ["EndTime"], "members": {"DynamicVariables": {"shape": "Sg"}, "EndTime": {"shape": "Sk"}, "StartTime": {"shape": "Sk"}}}, "S10": {"type": "list", "member": {"type": "structure", "members": {"MessageType": {}, "OffsetMillis": {"type": "long"}, "Slate": {"shape": "Sa"}, "SpliceInsertMessage": {"type": "structure", "members": {"AvailNum": {"type": "integer"}, "AvailsExpected": {"type": "integer"}, "SpliceEventId": {"type": "integer"}, "UniqueProgramId": {"type": "integer"}}}, "TimeSignalMessage": {"type": "structure", "members": {"SegmentationDescriptors": {"type": "list", "member": {"type": "structure", "members": {"SegmentNum": {"type": "integer"}, "SegmentationEventId": {"type": "integer"}, "SegmentationTypeId": {"type": "integer"}, "SegmentationUpid": {}, "SegmentationUpidType": {"type": "integer"}, "SegmentsExpected": {"type": "integer"}, "SubSegmentNum": {"type": "integer"}, "SubSegmentsExpected": {"type": "integer"}}}}}}}}}, "S1b": {"type": "structure", "required": ["EndOffsetMillis"], "members": {"EndOffsetMillis": {"type": "long"}}}, "S1g": {"type": "structure", "members": {"AccessType": {}, "SecretsManagerAccessTokenConfiguration": {"type": "structure", "members": {"HeaderName": {}, "SecretArn": {}, "SecretStringKey": {}}}}}, "S1j": {"type": "structure", "members": {"BaseUrl": {}}}, "S1k": {"type": "structure", "required": ["BaseUrl"], "members": {"BaseUrl": {}}}, "S1l": {"type": "list", "member": {"type": "structure", "members": {"BaseUrl": {}, "Name": {}}}}, "S28": {"type": "structure", "members": {"LogTypes": {"shape": "S3"}}}, "S2u": {"type": "structure", "members": {"Mode": {}, "Value": {}}}, "S2w": {"type": "structure", "members": {"EndUrl": {}, "StartUrl": {}}}, "S2x": {"type": "structure", "members": {"AdSegmentUrlPrefix": {}, "ContentSegmentUrlPrefix": {}}}, "S2y": {"type": "map", "key": {}, "value": {"shape": "Sg"}}, "S2z": {"type": "structure", "members": {"ManifestEndpointPrefix": {}, "MpdLocation": {}, "OriginManifestType": {}}}, "S31": {"type": "structure", "members": {"ManifestEndpointPrefix": {}}}, "S32": {"type": "structure", "members": {"AdDecisionServerUrl": {}, "MaxDurationSeconds": {"type": "integer"}}}, "S33": {"type": "structure", "required": ["PercentEnabled"], "members": {"PercentEnabled": {"type": "integer"}}}, "S34": {"type": "structure", "members": {"AdMarkerPassthrough": {"type": "structure", "members": {"Enabled": {"type": "boolean"}}}}}, "S3e": {"type": "list", "member": {}}}}