{"version": "2.0", "metadata": {"apiVersion": "2021-06-17", "endpointPrefix": "iotfleetwise", "jsonVersion": "1.0", "protocol": "json", "serviceFullName": "AWS IoT FleetWise", "serviceId": "IoTFleetWise", "signatureVersion": "v4", "signingName": "iotfleetwise", "targetPrefix": "IoTAutobahnControlPlane", "uid": "iotfleetwise-2021-06-17"}, "operations": {"AssociateVehicleFleet": {"input": {"type": "structure", "required": ["vehicleName", "fleetId"], "members": {"vehicleName": {}, "fleetId": {}}}, "output": {"type": "structure", "members": {}}}, "BatchCreateVehicle": {"input": {"type": "structure", "required": ["vehicles"], "members": {"vehicles": {"type": "list", "member": {"type": "structure", "required": ["vehicleName", "modelManifestArn", "decoderManifestArn"], "members": {"vehicleName": {}, "modelManifestArn": {}, "decoderManifestArn": {}, "attributes": {"shape": "S9"}, "associationBehavior": {}, "tags": {"shape": "Sd"}}}}}}, "output": {"type": "structure", "members": {"vehicles": {"type": "list", "member": {"type": "structure", "members": {"vehicleName": {}, "arn": {}, "thingArn": {}}}}, "errors": {"type": "list", "member": {"type": "structure", "members": {"vehicleName": {}, "code": {}, "message": {}}}}}}}, "BatchUpdateVehicle": {"input": {"type": "structure", "required": ["vehicles"], "members": {"vehicles": {"type": "list", "member": {"type": "structure", "required": ["vehicleName"], "members": {"vehicleName": {}, "modelManifestArn": {}, "decoderManifestArn": {}, "attributes": {"shape": "S9"}, "attributeUpdateMode": {}}}}}}, "output": {"type": "structure", "members": {"vehicles": {"type": "list", "member": {"type": "structure", "members": {"vehicleName": {}, "arn": {}}}}, "errors": {"type": "list", "member": {"type": "structure", "members": {"vehicleName": {}, "code": {"type": "integer"}, "message": {}}}}}}}, "CreateCampaign": {"input": {"type": "structure", "required": ["name", "signalCatalogArn", "targetArn", "collectionScheme"], "members": {"name": {}, "description": {}, "signalCatalogArn": {}, "targetArn": {}, "startTime": {"type": "timestamp"}, "expiryTime": {"type": "timestamp"}, "postTriggerCollectionDuration": {"type": "long"}, "diagnosticsMode": {}, "spoolingMode": {}, "compression": {}, "priority": {"type": "integer"}, "signalsToCollect": {"shape": "S16"}, "collectionScheme": {"shape": "S1a"}, "dataExtraDimensions": {"shape": "S1h"}, "tags": {"shape": "Sd"}}}, "output": {"type": "structure", "members": {"name": {}, "arn": {}}}, "idempotent": true}, "CreateDecoderManifest": {"input": {"type": "structure", "required": ["name", "modelManifestArn"], "members": {"name": {}, "description": {}, "modelManifestArn": {}, "signalDecoders": {"shape": "S1m"}, "networkInterfaces": {"shape": "S20"}, "tags": {"shape": "Sd"}}}, "output": {"type": "structure", "required": ["name", "arn"], "members": {"name": {}, "arn": {}}}, "idempotent": true}, "CreateFleet": {"input": {"type": "structure", "required": ["fleetId", "signalCatalogArn"], "members": {"fleetId": {}, "description": {}, "signalCatalogArn": {}, "tags": {"shape": "Sd"}}}, "output": {"type": "structure", "required": ["id", "arn"], "members": {"id": {}, "arn": {}}}, "idempotent": true}, "CreateModelManifest": {"input": {"type": "structure", "required": ["name", "nodes", "signalCatalogArn"], "members": {"name": {}, "description": {}, "nodes": {"shape": "S2e"}, "signalCatalogArn": {}, "tags": {"shape": "Sd"}}}, "output": {"type": "structure", "required": ["name", "arn"], "members": {"name": {}, "arn": {}}}, "idempotent": true}, "CreateSignalCatalog": {"input": {"type": "structure", "required": ["name"], "members": {"name": {}, "description": {}, "nodes": {"shape": "S2h"}, "tags": {"shape": "Sd"}}}, "output": {"type": "structure", "required": ["name", "arn"], "members": {"name": {}, "arn": {}}}, "idempotent": true}, "CreateVehicle": {"input": {"type": "structure", "required": ["vehicleName", "modelManifestArn", "decoderManifestArn"], "members": {"vehicleName": {}, "modelManifestArn": {}, "decoderManifestArn": {}, "attributes": {"shape": "S9"}, "associationBehavior": {}, "tags": {"shape": "Sd"}}}, "output": {"type": "structure", "members": {"vehicleName": {}, "arn": {}, "thingArn": {}}}, "idempotent": true}, "DeleteCampaign": {"input": {"type": "structure", "required": ["name"], "members": {"name": {}}}, "output": {"type": "structure", "members": {"name": {}, "arn": {}}}, "idempotent": true}, "DeleteDecoderManifest": {"input": {"type": "structure", "required": ["name"], "members": {"name": {}}}, "output": {"type": "structure", "required": ["name", "arn"], "members": {"name": {}, "arn": {}}}, "idempotent": true}, "DeleteFleet": {"input": {"type": "structure", "required": ["fleetId"], "members": {"fleetId": {}}}, "output": {"type": "structure", "members": {"id": {}, "arn": {}}}, "idempotent": true}, "DeleteModelManifest": {"input": {"type": "structure", "required": ["name"], "members": {"name": {}}}, "output": {"type": "structure", "required": ["name", "arn"], "members": {"name": {}, "arn": {}}}, "idempotent": true}, "DeleteSignalCatalog": {"input": {"type": "structure", "required": ["name"], "members": {"name": {}}}, "output": {"type": "structure", "required": ["name", "arn"], "members": {"name": {}, "arn": {}}}, "idempotent": true}, "DeleteVehicle": {"input": {"type": "structure", "required": ["vehicleName"], "members": {"vehicleName": {}}}, "output": {"type": "structure", "required": ["vehicleName", "arn"], "members": {"vehicleName": {}, "arn": {}}}, "idempotent": true}, "DisassociateVehicleFleet": {"input": {"type": "structure", "required": ["vehicleName", "fleetId"], "members": {"vehicleName": {}, "fleetId": {}}}, "output": {"type": "structure", "members": {}}}, "GetCampaign": {"input": {"type": "structure", "required": ["name"], "members": {"name": {}}}, "output": {"type": "structure", "members": {"name": {}, "arn": {}, "description": {}, "signalCatalogArn": {}, "targetArn": {}, "status": {}, "startTime": {"type": "timestamp"}, "expiryTime": {"type": "timestamp"}, "postTriggerCollectionDuration": {"type": "long"}, "diagnosticsMode": {}, "spoolingMode": {}, "compression": {}, "priority": {"type": "integer"}, "signalsToCollect": {"shape": "S16"}, "collectionScheme": {"shape": "S1a"}, "dataExtraDimensions": {"shape": "S1h"}, "creationTime": {"type": "timestamp"}, "lastModificationTime": {"type": "timestamp"}}}}, "GetDecoderManifest": {"input": {"type": "structure", "required": ["name"], "members": {"name": {}}}, "output": {"type": "structure", "required": ["name", "arn", "creationTime", "lastModificationTime"], "members": {"name": {}, "arn": {}, "description": {}, "modelManifestArn": {}, "status": {}, "creationTime": {"type": "timestamp"}, "lastModificationTime": {"type": "timestamp"}}}}, "GetFleet": {"input": {"type": "structure", "required": ["fleetId"], "members": {"fleetId": {}}}, "output": {"type": "structure", "required": ["id", "arn", "signalCatalogArn", "creationTime", "lastModificationTime"], "members": {"id": {}, "arn": {}, "description": {}, "signalCatalogArn": {}, "creationTime": {"type": "timestamp"}, "lastModificationTime": {"type": "timestamp"}}}}, "GetLoggingOptions": {"input": {"type": "structure", "members": {}}, "output": {"type": "structure", "required": ["cloudWatchLogDelivery"], "members": {"cloudWatchLogDelivery": {"shape": "S3f"}}}}, "GetModelManifest": {"input": {"type": "structure", "required": ["name"], "members": {"name": {}}}, "output": {"type": "structure", "required": ["name", "arn", "creationTime", "lastModificationTime"], "members": {"name": {}, "arn": {}, "description": {}, "signalCatalogArn": {}, "status": {}, "creationTime": {"type": "timestamp"}, "lastModificationTime": {"type": "timestamp"}}}}, "GetRegisterAccountStatus": {"input": {"type": "structure", "members": {}}, "output": {"type": "structure", "required": ["customerAccountId", "accountStatus", "timestreamRegistrationResponse", "iamRegistrationResponse", "creationTime", "lastModificationTime"], "members": {"customerAccountId": {}, "accountStatus": {}, "timestreamRegistrationResponse": {"type": "structure", "required": ["timestreamDatabaseName", "timestreamTableName", "registrationStatus"], "members": {"timestreamDatabaseName": {}, "timestreamTableName": {}, "timestreamDatabaseArn": {}, "timestreamTableArn": {}, "registrationStatus": {}, "errorMessage": {}}}, "iamRegistrationResponse": {"type": "structure", "required": ["roleArn", "registrationStatus"], "members": {"roleArn": {}, "registrationStatus": {}, "errorMessage": {}}}, "creationTime": {"type": "timestamp"}, "lastModificationTime": {"type": "timestamp"}}}}, "GetSignalCatalog": {"input": {"type": "structure", "required": ["name"], "members": {"name": {}}}, "output": {"type": "structure", "required": ["name", "arn", "creationTime", "lastModificationTime"], "members": {"name": {}, "arn": {}, "description": {}, "nodeCounts": {"type": "structure", "members": {"totalNodes": {"type": "integer"}, "totalBranches": {"type": "integer"}, "totalSensors": {"type": "integer"}, "totalAttributes": {"type": "integer"}, "totalActuators": {"type": "integer"}}}, "creationTime": {"type": "timestamp"}, "lastModificationTime": {"type": "timestamp"}}}}, "GetVehicle": {"input": {"type": "structure", "required": ["vehicleName"], "members": {"vehicleName": {}}}, "output": {"type": "structure", "members": {"vehicleName": {}, "arn": {}, "modelManifestArn": {}, "decoderManifestArn": {}, "attributes": {"shape": "S9"}, "creationTime": {"type": "timestamp"}, "lastModificationTime": {"type": "timestamp"}}}}, "GetVehicleStatus": {"input": {"type": "structure", "required": ["vehicleName"], "members": {"nextToken": {}, "maxResults": {"type": "integer"}, "vehicleName": {}}}, "output": {"type": "structure", "members": {"campaigns": {"type": "list", "member": {"type": "structure", "members": {"campaignName": {}, "vehicleName": {}, "status": {}}}}, "nextToken": {}}}}, "ImportDecoderManifest": {"input": {"type": "structure", "required": ["name", "networkFileDefinitions"], "members": {"name": {}, "networkFileDefinitions": {"type": "list", "member": {"type": "structure", "members": {"canDbc": {"type": "structure", "required": ["networkInterface", "canDbcFiles"], "members": {"networkInterface": {}, "canDbcFiles": {"type": "list", "member": {"type": "blob"}}, "signalsMap": {"type": "map", "key": {}, "value": {}}}}}, "union": true}}}}, "output": {"type": "structure", "required": ["name", "arn"], "members": {"name": {}, "arn": {}}}}, "ImportSignalCatalog": {"input": {"type": "structure", "required": ["name"], "members": {"name": {}, "description": {}, "vss": {"type": "structure", "members": {"vssJson": {}}, "union": true}, "tags": {"shape": "Sd"}}}, "output": {"type": "structure", "required": ["name", "arn"], "members": {"name": {}, "arn": {}}}, "idempotent": true}, "ListCampaigns": {"input": {"type": "structure", "members": {"nextToken": {}, "maxResults": {"type": "integer"}, "status": {}}}, "output": {"type": "structure", "members": {"campaignSummaries": {"type": "list", "member": {"type": "structure", "required": ["creationTime", "lastModificationTime"], "members": {"arn": {}, "name": {}, "description": {}, "signalCatalogArn": {}, "targetArn": {}, "status": {}, "creationTime": {"type": "timestamp"}, "lastModificationTime": {"type": "timestamp"}}}}, "nextToken": {}}}}, "ListDecoderManifestNetworkInterfaces": {"input": {"type": "structure", "required": ["name"], "members": {"name": {}, "nextToken": {}, "maxResults": {"type": "integer"}}}, "output": {"type": "structure", "members": {"networkInterfaces": {"shape": "S20"}, "nextToken": {}}}}, "ListDecoderManifestSignals": {"input": {"type": "structure", "required": ["name"], "members": {"name": {}, "nextToken": {}, "maxResults": {"type": "integer"}}}, "output": {"type": "structure", "members": {"signalDecoders": {"shape": "S1m"}, "nextToken": {}}}}, "ListDecoderManifests": {"input": {"type": "structure", "members": {"modelManifestArn": {}, "nextToken": {}, "maxResults": {"type": "integer"}}}, "output": {"type": "structure", "members": {"summaries": {"type": "list", "member": {"type": "structure", "required": ["creationTime", "lastModificationTime"], "members": {"name": {}, "arn": {}, "modelManifestArn": {}, "description": {}, "status": {}, "creationTime": {"type": "timestamp"}, "lastModificationTime": {"type": "timestamp"}}}}, "nextToken": {}}}}, "ListFleets": {"input": {"type": "structure", "members": {"nextToken": {}, "maxResults": {"type": "integer"}}}, "output": {"type": "structure", "members": {"fleetSummaries": {"type": "list", "member": {"type": "structure", "required": ["id", "arn", "signalCatalogArn", "creationTime"], "members": {"id": {}, "arn": {}, "description": {}, "signalCatalogArn": {}, "creationTime": {"type": "timestamp"}, "lastModificationTime": {"type": "timestamp"}}}}, "nextToken": {}}}}, "ListFleetsForVehicle": {"input": {"type": "structure", "required": ["vehicleName"], "members": {"vehicleName": {}, "nextToken": {}, "maxResults": {"type": "integer"}}}, "output": {"type": "structure", "members": {"fleets": {"type": "list", "member": {}}, "nextToken": {}}}}, "ListModelManifestNodes": {"input": {"type": "structure", "required": ["name"], "members": {"name": {}, "nextToken": {}, "maxResults": {"type": "integer"}}}, "output": {"type": "structure", "members": {"nodes": {"shape": "S2h"}, "nextToken": {}}}}, "ListModelManifests": {"input": {"type": "structure", "members": {"signalCatalogArn": {}, "nextToken": {}, "maxResults": {"type": "integer"}}}, "output": {"type": "structure", "members": {"summaries": {"type": "list", "member": {"type": "structure", "required": ["creationTime", "lastModificationTime"], "members": {"name": {}, "arn": {}, "signalCatalogArn": {}, "description": {}, "status": {}, "creationTime": {"type": "timestamp"}, "lastModificationTime": {"type": "timestamp"}}}}, "nextToken": {}}}}, "ListSignalCatalogNodes": {"input": {"type": "structure", "required": ["name"], "members": {"name": {}, "nextToken": {}, "maxResults": {"type": "integer"}}}, "output": {"type": "structure", "members": {"nodes": {"shape": "S2h"}, "nextToken": {}}}}, "ListSignalCatalogs": {"input": {"type": "structure", "members": {"nextToken": {}, "maxResults": {"type": "integer"}}}, "output": {"type": "structure", "members": {"summaries": {"type": "list", "member": {"type": "structure", "members": {"name": {}, "arn": {}, "creationTime": {"type": "timestamp"}, "lastModificationTime": {"type": "timestamp"}}}}, "nextToken": {}}}}, "ListTagsForResource": {"input": {"type": "structure", "required": ["ResourceARN"], "members": {"ResourceARN": {}}}, "output": {"type": "structure", "members": {"Tags": {"shape": "Sd"}}}}, "ListVehicles": {"input": {"type": "structure", "members": {"modelManifestArn": {}, "nextToken": {}, "maxResults": {"type": "integer"}}}, "output": {"type": "structure", "members": {"vehicleSummaries": {"type": "list", "member": {"type": "structure", "required": ["vehicleName", "arn", "modelManifestArn", "decoderManifestArn", "creationTime", "lastModificationTime"], "members": {"vehicleName": {}, "arn": {}, "modelManifestArn": {}, "decoderManifestArn": {}, "creationTime": {"type": "timestamp"}, "lastModificationTime": {"type": "timestamp"}}}}, "nextToken": {}}}}, "ListVehiclesInFleet": {"input": {"type": "structure", "required": ["fleetId"], "members": {"fleetId": {}, "nextToken": {}, "maxResults": {"type": "integer"}}}, "output": {"type": "structure", "members": {"vehicles": {"type": "list", "member": {}}, "nextToken": {}}}}, "PutLoggingOptions": {"input": {"type": "structure", "required": ["cloudWatchLogDelivery"], "members": {"cloudWatchLogDelivery": {"shape": "S3f"}}}, "output": {"type": "structure", "members": {}}, "idempotent": true}, "RegisterAccount": {"input": {"type": "structure", "required": ["timestreamResources"], "members": {"timestreamResources": {"shape": "S5r"}, "iamResources": {"shape": "S5s", "deprecated": true, "deprecatedMessage": "iamResources is no longer used or needed as input"}}}, "output": {"type": "structure", "required": ["registerAccountStatus", "timestreamResources", "iamResources", "creationTime", "lastModificationTime"], "members": {"registerAccountStatus": {}, "timestreamResources": {"shape": "S5r"}, "iamResources": {"shape": "S5s"}, "creationTime": {"type": "timestamp"}, "lastModificationTime": {"type": "timestamp"}}}}, "TagResource": {"input": {"type": "structure", "required": ["ResourceARN", "Tags"], "members": {"ResourceARN": {}, "Tags": {"shape": "Sd"}}}, "output": {"type": "structure", "members": {}}, "idempotent": true}, "UntagResource": {"input": {"type": "structure", "required": ["ResourceARN", "TagKeys"], "members": {"ResourceARN": {}, "TagKeys": {"type": "list", "member": {}}}}, "output": {"type": "structure", "members": {}}, "idempotent": true}, "UpdateCampaign": {"input": {"type": "structure", "required": ["name", "action"], "members": {"name": {}, "description": {}, "dataExtraDimensions": {"shape": "S1h"}, "action": {}}}, "output": {"type": "structure", "members": {"arn": {}, "name": {}, "status": {}}}}, "UpdateDecoderManifest": {"input": {"type": "structure", "required": ["name"], "members": {"name": {}, "description": {}, "signalDecodersToAdd": {"shape": "S1m"}, "signalDecodersToUpdate": {"shape": "S1m"}, "signalDecodersToRemove": {"type": "list", "member": {}}, "networkInterfacesToAdd": {"shape": "S20"}, "networkInterfacesToUpdate": {"shape": "S20"}, "networkInterfacesToRemove": {"type": "list", "member": {}}, "status": {}}}, "output": {"type": "structure", "required": ["name", "arn"], "members": {"name": {}, "arn": {}}}, "idempotent": true}, "UpdateFleet": {"input": {"type": "structure", "required": ["fleetId"], "members": {"fleetId": {}, "description": {}}}, "output": {"type": "structure", "members": {"id": {}, "arn": {}}}}, "UpdateModelManifest": {"input": {"type": "structure", "required": ["name"], "members": {"name": {}, "description": {}, "nodesToAdd": {"shape": "S6a"}, "nodesToRemove": {"shape": "S6a"}, "status": {}}}, "output": {"type": "structure", "required": ["name", "arn"], "members": {"name": {}, "arn": {}}}, "idempotent": true}, "UpdateSignalCatalog": {"input": {"type": "structure", "required": ["name"], "members": {"name": {}, "description": {}, "nodesToAdd": {"shape": "S2h"}, "nodesToUpdate": {"shape": "S2h"}, "nodesToRemove": {"shape": "S6a"}}}, "output": {"type": "structure", "required": ["name", "arn"], "members": {"name": {}, "arn": {}}}, "idempotent": true}, "UpdateVehicle": {"input": {"type": "structure", "required": ["vehicleName"], "members": {"vehicleName": {}, "modelManifestArn": {}, "decoderManifestArn": {}, "attributes": {"shape": "S9"}, "attributeUpdateMode": {}}}, "output": {"type": "structure", "members": {"vehicleName": {}, "arn": {}}}}}, "shapes": {"S9": {"type": "map", "key": {}, "value": {}}, "Sd": {"type": "list", "member": {"type": "structure", "required": ["Key", "Value"], "members": {"Key": {}, "Value": {}}}}, "S16": {"type": "list", "member": {"type": "structure", "required": ["name"], "members": {"name": {}, "maxSampleCount": {"type": "long"}, "minimumSamplingIntervalMs": {"type": "long"}}}}, "S1a": {"type": "structure", "members": {"timeBasedCollectionScheme": {"type": "structure", "required": ["periodMs"], "members": {"periodMs": {"type": "long"}}}, "conditionBasedCollectionScheme": {"type": "structure", "required": ["expression"], "members": {"expression": {}, "minimumTriggerIntervalMs": {"type": "long"}, "triggerMode": {}, "conditionLanguageVersion": {"type": "integer"}}}}, "union": true}, "S1h": {"type": "list", "member": {}}, "S1m": {"type": "list", "member": {"type": "structure", "required": ["fullyQualifiedName", "type", "interfaceId"], "members": {"fullyQualifiedName": {}, "type": {}, "interfaceId": {}, "canSignal": {"type": "structure", "required": ["messageId", "isBigEndian", "isSigned", "startBit", "offset", "factor", "length"], "members": {"messageId": {"type": "integer"}, "isBigEndian": {"type": "boolean"}, "isSigned": {"type": "boolean"}, "startBit": {"type": "integer"}, "offset": {"type": "double"}, "factor": {"type": "double"}, "length": {"type": "integer"}, "name": {}}}, "obdSignal": {"type": "structure", "required": ["pidResponseLength", "serviceMode", "pid", "scaling", "offset", "startByte", "byteLength"], "members": {"pidResponseLength": {"type": "integer"}, "serviceMode": {"type": "integer"}, "pid": {"type": "integer"}, "scaling": {"type": "double"}, "offset": {"type": "double"}, "startByte": {"type": "integer"}, "byteLength": {"type": "integer"}, "bitRightShift": {"type": "integer"}, "bitMaskLength": {"type": "integer"}}}}}}, "S20": {"type": "list", "member": {"type": "structure", "required": ["interfaceId", "type"], "members": {"interfaceId": {}, "type": {}, "canInterface": {"type": "structure", "required": ["name"], "members": {"name": {}, "protocolName": {}, "protocolVersion": {}}}, "obdInterface": {"type": "structure", "required": ["name", "requestMessageId"], "members": {"name": {}, "requestMessageId": {"type": "integer"}, "obdStandard": {}, "pidRequestIntervalSeconds": {"type": "integer"}, "dtcRequestIntervalSeconds": {"type": "integer"}, "useExtendedIds": {"type": "boolean"}, "hasTransmissionEcu": {"type": "boolean"}}}}}}, "S2e": {"type": "list", "member": {}}, "S2h": {"type": "list", "member": {"type": "structure", "members": {"branch": {"type": "structure", "required": ["fullyQualifiedName"], "members": {"fullyQualifiedName": {}, "description": {}}}, "sensor": {"type": "structure", "required": ["fullyQualifiedName", "dataType"], "members": {"fullyQualifiedName": {}, "dataType": {}, "description": {}, "unit": {}, "allowedValues": {"shape": "S2e"}, "min": {"type": "double"}, "max": {"type": "double"}}}, "actuator": {"type": "structure", "required": ["fullyQualifiedName", "dataType"], "members": {"fullyQualifiedName": {}, "dataType": {}, "description": {}, "unit": {}, "allowedValues": {"shape": "S2e"}, "min": {"type": "double"}, "max": {"type": "double"}, "assignedValue": {"deprecated": true, "deprecatedMessage": "assignedValue is no longer in use"}}}, "attribute": {"type": "structure", "required": ["fullyQualifiedName", "dataType"], "members": {"fullyQualifiedName": {}, "dataType": {}, "description": {}, "unit": {}, "allowedValues": {"shape": "S2e"}, "min": {"type": "double"}, "max": {"type": "double"}, "assignedValue": {"deprecated": true, "deprecatedMessage": "assignedValue is no longer in use"}, "defaultValue": {}}}}, "union": true}}, "S3f": {"type": "structure", "required": ["logType"], "members": {"logType": {}, "logGroupName": {}}}, "S5r": {"type": "structure", "required": ["timestreamDatabaseName", "timestreamTableName"], "members": {"timestreamDatabaseName": {}, "timestreamTableName": {}}}, "S5s": {"type": "structure", "required": ["roleArn"], "members": {"roleArn": {}}}, "S6a": {"type": "list", "member": {}}}}