{"version": "2.0", "metadata": {"apiVersion": "2012-11-05", "endpointPrefix": "sqs", "protocol": "query", "serviceAbbreviation": "Amazon SQS", "serviceFullName": "Amazon Simple Queue Service", "serviceId": "SQS", "signatureVersion": "v4", "uid": "sqs-2012-11-05", "xmlNamespace": "http://queue.amazonaws.com/doc/2012-11-05/"}, "operations": {"AddPermission": {"input": {"type": "structure", "required": ["QueueUrl", "Label", "AWSAccountIds", "Actions"], "members": {"QueueUrl": {}, "Label": {}, "AWSAccountIds": {"type": "list", "member": {"locationName": "AWSAccountId"}, "flattened": true}, "Actions": {"type": "list", "member": {"locationName": "ActionName"}, "flattened": true}}}}, "ChangeMessageVisibility": {"input": {"type": "structure", "required": ["QueueUrl", "ReceiptHandle", "VisibilityTimeout"], "members": {"QueueUrl": {}, "ReceiptHandle": {}, "VisibilityTimeout": {"type": "integer"}}}}, "ChangeMessageVisibilityBatch": {"input": {"type": "structure", "required": ["QueueUrl", "Entries"], "members": {"QueueUrl": {}, "Entries": {"type": "list", "member": {"locationName": "ChangeMessageVisibilityBatchRequestEntry", "type": "structure", "required": ["Id", "ReceiptHandle"], "members": {"Id": {}, "ReceiptHandle": {}, "VisibilityTimeout": {"type": "integer"}}}, "flattened": true}}}, "output": {"resultWrapper": "ChangeMessageVisibilityBatchResult", "type": "structure", "required": ["Successful", "Failed"], "members": {"Successful": {"type": "list", "member": {"locationName": "ChangeMessageVisibilityBatchResultEntry", "type": "structure", "required": ["Id"], "members": {"Id": {}}}, "flattened": true}, "Failed": {"shape": "Sd"}}}}, "CreateQueue": {"input": {"type": "structure", "required": ["QueueName"], "members": {"QueueName": {}, "Attributes": {"shape": "Sh", "locationName": "Attribute"}, "tags": {"shape": "Sj", "locationName": "Tag"}}}, "output": {"resultWrapper": "CreateQueueResult", "type": "structure", "members": {"QueueUrl": {}}}}, "DeleteMessage": {"input": {"type": "structure", "required": ["QueueUrl", "ReceiptHandle"], "members": {"QueueUrl": {}, "ReceiptHandle": {}}}}, "DeleteMessageBatch": {"input": {"type": "structure", "required": ["QueueUrl", "Entries"], "members": {"QueueUrl": {}, "Entries": {"type": "list", "member": {"locationName": "DeleteMessageBatchRequestEntry", "type": "structure", "required": ["Id", "ReceiptHandle"], "members": {"Id": {}, "ReceiptHandle": {}}}, "flattened": true}}}, "output": {"resultWrapper": "DeleteMessageBatchResult", "type": "structure", "required": ["Successful", "Failed"], "members": {"Successful": {"type": "list", "member": {"locationName": "DeleteMessageBatchResultEntry", "type": "structure", "required": ["Id"], "members": {"Id": {}}}, "flattened": true}, "Failed": {"shape": "Sd"}}}}, "DeleteQueue": {"input": {"type": "structure", "required": ["QueueUrl"], "members": {"QueueUrl": {}}}}, "GetQueueAttributes": {"input": {"type": "structure", "required": ["QueueUrl"], "members": {"QueueUrl": {}, "AttributeNames": {"shape": "Sw"}}}, "output": {"resultWrapper": "GetQueueAttributesResult", "type": "structure", "members": {"Attributes": {"shape": "Sh", "locationName": "Attribute"}}}}, "GetQueueUrl": {"input": {"type": "structure", "required": ["QueueName"], "members": {"QueueName": {}, "QueueOwnerAWSAccountId": {}}}, "output": {"resultWrapper": "GetQueueUrlResult", "type": "structure", "members": {"QueueUrl": {}}}}, "ListDeadLetterSourceQueues": {"input": {"type": "structure", "required": ["QueueUrl"], "members": {"QueueUrl": {}, "NextToken": {}, "MaxResults": {"type": "integer"}}}, "output": {"resultWrapper": "ListDeadLetterSourceQueuesResult", "type": "structure", "required": ["queueUrls"], "members": {"queueUrls": {"shape": "S14"}, "NextToken": {}}}}, "ListQueueTags": {"input": {"type": "structure", "required": ["QueueUrl"], "members": {"QueueUrl": {}}}, "output": {"resultWrapper": "ListQueueTagsResult", "type": "structure", "members": {"Tags": {"shape": "Sj", "locationName": "Tag"}}}}, "ListQueues": {"input": {"type": "structure", "members": {"QueueNamePrefix": {}, "NextToken": {}, "MaxResults": {"type": "integer"}}}, "output": {"resultWrapper": "ListQueuesResult", "type": "structure", "members": {"QueueUrls": {"shape": "S14"}, "NextToken": {}}}}, "PurgeQueue": {"input": {"type": "structure", "required": ["QueueUrl"], "members": {"QueueUrl": {}}}}, "ReceiveMessage": {"input": {"type": "structure", "required": ["QueueUrl"], "members": {"QueueUrl": {}, "AttributeNames": {"shape": "Sw"}, "MessageAttributeNames": {"type": "list", "member": {"locationName": "MessageAttributeName"}, "flattened": true}, "MaxNumberOfMessages": {"type": "integer"}, "VisibilityTimeout": {"type": "integer"}, "WaitTimeSeconds": {"type": "integer"}, "ReceiveRequestAttemptId": {}}}, "output": {"resultWrapper": "ReceiveMessageResult", "type": "structure", "members": {"Messages": {"type": "list", "member": {"locationName": "Message", "type": "structure", "members": {"MessageId": {}, "ReceiptHandle": {}, "MD5OfBody": {}, "Body": {}, "Attributes": {"locationName": "Attribute", "type": "map", "key": {"locationName": "Name"}, "value": {"locationName": "Value"}, "flattened": true}, "MD5OfMessageAttributes": {}, "MessageAttributes": {"shape": "S1i", "locationName": "MessageAttribute"}}}, "flattened": true}}}}, "RemovePermission": {"input": {"type": "structure", "required": ["QueueUrl", "Label"], "members": {"QueueUrl": {}, "Label": {}}}}, "SendMessage": {"input": {"type": "structure", "required": ["QueueUrl", "MessageBody"], "members": {"QueueUrl": {}, "MessageBody": {}, "DelaySeconds": {"type": "integer"}, "MessageAttributes": {"shape": "S1i", "locationName": "MessageAttribute"}, "MessageSystemAttributes": {"shape": "S1p", "locationName": "MessageSystemAttribute"}, "MessageDeduplicationId": {}, "MessageGroupId": {}}}, "output": {"resultWrapper": "SendMessageResult", "type": "structure", "members": {"MD5OfMessageBody": {}, "MD5OfMessageAttributes": {}, "MD5OfMessageSystemAttributes": {}, "MessageId": {}, "SequenceNumber": {}}}}, "SendMessageBatch": {"input": {"type": "structure", "required": ["QueueUrl", "Entries"], "members": {"QueueUrl": {}, "Entries": {"type": "list", "member": {"locationName": "SendMessageBatchRequestEntry", "type": "structure", "required": ["Id", "MessageBody"], "members": {"Id": {}, "MessageBody": {}, "DelaySeconds": {"type": "integer"}, "MessageAttributes": {"shape": "S1i", "locationName": "MessageAttribute"}, "MessageSystemAttributes": {"shape": "S1p", "locationName": "MessageSystemAttribute"}, "MessageDeduplicationId": {}, "MessageGroupId": {}}}, "flattened": true}}}, "output": {"resultWrapper": "SendMessageBatchResult", "type": "structure", "required": ["Successful", "Failed"], "members": {"Successful": {"type": "list", "member": {"locationName": "SendMessageBatchResultEntry", "type": "structure", "required": ["Id", "MessageId", "MD5OfMessageBody"], "members": {"Id": {}, "MessageId": {}, "MD5OfMessageBody": {}, "MD5OfMessageAttributes": {}, "MD5OfMessageSystemAttributes": {}, "SequenceNumber": {}}}, "flattened": true}, "Failed": {"shape": "Sd"}}}}, "SetQueueAttributes": {"input": {"type": "structure", "required": ["QueueUrl", "Attributes"], "members": {"QueueUrl": {}, "Attributes": {"shape": "Sh", "locationName": "Attribute"}}}}, "TagQueue": {"input": {"type": "structure", "required": ["QueueUrl", "Tags"], "members": {"QueueUrl": {}, "Tags": {"shape": "Sj"}}}}, "UntagQueue": {"input": {"type": "structure", "required": ["QueueUrl", "TagKeys"], "members": {"QueueUrl": {}, "TagKeys": {"type": "list", "member": {"locationName": "TagKey"}, "flattened": true}}}}}, "shapes": {"Sd": {"type": "list", "member": {"locationName": "BatchResultErrorEntry", "type": "structure", "required": ["Id", "<PERSON><PERSON><PERSON><PERSON>", "Code"], "members": {"Id": {}, "SenderFault": {"type": "boolean"}, "Code": {}, "Message": {}}}, "flattened": true}, "Sh": {"type": "map", "key": {"locationName": "Name"}, "value": {"locationName": "Value"}, "flattened": true, "locationName": "Attribute"}, "Sj": {"type": "map", "key": {"locationName": "Key"}, "value": {"locationName": "Value"}, "flattened": true, "locationName": "Tag"}, "Sw": {"type": "list", "member": {"locationName": "AttributeName"}, "flattened": true}, "S14": {"type": "list", "member": {"locationName": "QueueUrl"}, "flattened": true}, "S1i": {"type": "map", "key": {"locationName": "Name"}, "value": {"locationName": "Value", "type": "structure", "required": ["DataType"], "members": {"StringValue": {}, "BinaryValue": {"type": "blob"}, "StringListValues": {"shape": "S1l", "flattened": true, "locationName": "StringListValue"}, "BinaryListValues": {"shape": "S1m", "flattened": true, "locationName": "BinaryListValue"}, "DataType": {}}}, "flattened": true}, "S1l": {"type": "list", "member": {"locationName": "StringListValue"}}, "S1m": {"type": "list", "member": {"locationName": "BinaryListValue", "type": "blob"}}, "S1p": {"type": "map", "key": {"locationName": "Name"}, "value": {"locationName": "Value", "type": "structure", "required": ["DataType"], "members": {"StringValue": {}, "BinaryValue": {"type": "blob"}, "StringListValues": {"shape": "S1l", "flattened": true, "locationName": "StringListValue"}, "BinaryListValues": {"shape": "S1m", "flattened": true, "locationName": "BinaryListValue"}, "DataType": {}}}, "flattened": true}}}