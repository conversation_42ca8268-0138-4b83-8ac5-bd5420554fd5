{"version": "2.0", "metadata": {"apiVersion": "2021-11-01", "endpointPrefix": "aoss", "jsonVersion": "1.0", "protocol": "json", "serviceFullName": "OpenSearch Service Serverless", "serviceId": "OpenSearchServerless", "signatureVersion": "v4", "signingName": "aoss", "targetPrefix": "OpenSearchServerless", "uid": "opensearchserverless-2021-11-01"}, "operations": {"BatchGetCollection": {"input": {"type": "structure", "members": {"ids": {"type": "list", "member": {}}, "names": {"type": "list", "member": {}}}}, "output": {"type": "structure", "members": {"collectionDetails": {"type": "list", "member": {"type": "structure", "members": {"arn": {}, "collectionEndpoint": {}, "createdDate": {"type": "long"}, "dashboardEndpoint": {}, "description": {}, "id": {}, "kmsKeyArn": {}, "lastModifiedDate": {"type": "long"}, "name": {}, "status": {}, "type": {}}}}, "collectionErrorDetails": {"type": "list", "member": {"type": "structure", "members": {"errorCode": {}, "errorMessage": {}, "id": {}, "name": {}}}}}}}, "BatchGetVpcEndpoint": {"input": {"type": "structure", "required": ["ids"], "members": {"ids": {"type": "list", "member": {}}}}, "output": {"type": "structure", "members": {"vpcEndpointDetails": {"type": "list", "member": {"type": "structure", "members": {"createdDate": {"type": "long"}, "id": {}, "name": {}, "securityGroupIds": {"shape": "Sm"}, "status": {}, "subnetIds": {"shape": "Sp"}, "vpcId": {}}}}, "vpcEndpointErrorDetails": {"type": "list", "member": {"type": "structure", "members": {"errorCode": {}, "errorMessage": {}, "id": {}}}}}}}, "CreateAccessPolicy": {"input": {"type": "structure", "required": ["name", "policy", "type"], "members": {"clientToken": {"idempotencyToken": true}, "description": {}, "name": {}, "policy": {}, "type": {}}}, "output": {"type": "structure", "members": {"accessPolicyDetail": {"shape": "S11"}}}, "idempotent": true}, "CreateCollection": {"input": {"type": "structure", "required": ["name"], "members": {"clientToken": {"idempotencyToken": true}, "description": {}, "name": {}, "tags": {"shape": "S16"}, "type": {}}}, "output": {"type": "structure", "members": {"createCollectionDetail": {"type": "structure", "members": {"arn": {}, "createdDate": {"type": "long"}, "description": {}, "id": {}, "kmsKeyArn": {}, "lastModifiedDate": {"type": "long"}, "name": {}, "status": {}, "type": {}}}}}, "idempotent": true}, "CreateSecurityConfig": {"input": {"type": "structure", "required": ["name", "type"], "members": {"clientToken": {"idempotencyToken": true}, "description": {}, "name": {}, "samlOptions": {"shape": "S1f"}, "type": {}}}, "output": {"type": "structure", "members": {"securityConfigDetail": {"shape": "S1m"}}}, "idempotent": true}, "CreateSecurityPolicy": {"input": {"type": "structure", "required": ["name", "policy", "type"], "members": {"clientToken": {"idempotencyToken": true}, "description": {}, "name": {}, "policy": {}, "type": {}}}, "output": {"type": "structure", "members": {"securityPolicyDetail": {"shape": "S1r"}}}, "idempotent": true}, "CreateVpcEndpoint": {"input": {"type": "structure", "required": ["name", "subnetIds", "vpcId"], "members": {"clientToken": {"idempotencyToken": true}, "name": {}, "securityGroupIds": {"shape": "Sm"}, "subnetIds": {"shape": "Sp"}, "vpcId": {}}}, "output": {"type": "structure", "members": {"createVpcEndpointDetail": {"type": "structure", "members": {"id": {}, "name": {}, "status": {}}}}}, "idempotent": true}, "DeleteAccessPolicy": {"input": {"type": "structure", "required": ["name", "type"], "members": {"clientToken": {"idempotencyToken": true}, "name": {}, "type": {}}}, "output": {"type": "structure", "members": {}}, "idempotent": true}, "DeleteCollection": {"input": {"type": "structure", "required": ["id"], "members": {"clientToken": {"idempotencyToken": true}, "id": {}}}, "output": {"type": "structure", "members": {"deleteCollectionDetail": {"type": "structure", "members": {"id": {}, "name": {}, "status": {}}}}}, "idempotent": true}, "DeleteSecurityConfig": {"input": {"type": "structure", "required": ["id"], "members": {"clientToken": {"idempotencyToken": true}, "id": {}}}, "output": {"type": "structure", "members": {}}, "idempotent": true}, "DeleteSecurityPolicy": {"input": {"type": "structure", "required": ["name", "type"], "members": {"clientToken": {"idempotencyToken": true}, "name": {}, "type": {}}}, "output": {"type": "structure", "members": {}}, "idempotent": true}, "DeleteVpcEndpoint": {"input": {"type": "structure", "required": ["id"], "members": {"clientToken": {"idempotencyToken": true}, "id": {}}}, "output": {"type": "structure", "members": {"deleteVpcEndpointDetail": {"type": "structure", "members": {"id": {}, "name": {}, "status": {}}}}}, "idempotent": true}, "GetAccessPolicy": {"input": {"type": "structure", "required": ["name", "type"], "members": {"name": {}, "type": {}}}, "output": {"type": "structure", "members": {"accessPolicyDetail": {"shape": "S11"}}}}, "GetAccountSettings": {"input": {"type": "structure", "members": {}}, "output": {"type": "structure", "members": {"accountSettingsDetail": {"shape": "S2b"}}}}, "GetPoliciesStats": {"input": {"type": "structure", "members": {}}, "output": {"type": "structure", "members": {"AccessPolicyStats": {"type": "structure", "members": {"DataPolicyCount": {"type": "long"}}}, "SecurityConfigStats": {"type": "structure", "members": {"SamlConfigCount": {"type": "long"}}}, "SecurityPolicyStats": {"type": "structure", "members": {"EncryptionPolicyCount": {"type": "long"}, "NetworkPolicyCount": {"type": "long"}}}, "TotalPolicyCount": {"type": "long"}}}}, "GetSecurityConfig": {"input": {"type": "structure", "required": ["id"], "members": {"id": {}}}, "output": {"type": "structure", "members": {"securityConfigDetail": {"shape": "S1m"}}}}, "GetSecurityPolicy": {"input": {"type": "structure", "required": ["name", "type"], "members": {"name": {}, "type": {}}}, "output": {"type": "structure", "members": {"securityPolicyDetail": {"shape": "S1r"}}}}, "ListAccessPolicies": {"input": {"type": "structure", "required": ["type"], "members": {"maxResults": {"type": "integer"}, "nextToken": {}, "resource": {"type": "list", "member": {}}, "type": {}}}, "output": {"type": "structure", "members": {"accessPolicySummaries": {"type": "list", "member": {"type": "structure", "members": {"createdDate": {"type": "long"}, "description": {}, "lastModifiedDate": {"type": "long"}, "name": {}, "policyVersion": {}, "type": {}}}}, "nextToken": {}}}}, "ListCollections": {"input": {"type": "structure", "members": {"collectionFilters": {"type": "structure", "members": {"name": {}, "status": {}}}, "maxResults": {"type": "integer"}, "nextToken": {}}}, "output": {"type": "structure", "members": {"collectionSummaries": {"type": "list", "member": {"type": "structure", "members": {"arn": {}, "id": {}, "name": {}, "status": {}}}}, "nextToken": {}}}}, "ListSecurityConfigs": {"input": {"type": "structure", "required": ["type"], "members": {"maxResults": {"type": "integer"}, "nextToken": {}, "type": {}}}, "output": {"type": "structure", "members": {"nextToken": {}, "securityConfigSummaries": {"type": "list", "member": {"type": "structure", "members": {"configVersion": {}, "createdDate": {"type": "long"}, "description": {}, "id": {}, "lastModifiedDate": {"type": "long"}, "type": {}}}}}}}, "ListSecurityPolicies": {"input": {"type": "structure", "required": ["type"], "members": {"maxResults": {"type": "integer"}, "nextToken": {}, "resource": {"type": "list", "member": {}}, "type": {}}}, "output": {"type": "structure", "members": {"nextToken": {}, "securityPolicySummaries": {"type": "list", "member": {"type": "structure", "members": {"createdDate": {"type": "long"}, "description": {}, "lastModifiedDate": {"type": "long"}, "name": {}, "policyVersion": {}, "type": {}}}}}}}, "ListTagsForResource": {"input": {"type": "structure", "required": ["resourceArn"], "members": {"resourceArn": {}}}, "output": {"type": "structure", "members": {"tags": {"shape": "S16"}}}}, "ListVpcEndpoints": {"input": {"type": "structure", "members": {"maxResults": {"type": "integer"}, "nextToken": {}, "vpcEndpointFilters": {"type": "structure", "members": {"status": {}}}}}, "output": {"type": "structure", "members": {"nextToken": {}, "vpcEndpointSummaries": {"type": "list", "member": {"type": "structure", "members": {"id": {}, "name": {}, "status": {}}}}}}}, "TagResource": {"input": {"type": "structure", "required": ["resourceArn", "tags"], "members": {"resourceArn": {}, "tags": {"shape": "S16"}}}, "output": {"type": "structure", "members": {}}}, "UntagResource": {"input": {"type": "structure", "required": ["resourceArn", "tagKeys"], "members": {"resourceArn": {}, "tagKeys": {"type": "list", "member": {}}}}, "output": {"type": "structure", "members": {}}}, "UpdateAccessPolicy": {"input": {"type": "structure", "required": ["name", "policyVersion", "type"], "members": {"clientToken": {"idempotencyToken": true}, "description": {}, "name": {}, "policy": {}, "policyVersion": {}, "type": {}}}, "output": {"type": "structure", "members": {"accessPolicyDetail": {"shape": "S11"}}}, "idempotent": true}, "UpdateAccountSettings": {"input": {"type": "structure", "members": {"capacityLimits": {"shape": "S2c"}}}, "output": {"type": "structure", "members": {"accountSettingsDetail": {"shape": "S2b"}}}}, "UpdateCollection": {"input": {"type": "structure", "required": ["id"], "members": {"clientToken": {"idempotencyToken": true}, "description": {}, "id": {}}}, "output": {"type": "structure", "members": {"updateCollectionDetail": {"type": "structure", "members": {"arn": {}, "createdDate": {"type": "long"}, "description": {}, "id": {}, "lastModifiedDate": {"type": "long"}, "name": {}, "status": {}, "type": {}}}}}, "idempotent": true}, "UpdateSecurityConfig": {"input": {"type": "structure", "required": ["configVersion", "id"], "members": {"clientToken": {"idempotencyToken": true}, "configVersion": {}, "description": {}, "id": {}, "samlOptions": {"shape": "S1f"}}}, "output": {"type": "structure", "members": {"securityConfigDetail": {"shape": "S1m"}}}, "idempotent": true}, "UpdateSecurityPolicy": {"input": {"type": "structure", "required": ["name", "policyVersion", "type"], "members": {"clientToken": {"idempotencyToken": true}, "description": {}, "name": {}, "policy": {}, "policyVersion": {}, "type": {}}}, "output": {"type": "structure", "members": {"securityPolicyDetail": {"shape": "S1r"}}}, "idempotent": true}, "UpdateVpcEndpoint": {"input": {"type": "structure", "required": ["id"], "members": {"addSecurityGroupIds": {"shape": "Sm"}, "addSubnetIds": {"shape": "Sp"}, "clientToken": {"idempotencyToken": true}, "id": {}, "removeSecurityGroupIds": {"shape": "Sm"}, "removeSubnetIds": {"shape": "Sp"}}}, "output": {"type": "structure", "members": {"UpdateVpcEndpointDetail": {"type": "structure", "members": {"id": {}, "lastModifiedDate": {"type": "long"}, "name": {}, "securityGroupIds": {"shape": "Sm"}, "status": {}, "subnetIds": {"shape": "Sp"}}}}}, "idempotent": true}}, "shapes": {"Sm": {"type": "list", "member": {}}, "Sp": {"type": "list", "member": {}}, "S11": {"type": "structure", "members": {"createdDate": {"type": "long"}, "description": {}, "lastModifiedDate": {"type": "long"}, "name": {}, "policy": {"shape": "S12"}, "policyVersion": {}, "type": {}}}, "S12": {"type": "structure", "members": {}, "document": true}, "S16": {"type": "list", "member": {"type": "structure", "required": ["key", "value"], "members": {"key": {}, "value": {}}}}, "S1f": {"type": "structure", "required": ["metadata"], "members": {"groupAttribute": {}, "metadata": {}, "sessionTimeout": {"type": "integer"}, "userAttribute": {}}}, "S1m": {"type": "structure", "members": {"configVersion": {}, "createdDate": {"type": "long"}, "description": {}, "id": {}, "lastModifiedDate": {"type": "long"}, "samlOptions": {"shape": "S1f"}, "type": {}}}, "S1r": {"type": "structure", "members": {"createdDate": {"type": "long"}, "description": {}, "lastModifiedDate": {"type": "long"}, "name": {}, "policy": {"shape": "S12"}, "policyVersion": {}, "type": {}}}, "S2b": {"type": "structure", "members": {"capacityLimits": {"shape": "S2c"}}}, "S2c": {"type": "structure", "members": {"maxIndexingCapacityInOCU": {"type": "integer"}, "maxSearchCapacityInOCU": {"type": "integer"}}}}}