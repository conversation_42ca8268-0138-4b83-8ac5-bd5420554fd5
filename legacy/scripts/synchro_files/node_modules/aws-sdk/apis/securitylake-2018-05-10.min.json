{"version": "2.0", "metadata": {"apiVersion": "2018-05-10", "endpointPrefix": "securitylake", "jsonVersion": "1.1", "protocol": "rest-json", "serviceFullName": "Amazon Security Lake", "serviceId": "SecurityLake", "signatureVersion": "v4", "signingName": "securitylake", "uid": "securitylake-2018-05-10"}, "operations": {"CreateAwsLogSource": {"http": {"requestUri": "/v1/logsources/aws", "responseCode": 200}, "input": {"type": "structure", "required": ["inputOrder"], "members": {"enableAllDimensions": {"shape": "S2"}, "enableSingleDimension": {"shape": "S6"}, "enableTwoDimensions": {"shape": "S4"}, "inputOrder": {"shape": "S8"}}}, "output": {"type": "structure", "members": {"failed": {"shape": "Sb"}, "processing": {"shape": "Sb"}}}}, "CreateCustomLogSource": {"http": {"requestUri": "/v1/logsources/custom", "responseCode": 200}, "input": {"type": "structure", "required": ["customSourceName", "eventClass", "glueInvocationRoleArn", "logProviderAccountId"], "members": {"customSourceName": {}, "eventClass": {}, "glueInvocationRoleArn": {}, "logProviderAccountId": {}}}, "output": {"type": "structure", "required": ["customDataLocation", "glueCrawlerName", "glueDatabaseName", "glueTableName", "logProviderAccessRoleArn"], "members": {"customDataLocation": {}, "glueCrawlerName": {}, "glueDatabaseName": {}, "glueTableName": {}, "logProviderAccessRoleArn": {}}}}, "CreateDatalake": {"http": {"requestUri": "/v1/datalake", "responseCode": 200}, "input": {"type": "structure", "members": {"configurations": {"shape": "Sj"}, "enableAll": {"type": "boolean"}, "metaStoreManagerRoleArn": {}, "regions": {"shape": "Sm"}}}, "output": {"type": "structure", "members": {}}}, "CreateDatalakeAutoEnable": {"http": {"requestUri": "/v1/datalake/autoenable", "responseCode": 200}, "input": {"type": "structure", "required": ["configurationForNewAccounts"], "members": {"configurationForNewAccounts": {"shape": "Sv"}}}, "output": {"type": "structure", "members": {}}}, "CreateDatalakeDelegatedAdmin": {"http": {"requestUri": "/v1/datalake/delegate", "responseCode": 200}, "input": {"type": "structure", "required": ["account"], "members": {"account": {}}}, "output": {"type": "structure", "members": {}}}, "CreateDatalakeExceptionsSubscription": {"http": {"requestUri": "/v1/datalake/exceptions/subscription", "responseCode": 200}, "input": {"type": "structure", "required": ["notificationEndpoint", "subscriptionProtocol"], "members": {"notificationEndpoint": {}, "subscriptionProtocol": {}}}, "output": {"type": "structure", "members": {}}}, "CreateSubscriber": {"http": {"requestUri": "/v1/subscribers", "responseCode": 200}, "input": {"type": "structure", "required": ["accountId", "externalId", "sourceTypes", "subscriberName"], "members": {"accessTypes": {"shape": "S16"}, "accountId": {}, "externalId": {}, "sourceTypes": {"shape": "S18"}, "subscriberDescription": {}, "subscriberName": {}}}, "output": {"type": "structure", "required": ["subscriptionId"], "members": {"roleArn": {}, "s3BucketArn": {}, "snsArn": {}, "subscriptionId": {}}}}, "CreateSubscriptionNotificationConfiguration": {"http": {"requestUri": "/subscription-notifications/{subscriptionId}", "responseCode": 200}, "input": {"type": "structure", "required": ["subscriptionId"], "members": {"createSqs": {"type": "boolean"}, "httpsApiKeyName": {}, "httpsApiKeyValue": {}, "httpsMethod": {}, "roleArn": {}, "subscriptionEndpoint": {}, "subscriptionId": {"location": "uri", "locationName": "subscriptionId"}}}, "output": {"type": "structure", "members": {"queueArn": {}}}}, "DeleteAwsLogSource": {"http": {"requestUri": "/v1/logsources/aws/delete", "responseCode": 200}, "input": {"type": "structure", "required": ["inputOrder"], "members": {"disableAllDimensions": {"shape": "S2"}, "disableSingleDimension": {"shape": "S6"}, "disableTwoDimensions": {"shape": "S4"}, "inputOrder": {"shape": "S8"}}}, "output": {"type": "structure", "members": {"failed": {"shape": "Sb"}, "processing": {"shape": "Sb"}}}}, "DeleteCustomLogSource": {"http": {"method": "DELETE", "requestUri": "/v1/logsources/custom", "responseCode": 200}, "input": {"type": "structure", "required": ["customSourceName"], "members": {"customSourceName": {"location": "querystring", "locationName": "customSourceName"}}}, "output": {"type": "structure", "required": ["customDataLocation"], "members": {"customDataLocation": {}}}, "idempotent": true}, "DeleteDatalake": {"http": {"method": "DELETE", "requestUri": "/v1/datalake", "responseCode": 200}, "input": {"type": "structure", "members": {}}, "output": {"type": "structure", "members": {}}, "idempotent": true}, "DeleteDatalakeAutoEnable": {"http": {"requestUri": "/v1/datalake/autoenable/delete", "responseCode": 200}, "input": {"type": "structure", "required": ["removeFromConfigurationForNewAccounts"], "members": {"removeFromConfigurationForNewAccounts": {"shape": "Sv"}}}, "output": {"type": "structure", "members": {}}}, "DeleteDatalakeDelegatedAdmin": {"http": {"method": "DELETE", "requestUri": "/v1/datalake/delegate/{account}", "responseCode": 200}, "input": {"type": "structure", "required": ["account"], "members": {"account": {"location": "uri", "locationName": "account"}}}, "output": {"type": "structure", "members": {}}}, "DeleteDatalakeExceptionsSubscription": {"http": {"method": "DELETE", "requestUri": "/v1/datalake/exceptions/subscription", "responseCode": 200}, "input": {"type": "structure", "members": {}}, "output": {"type": "structure", "required": ["status"], "members": {"status": {}}}}, "DeleteSubscriber": {"http": {"method": "DELETE", "requestUri": "/v1/subscribers", "responseCode": 200}, "input": {"type": "structure", "required": ["id"], "members": {"id": {"location": "querystring", "locationName": "id"}}}, "output": {"type": "structure", "members": {}}, "idempotent": true}, "DeleteSubscriptionNotificationConfiguration": {"http": {"method": "DELETE", "requestUri": "/subscription-notifications/{subscriptionId}", "responseCode": 200}, "input": {"type": "structure", "required": ["subscriptionId"], "members": {"subscriptionId": {"location": "uri", "locationName": "subscriptionId"}}}, "output": {"type": "structure", "members": {}}, "idempotent": true}, "GetDatalake": {"http": {"method": "GET", "requestUri": "/v1/datalake", "responseCode": 200}, "input": {"type": "structure", "members": {}}, "output": {"type": "structure", "required": ["configurations"], "members": {"configurations": {"type": "map", "key": {}, "value": {"type": "structure", "members": {"encryptionKey": {}, "replicationDestinationRegions": {"shape": "Sm"}, "replicationRoleArn": {}, "retentionSettings": {"shape": "Sn"}, "s3BucketArn": {}, "status": {}, "tagsMap": {"shape": "<PERSON>"}}}}}}}, "GetDatalakeAutoEnable": {"http": {"method": "GET", "requestUri": "/v1/datalake/autoenable", "responseCode": 200}, "input": {"type": "structure", "members": {}}, "output": {"type": "structure", "required": ["autoEnableNewAccounts"], "members": {"autoEnableNewAccounts": {"shape": "Sv"}}}}, "GetDatalakeExceptionsExpiry": {"http": {"method": "GET", "requestUri": "/v1/datalake/exceptions/expiry", "responseCode": 200}, "input": {"type": "structure", "members": {}}, "output": {"type": "structure", "required": ["exceptionMessageExpiry"], "members": {"exceptionMessageExpiry": {"type": "long"}}}}, "GetDatalakeExceptionsSubscription": {"http": {"method": "GET", "requestUri": "/v1/datalake/exceptions/subscription", "responseCode": 200}, "input": {"type": "structure", "members": {}}, "output": {"type": "structure", "required": ["protocolAndNotificationEndpoint"], "members": {"protocolAndNotificationEndpoint": {"type": "structure", "members": {"endpoint": {}, "protocol": {}}}}}}, "GetDatalakeStatus": {"http": {"requestUri": "/v1/datalake/status", "responseCode": 200}, "input": {"type": "structure", "members": {"accountSet": {"shape": "S6"}, "maxAccountResults": {"type": "integer"}, "nextToken": {}}}, "output": {"type": "structure", "required": ["accountSourcesList"], "members": {"accountSourcesList": {"type": "list", "member": {"type": "structure", "required": ["account", "sourceType"], "members": {"account": {}, "eventClass": {}, "logsStatus": {"type": "list", "member": {"type": "structure", "required": ["healthStatus", "pathToLogs"], "members": {"healthStatus": {}, "pathToLogs": {}}}}, "sourceType": {}}}}, "nextToken": {}}}}, "GetSubscriber": {"http": {"method": "GET", "requestUri": "/v1/subscribers/{id}", "responseCode": 200}, "input": {"type": "structure", "required": ["id"], "members": {"id": {"location": "uri", "locationName": "id"}}}, "output": {"type": "structure", "members": {"subscriber": {"shape": "S2n"}}}}, "ListDatalakeExceptions": {"http": {"requestUri": "/v1/datalake/exceptions", "responseCode": 200}, "input": {"type": "structure", "members": {"maxFailures": {"type": "integer"}, "nextToken": {}, "regionSet": {"shape": "Sm"}}}, "output": {"type": "structure", "required": ["nonRetryableFailures"], "members": {"nextToken": {}, "nonRetryableFailures": {"type": "list", "member": {"type": "structure", "members": {"failures": {"type": "list", "member": {"type": "structure", "required": ["exceptionMessage", "remediation", "timestamp"], "members": {"exceptionMessage": {}, "remediation": {}, "timestamp": {"shape": "S2o"}}}}, "region": {}}}}}}}, "ListLogSources": {"http": {"requestUri": "/v1/logsources/list", "responseCode": 200}, "input": {"type": "structure", "members": {"inputOrder": {"shape": "S8"}, "listAllDimensions": {"shape": "S2"}, "listSingleDimension": {"shape": "S6"}, "listTwoDimensions": {"shape": "S4"}, "maxResults": {"type": "integer"}, "nextToken": {}}}, "output": {"type": "structure", "required": ["regionSourceTypesAccountsList"], "members": {"nextToken": {}, "regionSourceTypesAccountsList": {"type": "list", "member": {"shape": "S2"}}}}}, "ListSubscribers": {"http": {"method": "GET", "requestUri": "/v1/subscribers", "responseCode": 200}, "input": {"type": "structure", "members": {"maxResults": {"location": "querystring", "locationName": "maxResults", "type": "integer"}, "nextToken": {"location": "querystring", "locationName": "nextToken"}}}, "output": {"type": "structure", "required": ["subscribers"], "members": {"nextToken": {}, "subscribers": {"type": "list", "member": {"shape": "S2n"}}}}}, "UpdateDatalake": {"http": {"method": "PUT", "requestUri": "/v1/datalake", "responseCode": 200}, "input": {"type": "structure", "required": ["configurations"], "members": {"configurations": {"shape": "Sj"}}}, "output": {"type": "structure", "members": {}}, "idempotent": true}, "UpdateDatalakeExceptionsExpiry": {"http": {"method": "PUT", "requestUri": "/v1/datalake/exceptions/expiry", "responseCode": 200}, "input": {"type": "structure", "required": ["exceptionMessageExpiry"], "members": {"exceptionMessageExpiry": {"type": "long"}}}, "output": {"type": "structure", "members": {}}}, "UpdateDatalakeExceptionsSubscription": {"http": {"method": "PUT", "requestUri": "/v1/datalake/exceptions/subscription", "responseCode": 200}, "input": {"type": "structure", "required": ["notificationEndpoint", "subscriptionProtocol"], "members": {"notificationEndpoint": {}, "subscriptionProtocol": {}}}, "output": {"type": "structure", "members": {}}}, "UpdateSubscriber": {"http": {"method": "PUT", "requestUri": "/v1/subscribers/{id}", "responseCode": 200}, "input": {"type": "structure", "required": ["id", "sourceTypes"], "members": {"externalId": {}, "id": {"location": "uri", "locationName": "id"}, "sourceTypes": {"shape": "S18"}, "subscriberDescription": {}, "subscriberName": {}}}, "output": {"type": "structure", "members": {"subscriber": {"shape": "S2n"}}}, "idempotent": true}, "UpdateSubscriptionNotificationConfiguration": {"http": {"method": "PUT", "requestUri": "/subscription-notifications/{subscriptionId}", "responseCode": 200}, "input": {"type": "structure", "required": ["subscriptionId"], "members": {"createSqs": {"type": "boolean"}, "httpsApiKeyName": {}, "httpsApiKeyValue": {}, "httpsMethod": {}, "roleArn": {}, "subscriptionEndpoint": {}, "subscriptionId": {"location": "uri", "locationName": "subscriptionId"}}}, "output": {"type": "structure", "members": {"queueArn": {}}}}}, "shapes": {"S2": {"type": "map", "key": {}, "value": {"shape": "S4"}}, "S4": {"type": "map", "key": {}, "value": {"type": "list", "member": {}}}, "S6": {"type": "list", "member": {}}, "S8": {"type": "list", "member": {}}, "Sb": {"type": "list", "member": {}}, "Sj": {"type": "map", "key": {}, "value": {"type": "structure", "members": {"encryptionKey": {}, "replicationDestinationRegions": {"shape": "Sm"}, "replicationRoleArn": {}, "retentionSettings": {"shape": "Sn"}, "tagsMap": {"shape": "<PERSON>"}}}}, "Sm": {"type": "list", "member": {}}, "Sn": {"type": "list", "member": {"type": "structure", "members": {"retentionPeriod": {"type": "integer"}, "storageClass": {}}}}, "Sr": {"type": "map", "key": {}, "value": {}}, "Sv": {"type": "list", "member": {"type": "structure", "required": ["region", "sources"], "members": {"region": {}, "sources": {"type": "list", "member": {}}}}}, "S16": {"type": "list", "member": {}}, "S18": {"type": "list", "member": {"type": "structure", "members": {"awsSourceType": {}, "customSourceType": {}}, "union": true}}, "S2n": {"type": "structure", "required": ["accountId", "sourceTypes", "subscriptionId"], "members": {"accessTypes": {"shape": "S16"}, "accountId": {}, "createdAt": {"shape": "S2o"}, "externalId": {}, "roleArn": {}, "s3BucketArn": {}, "snsArn": {}, "sourceTypes": {"shape": "S18"}, "subscriberDescription": {}, "subscriberName": {}, "subscriptionEndpoint": {}, "subscriptionId": {}, "subscriptionProtocol": {}, "subscriptionStatus": {}, "updatedAt": {"shape": "S2o"}}}, "S2o": {"type": "timestamp", "timestampFormat": "iso8601"}}}