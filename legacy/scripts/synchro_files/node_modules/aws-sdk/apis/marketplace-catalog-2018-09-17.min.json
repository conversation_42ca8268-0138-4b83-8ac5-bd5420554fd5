{"version": "2.0", "metadata": {"apiVersion": "2018-09-17", "endpointPrefix": "catalog.marketplace", "jsonVersion": "1.1", "protocol": "rest-json", "serviceAbbreviation": "AWS Marketplace Catalog", "serviceFullName": "AWS Marketplace Catalog Service", "serviceId": "Marketplace Catalog", "signatureVersion": "v4", "signingName": "aws-marketplace", "uid": "marketplace-catalog-2018-09-17"}, "operations": {"CancelChangeSet": {"http": {"method": "PATCH", "requestUri": "/CancelChangeSet"}, "input": {"type": "structure", "required": ["Catalog", "ChangeSetId"], "members": {"Catalog": {"location": "querystring", "locationName": "catalog"}, "ChangeSetId": {"location": "querystring", "locationName": "changeSetId"}}}, "output": {"type": "structure", "members": {"ChangeSetId": {}, "ChangeSetArn": {}}}}, "DescribeChangeSet": {"http": {"method": "GET", "requestUri": "/DescribeChangeSet"}, "input": {"type": "structure", "required": ["Catalog", "ChangeSetId"], "members": {"Catalog": {"location": "querystring", "locationName": "catalog"}, "ChangeSetId": {"location": "querystring", "locationName": "changeSetId"}}}, "output": {"type": "structure", "members": {"ChangeSetId": {}, "ChangeSetArn": {}, "ChangeSetName": {}, "StartTime": {}, "EndTime": {}, "Status": {}, "FailureCode": {}, "FailureDescription": {}, "ChangeSet": {"type": "list", "member": {"type": "structure", "members": {"ChangeType": {}, "Entity": {"shape": "Sg"}, "Details": {}, "ErrorDetailList": {"type": "list", "member": {"type": "structure", "members": {"ErrorCode": {}, "ErrorMessage": {}}}}, "ChangeName": {}}}}}}}, "DescribeEntity": {"http": {"method": "GET", "requestUri": "/DescribeEntity"}, "input": {"type": "structure", "required": ["Catalog", "EntityId"], "members": {"Catalog": {"location": "querystring", "locationName": "catalog"}, "EntityId": {"location": "querystring", "locationName": "entityId"}}}, "output": {"type": "structure", "members": {"EntityType": {}, "EntityIdentifier": {}, "EntityArn": {}, "LastModifiedDate": {}, "Details": {}}}}, "ListChangeSets": {"http": {"requestUri": "/ListChangeSets"}, "input": {"type": "structure", "required": ["Catalog"], "members": {"Catalog": {}, "FilterList": {"shape": "<PERSON>"}, "Sort": {"shape": "Sw"}, "MaxResults": {"type": "integer"}, "NextToken": {}}}, "output": {"type": "structure", "members": {"ChangeSetSummaryList": {"type": "list", "member": {"type": "structure", "members": {"ChangeSetId": {}, "ChangeSetArn": {}, "ChangeSetName": {}, "StartTime": {}, "EndTime": {}, "Status": {}, "EntityIdList": {"type": "list", "member": {}}, "FailureCode": {}}}}, "NextToken": {}}}}, "ListEntities": {"http": {"requestUri": "/ListEntities"}, "input": {"type": "structure", "required": ["Catalog", "EntityType"], "members": {"Catalog": {}, "EntityType": {}, "FilterList": {"shape": "<PERSON>"}, "Sort": {"shape": "Sw"}, "NextToken": {}, "MaxResults": {"type": "integer"}}}, "output": {"type": "structure", "members": {"EntitySummaryList": {"type": "list", "member": {"type": "structure", "members": {"Name": {}, "EntityType": {}, "EntityId": {}, "EntityArn": {}, "LastModifiedDate": {}, "Visibility": {}}}}, "NextToken": {}}}}, "ListTagsForResource": {"http": {"requestUri": "/ListTagsForResource"}, "input": {"type": "structure", "required": ["ResourceArn"], "members": {"ResourceArn": {}}}, "output": {"type": "structure", "members": {"ResourceArn": {}, "Tags": {"shape": "S1e"}}}}, "StartChangeSet": {"http": {"requestUri": "/StartChangeSet"}, "input": {"type": "structure", "required": ["Catalog", "ChangeSet"], "members": {"Catalog": {}, "ChangeSet": {"type": "list", "member": {"type": "structure", "required": ["ChangeType", "Entity", "Details"], "members": {"ChangeType": {}, "Entity": {"shape": "Sg"}, "EntityTags": {"shape": "S1e"}, "Details": {}, "ChangeName": {}}}}, "ChangeSetName": {}, "ClientRequestToken": {"idempotencyToken": true}, "ChangeSetTags": {"shape": "S1e"}}}, "output": {"type": "structure", "members": {"ChangeSetId": {}, "ChangeSetArn": {}}}}, "TagResource": {"http": {"requestUri": "/TagResource"}, "input": {"type": "structure", "required": ["ResourceArn", "Tags"], "members": {"ResourceArn": {}, "Tags": {"shape": "S1e"}}}, "output": {"type": "structure", "members": {}}}, "UntagResource": {"http": {"requestUri": "/UntagResource"}, "input": {"type": "structure", "required": ["ResourceArn", "TagKeys"], "members": {"ResourceArn": {}, "TagKeys": {"type": "list", "member": {}}}}, "output": {"type": "structure", "members": {}}}}, "shapes": {"Sg": {"type": "structure", "required": ["Type"], "members": {"Type": {}, "Identifier": {}}}, "Sr": {"type": "list", "member": {"type": "structure", "members": {"Name": {}, "ValueList": {"type": "list", "member": {}}}}}, "Sw": {"type": "structure", "members": {"SortBy": {}, "SortOrder": {}}}, "S1e": {"type": "list", "member": {"type": "structure", "required": ["Key", "Value"], "members": {"Key": {}, "Value": {}}}}}}