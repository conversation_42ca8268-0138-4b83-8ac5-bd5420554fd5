{"version": "2.0", "metadata": {"apiVersion": "2014-03-28", "endpointPrefix": "logs", "jsonVersion": "1.1", "protocol": "json", "serviceFullName": "Amazon CloudWatch Logs", "serviceId": "CloudWatch Logs", "signatureVersion": "v4", "targetPrefix": "Logs_20140328", "uid": "logs-2014-03-28"}, "operations": {"AssociateKmsKey": {"input": {"type": "structure", "required": ["logGroupName", "kmsKeyId"], "members": {"logGroupName": {}, "kmsKeyId": {}}}}, "CancelExportTask": {"input": {"type": "structure", "required": ["taskId"], "members": {"taskId": {}}}}, "CreateExportTask": {"input": {"type": "structure", "required": ["logGroupName", "from", "to", "destination"], "members": {"taskName": {}, "logGroupName": {}, "logStreamNamePrefix": {}, "from": {"type": "long"}, "to": {"type": "long"}, "destination": {}, "destinationPrefix": {}}}, "output": {"type": "structure", "members": {"taskId": {}}}}, "CreateLogGroup": {"input": {"type": "structure", "required": ["logGroupName"], "members": {"logGroupName": {}, "kmsKeyId": {}, "tags": {"shape": "Se"}}}}, "CreateLogStream": {"input": {"type": "structure", "required": ["logGroupName", "logStreamName"], "members": {"logGroupName": {}, "logStreamName": {}}}}, "DeleteDataProtectionPolicy": {"input": {"type": "structure", "required": ["logGroupIdentifier"], "members": {"logGroupIdentifier": {}}}}, "DeleteDestination": {"input": {"type": "structure", "required": ["destinationName"], "members": {"destinationName": {}}}}, "DeleteLogGroup": {"input": {"type": "structure", "required": ["logGroupName"], "members": {"logGroupName": {}}}}, "DeleteLogStream": {"input": {"type": "structure", "required": ["logGroupName", "logStreamName"], "members": {"logGroupName": {}, "logStreamName": {}}}}, "DeleteMetricFilter": {"input": {"type": "structure", "required": ["logGroupName", "filterName"], "members": {"logGroupName": {}, "filterName": {}}}}, "DeleteQueryDefinition": {"input": {"type": "structure", "required": ["queryDefinitionId"], "members": {"queryDefinitionId": {}}}, "output": {"type": "structure", "members": {"success": {"type": "boolean"}}}}, "DeleteResourcePolicy": {"input": {"type": "structure", "members": {"policyName": {}}}}, "DeleteRetentionPolicy": {"input": {"type": "structure", "required": ["logGroupName"], "members": {"logGroupName": {}}}}, "DeleteSubscriptionFilter": {"input": {"type": "structure", "required": ["logGroupName", "filterName"], "members": {"logGroupName": {}, "filterName": {}}}}, "DescribeDestinations": {"input": {"type": "structure", "members": {"DestinationNamePrefix": {}, "nextToken": {}, "limit": {"type": "integer"}}}, "output": {"type": "structure", "members": {"destinations": {"type": "list", "member": {"shape": "S13"}}, "nextToken": {}}}}, "DescribeExportTasks": {"input": {"type": "structure", "members": {"taskId": {}, "statusCode": {}, "nextToken": {}, "limit": {"type": "integer"}}}, "output": {"type": "structure", "members": {"exportTasks": {"type": "list", "member": {"type": "structure", "members": {"taskId": {}, "taskName": {}, "logGroupName": {}, "from": {"type": "long"}, "to": {"type": "long"}, "destination": {}, "destinationPrefix": {}, "status": {"type": "structure", "members": {"code": {}, "message": {}}}, "executionInfo": {"type": "structure", "members": {"creationTime": {"type": "long"}, "completionTime": {"type": "long"}}}}}}, "nextToken": {}}}}, "DescribeLogGroups": {"input": {"type": "structure", "members": {"accountIdentifiers": {"type": "list", "member": {}}, "logGroupNamePrefix": {}, "logGroupNamePattern": {}, "nextToken": {}, "limit": {"type": "integer"}, "includeLinkedAccounts": {"type": "boolean"}}}, "output": {"type": "structure", "members": {"logGroups": {"type": "list", "member": {"type": "structure", "members": {"logGroupName": {}, "creationTime": {"type": "long"}, "retentionInDays": {"type": "integer"}, "metricFilterCount": {"type": "integer"}, "arn": {}, "storedBytes": {"type": "long"}, "kmsKeyId": {}, "dataProtectionStatus": {}}}}, "nextToken": {}}}}, "DescribeLogStreams": {"input": {"type": "structure", "members": {"logGroupName": {}, "logGroupIdentifier": {}, "logStreamNamePrefix": {}, "orderBy": {}, "descending": {"type": "boolean"}, "nextToken": {}, "limit": {"type": "integer"}}}, "output": {"type": "structure", "members": {"logStreams": {"type": "list", "member": {"type": "structure", "members": {"logStreamName": {}, "creationTime": {"type": "long"}, "firstEventTimestamp": {"type": "long"}, "lastEventTimestamp": {"type": "long"}, "lastIngestionTime": {"type": "long"}, "uploadSequenceToken": {}, "arn": {}, "storedBytes": {"deprecated": true, "deprecatedMessage": "Starting on June 17, 2019, this parameter will be deprecated for log streams, and will be reported as zero. This change applies only to log streams. The storedBytes parameter for log groups is not affected.", "type": "long"}}}}, "nextToken": {}}}}, "DescribeMetricFilters": {"input": {"type": "structure", "members": {"logGroupName": {}, "filterNamePrefix": {}, "nextToken": {}, "limit": {"type": "integer"}, "metricName": {}, "metricNamespace": {}}}, "output": {"type": "structure", "members": {"metricFilters": {"type": "list", "member": {"type": "structure", "members": {"filterName": {}, "filterPattern": {}, "metricTransformations": {"shape": "S26"}, "creationTime": {"type": "long"}, "logGroupName": {}}}}, "nextToken": {}}}}, "DescribeQueries": {"input": {"type": "structure", "members": {"logGroupName": {}, "status": {}, "maxResults": {"type": "integer"}, "nextToken": {}}}, "output": {"type": "structure", "members": {"queries": {"type": "list", "member": {"type": "structure", "members": {"queryId": {}, "queryString": {}, "status": {}, "createTime": {"type": "long"}, "logGroupName": {}}}}, "nextToken": {}}}}, "DescribeQueryDefinitions": {"input": {"type": "structure", "members": {"queryDefinitionNamePrefix": {}, "maxResults": {"type": "integer"}, "nextToken": {}}}, "output": {"type": "structure", "members": {"queryDefinitions": {"type": "list", "member": {"type": "structure", "members": {"queryDefinitionId": {}, "name": {}, "queryString": {}, "lastModified": {"type": "long"}, "logGroupNames": {"shape": "S2s"}}}}, "nextToken": {}}}}, "DescribeResourcePolicies": {"input": {"type": "structure", "members": {"nextToken": {}, "limit": {"type": "integer"}}}, "output": {"type": "structure", "members": {"resourcePolicies": {"type": "list", "member": {"shape": "S2w"}}, "nextToken": {}}}}, "DescribeSubscriptionFilters": {"input": {"type": "structure", "required": ["logGroupName"], "members": {"logGroupName": {}, "filterNamePrefix": {}, "nextToken": {}, "limit": {"type": "integer"}}}, "output": {"type": "structure", "members": {"subscriptionFilters": {"type": "list", "member": {"type": "structure", "members": {"filterName": {}, "logGroupName": {}, "filterPattern": {}, "destinationArn": {}, "roleArn": {}, "distribution": {}, "creationTime": {"type": "long"}}}}, "nextToken": {}}}}, "DisassociateKmsKey": {"input": {"type": "structure", "required": ["logGroupName"], "members": {"logGroupName": {}}}}, "FilterLogEvents": {"input": {"type": "structure", "members": {"logGroupName": {}, "logGroupIdentifier": {}, "logStreamNames": {"type": "list", "member": {}}, "logStreamNamePrefix": {}, "startTime": {"type": "long"}, "endTime": {"type": "long"}, "filterPattern": {}, "nextToken": {}, "limit": {"type": "integer"}, "interleaved": {"deprecated": true, "deprecatedMessage": "Starting on June 17, 2019, this parameter will be ignored and the value will be assumed to be true. The response from this operation will always interleave events from multiple log streams within a log group.", "type": "boolean"}, "unmask": {"type": "boolean"}}}, "output": {"type": "structure", "members": {"events": {"type": "list", "member": {"type": "structure", "members": {"logStreamName": {}, "timestamp": {"type": "long"}, "message": {}, "ingestionTime": {"type": "long"}, "eventId": {}}}}, "searchedLogStreams": {"type": "list", "member": {"type": "structure", "members": {"logStreamName": {}, "searchedCompletely": {"type": "boolean"}}}}, "nextToken": {}}}}, "GetDataProtectionPolicy": {"input": {"type": "structure", "required": ["logGroupIdentifier"], "members": {"logGroupIdentifier": {}}}, "output": {"type": "structure", "members": {"logGroupIdentifier": {}, "policyDocument": {}, "lastUpdatedTime": {"type": "long"}}}}, "GetLogEvents": {"input": {"type": "structure", "required": ["logStreamName"], "members": {"logGroupName": {}, "logGroupIdentifier": {}, "logStreamName": {}, "startTime": {"type": "long"}, "endTime": {"type": "long"}, "nextToken": {}, "limit": {"type": "integer"}, "startFromHead": {"type": "boolean"}, "unmask": {"type": "boolean"}}}, "output": {"type": "structure", "members": {"events": {"type": "list", "member": {"type": "structure", "members": {"timestamp": {"type": "long"}, "message": {}, "ingestionTime": {"type": "long"}}}}, "nextForwardToken": {}, "nextBackwardToken": {}}}}, "GetLogGroupFields": {"input": {"type": "structure", "members": {"logGroupName": {}, "time": {"type": "long"}, "logGroupIdentifier": {}}}, "output": {"type": "structure", "members": {"logGroupFields": {"type": "list", "member": {"type": "structure", "members": {"name": {}, "percent": {"type": "integer"}}}}}}}, "GetLogRecord": {"input": {"type": "structure", "required": ["logRecordPointer"], "members": {"logRecordPointer": {}, "unmask": {"type": "boolean"}}}, "output": {"type": "structure", "members": {"logRecord": {"type": "map", "key": {}, "value": {}}}}}, "GetQueryResults": {"input": {"type": "structure", "required": ["queryId"], "members": {"queryId": {}}}, "output": {"type": "structure", "members": {"results": {"type": "list", "member": {"type": "list", "member": {"type": "structure", "members": {"field": {}, "value": {}}}}}, "statistics": {"type": "structure", "members": {"recordsMatched": {"type": "double"}, "recordsScanned": {"type": "double"}, "bytesScanned": {"type": "double"}}}, "status": {}}}}, "ListTagsForResource": {"input": {"type": "structure", "required": ["resourceArn"], "members": {"resourceArn": {}}}, "output": {"type": "structure", "members": {"tags": {"shape": "Se"}}}}, "ListTagsLogGroup": {"input": {"type": "structure", "required": ["logGroupName"], "members": {"logGroupName": {}}, "deprecated": true, "deprecatedMessage": "Please use the generic tagging API model ListTagsForResourceRequest and ListTagsForResourceResponse"}, "output": {"type": "structure", "members": {"tags": {"shape": "Se"}}, "deprecated": true, "deprecatedMessage": "Please use the generic tagging API model ListTagsForResourceRequest and ListTagsForResourceResponse"}, "deprecated": true, "deprecatedMessage": "Please use the generic tagging API ListTagsForResource"}, "PutDataProtectionPolicy": {"input": {"type": "structure", "required": ["logGroupIdentifier", "policyDocument"], "members": {"logGroupIdentifier": {}, "policyDocument": {}}}, "output": {"type": "structure", "members": {"logGroupIdentifier": {}, "policyDocument": {}, "lastUpdatedTime": {"type": "long"}}}}, "PutDestination": {"input": {"type": "structure", "required": ["destinationName", "targetArn", "roleArn"], "members": {"destinationName": {}, "targetArn": {}, "roleArn": {}, "tags": {"shape": "Se"}}}, "output": {"type": "structure", "members": {"destination": {"shape": "S13"}}}}, "PutDestinationPolicy": {"input": {"type": "structure", "required": ["destinationName", "accessPolicy"], "members": {"destinationName": {}, "accessPolicy": {}, "forceUpdate": {"type": "boolean"}}}}, "PutLogEvents": {"input": {"type": "structure", "required": ["logGroupName", "logStreamName", "logEvents"], "members": {"logGroupName": {}, "logStreamName": {}, "logEvents": {"type": "list", "member": {"type": "structure", "required": ["timestamp", "message"], "members": {"timestamp": {"type": "long"}, "message": {}}}}, "sequenceToken": {}}}, "output": {"type": "structure", "members": {"nextSequenceToken": {}, "rejectedLogEventsInfo": {"type": "structure", "members": {"tooNewLogEventStartIndex": {"type": "integer"}, "tooOldLogEventEndIndex": {"type": "integer"}, "expiredLogEventEndIndex": {"type": "integer"}}}}}}, "PutMetricFilter": {"input": {"type": "structure", "required": ["logGroupName", "filterName", "filterPattern", "metricTransformations"], "members": {"logGroupName": {}, "filterName": {}, "filterPattern": {}, "metricTransformations": {"shape": "S26"}}}}, "PutQueryDefinition": {"input": {"type": "structure", "required": ["name", "queryString"], "members": {"name": {}, "queryDefinitionId": {}, "logGroupNames": {"shape": "S2s"}, "queryString": {}}}, "output": {"type": "structure", "members": {"queryDefinitionId": {}}}}, "PutResourcePolicy": {"input": {"type": "structure", "members": {"policyName": {}, "policyDocument": {}}}, "output": {"type": "structure", "members": {"resourcePolicy": {"shape": "S2w"}}}}, "PutRetentionPolicy": {"input": {"type": "structure", "required": ["logGroupName", "retentionInDays"], "members": {"logGroupName": {}, "retentionInDays": {"type": "integer"}}}}, "PutSubscriptionFilter": {"input": {"type": "structure", "required": ["logGroupName", "filterName", "filterPattern", "destinationArn"], "members": {"logGroupName": {}, "filterName": {}, "filterPattern": {}, "destinationArn": {}, "roleArn": {}, "distribution": {}}}}, "StartQuery": {"input": {"type": "structure", "required": ["startTime", "endTime", "queryString"], "members": {"logGroupName": {}, "logGroupNames": {"shape": "S2s"}, "logGroupIdentifiers": {"type": "list", "member": {}}, "startTime": {"type": "long"}, "endTime": {"type": "long"}, "queryString": {}, "limit": {"type": "integer"}}}, "output": {"type": "structure", "members": {"queryId": {}}}}, "StopQuery": {"input": {"type": "structure", "required": ["queryId"], "members": {"queryId": {}}}, "output": {"type": "structure", "members": {"success": {"type": "boolean"}}}}, "TagLogGroup": {"input": {"type": "structure", "required": ["logGroupName", "tags"], "members": {"logGroupName": {}, "tags": {"shape": "Se"}}, "deprecated": true, "deprecatedMessage": "Please use the generic tagging API model TagResourceRequest"}, "deprecated": true, "deprecatedMessage": "Please use the generic tagging API TagResource"}, "TagResource": {"input": {"type": "structure", "required": ["resourceArn", "tags"], "members": {"resourceArn": {}, "tags": {"shape": "Se"}}}}, "TestMetricFilter": {"input": {"type": "structure", "required": ["filterPattern", "logEventMessages"], "members": {"filterPattern": {}, "logEventMessages": {"type": "list", "member": {}}}}, "output": {"type": "structure", "members": {"matches": {"type": "list", "member": {"type": "structure", "members": {"eventNumber": {"type": "long"}, "eventMessage": {}, "extractedValues": {"type": "map", "key": {}, "value": {}}}}}}}}, "UntagLogGroup": {"input": {"type": "structure", "required": ["logGroupName", "tags"], "members": {"logGroupName": {}, "tags": {"type": "list", "member": {}}}, "deprecated": true, "deprecatedMessage": "Please use the generic tagging API model UntagResourceRequest"}, "deprecated": true, "deprecatedMessage": "Please use the generic tagging API UntagResource"}, "UntagResource": {"input": {"type": "structure", "required": ["resourceArn", "tagKeys"], "members": {"resourceArn": {}, "tagKeys": {"type": "list", "member": {}}}}}}, "shapes": {"Se": {"type": "map", "key": {}, "value": {}}, "S13": {"type": "structure", "members": {"destinationName": {}, "targetArn": {}, "roleArn": {}, "accessPolicy": {}, "arn": {}, "creationTime": {"type": "long"}}}, "S26": {"type": "list", "member": {"type": "structure", "required": ["metricName", "metricNamespace", "metricValue"], "members": {"metricName": {}, "metricNamespace": {}, "metricValue": {}, "defaultValue": {"type": "double"}, "dimensions": {"type": "map", "key": {}, "value": {}}, "unit": {}}}}, "S2s": {"type": "list", "member": {}}, "S2w": {"type": "structure", "members": {"policyName": {}, "policyDocument": {}, "lastUpdatedTime": {"type": "long"}}}}}