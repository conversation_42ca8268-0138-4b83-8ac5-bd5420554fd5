{"version": "2.0", "metadata": {"apiVersion": "2022-11-28", "endpointPrefix": "omics", "jsonVersion": "1.1", "protocol": "rest-json", "serviceFullName": "Amazon Omics", "serviceId": "Omics", "signatureVersion": "v4", "signingName": "omics", "uid": "omics-2022-11-28"}, "operations": {"BatchDeleteReadSet": {"http": {"requestUri": "/sequencestore/{sequenceStoreId}/readset/batch/delete", "responseCode": 200}, "input": {"type": "structure", "required": ["ids", "sequenceStoreId"], "members": {"ids": {"type": "list", "member": {}}, "sequenceStoreId": {"location": "uri", "locationName": "sequenceStoreId"}}}, "output": {"type": "structure", "members": {"errors": {"type": "list", "member": {"type": "structure", "required": ["code", "id", "message"], "members": {"code": {}, "id": {}, "message": {}}}}}}, "endpoint": {"hostPrefix": "control-storage-"}, "idempotent": true}, "CancelAnnotationImportJob": {"http": {"method": "DELETE", "requestUri": "/import/annotation/{jobId}", "responseCode": 200}, "input": {"type": "structure", "required": ["jobId"], "members": {"jobId": {"location": "uri", "locationName": "jobId"}}}, "output": {"type": "structure", "members": {}}, "endpoint": {"hostPrefix": "analytics-"}, "idempotent": true}, "CancelRun": {"http": {"requestUri": "/run/{id}/cancel", "responseCode": 202}, "input": {"type": "structure", "required": ["id"], "members": {"id": {"location": "uri", "locationName": "id"}}}, "endpoint": {"hostPrefix": "workflows-"}}, "CancelVariantImportJob": {"http": {"method": "DELETE", "requestUri": "/import/variant/{jobId}", "responseCode": 200}, "input": {"type": "structure", "required": ["jobId"], "members": {"jobId": {"location": "uri", "locationName": "jobId"}}}, "output": {"type": "structure", "members": {}}, "endpoint": {"hostPrefix": "analytics-"}, "idempotent": true}, "CreateAnnotationStore": {"http": {"requestUri": "/annotationStore", "responseCode": 200}, "input": {"type": "structure", "required": ["storeFormat"], "members": {"description": {}, "name": {}, "reference": {"shape": "Sj"}, "sseConfig": {"shape": "Sl"}, "storeFormat": {}, "storeOptions": {"shape": "Sp"}, "tags": {"shape": "Sy"}}}, "output": {"type": "structure", "required": ["creationTime", "id", "name", "status"], "members": {"creationTime": {"shape": "S12"}, "id": {}, "name": {}, "reference": {"shape": "Sj"}, "status": {}, "storeFormat": {}, "storeOptions": {"shape": "Sp"}}}, "endpoint": {"hostPrefix": "analytics-"}}, "CreateReferenceStore": {"http": {"requestUri": "/referencestore", "responseCode": 200}, "input": {"type": "structure", "required": ["name"], "members": {"clientToken": {}, "description": {}, "name": {}, "sseConfig": {"shape": "Sl"}, "tags": {"shape": "Sy"}}}, "output": {"type": "structure", "required": ["arn", "creationTime", "id"], "members": {"arn": {}, "creationTime": {"shape": "S1a"}, "description": {}, "id": {}, "name": {}, "sseConfig": {"shape": "Sl"}}}, "endpoint": {"hostPrefix": "control-storage-"}}, "CreateRunGroup": {"http": {"requestUri": "/runGroup", "responseCode": 201}, "input": {"type": "structure", "required": ["requestId"], "members": {"maxCpus": {"type": "integer"}, "maxDuration": {"type": "integer"}, "maxRuns": {"type": "integer"}, "name": {}, "requestId": {"idempotencyToken": true}, "tags": {"shape": "Sy"}}}, "output": {"type": "structure", "members": {"arn": {}, "id": {}, "tags": {"shape": "Sy"}}}, "endpoint": {"hostPrefix": "workflows-"}}, "CreateSequenceStore": {"http": {"requestUri": "/sequencestore", "responseCode": 200}, "input": {"type": "structure", "required": ["name"], "members": {"clientToken": {}, "description": {}, "name": {}, "sseConfig": {"shape": "Sl"}, "tags": {"shape": "Sy"}}}, "output": {"type": "structure", "required": ["arn", "creationTime", "id"], "members": {"arn": {}, "creationTime": {"shape": "S1a"}, "description": {}, "id": {}, "name": {}, "sseConfig": {"shape": "Sl"}}}, "endpoint": {"hostPrefix": "control-storage-"}}, "CreateVariantStore": {"http": {"requestUri": "/variantStore", "responseCode": 200}, "input": {"type": "structure", "required": ["reference"], "members": {"description": {}, "name": {}, "reference": {"shape": "Sj"}, "sseConfig": {"shape": "Sl"}, "tags": {"shape": "Sy"}}}, "output": {"type": "structure", "required": ["creationTime", "id", "name", "status"], "members": {"creationTime": {"shape": "S12"}, "id": {}, "name": {}, "reference": {"shape": "Sj"}, "status": {}}}, "endpoint": {"hostPrefix": "analytics-"}}, "CreateWorkflow": {"http": {"requestUri": "/workflow", "responseCode": 201}, "input": {"type": "structure", "required": ["requestId"], "members": {"definitionUri": {}, "definitionZip": {"type": "blob"}, "description": {}, "engine": {}, "main": {}, "name": {}, "parameterTemplate": {"shape": "S20"}, "requestId": {"idempotencyToken": true}, "storageCapacity": {"type": "integer"}, "tags": {"shape": "Sy"}}}, "output": {"type": "structure", "members": {"arn": {}, "id": {}, "status": {}, "tags": {"shape": "Sy"}}}, "endpoint": {"hostPrefix": "workflows-"}}, "DeleteAnnotationStore": {"http": {"method": "DELETE", "requestUri": "/annotationStore/{name}", "responseCode": 200}, "input": {"type": "structure", "required": ["name"], "members": {"force": {"location": "querystring", "locationName": "force", "type": "boolean"}, "name": {"location": "uri", "locationName": "name"}}}, "output": {"type": "structure", "required": ["status"], "members": {"status": {}}}, "endpoint": {"hostPrefix": "analytics-"}, "idempotent": true}, "DeleteReference": {"http": {"method": "DELETE", "requestUri": "/referencestore/{referenceStoreId}/reference/{id}", "responseCode": 200}, "input": {"type": "structure", "required": ["id", "referenceStoreId"], "members": {"id": {"location": "uri", "locationName": "id"}, "referenceStoreId": {"location": "uri", "locationName": "referenceStoreId"}}}, "output": {"type": "structure", "members": {}}, "endpoint": {"hostPrefix": "control-storage-"}, "idempotent": true}, "DeleteReferenceStore": {"http": {"method": "DELETE", "requestUri": "/referencestore/{id}", "responseCode": 200}, "input": {"type": "structure", "required": ["id"], "members": {"id": {"location": "uri", "locationName": "id"}}}, "output": {"type": "structure", "members": {}}, "endpoint": {"hostPrefix": "control-storage-"}, "idempotent": true}, "DeleteRun": {"http": {"method": "DELETE", "requestUri": "/run/{id}", "responseCode": 202}, "input": {"type": "structure", "required": ["id"], "members": {"id": {"location": "uri", "locationName": "id"}}}, "endpoint": {"hostPrefix": "workflows-"}, "idempotent": true}, "DeleteRunGroup": {"http": {"method": "DELETE", "requestUri": "/runGroup/{id}", "responseCode": 202}, "input": {"type": "structure", "required": ["id"], "members": {"id": {"location": "uri", "locationName": "id"}}}, "endpoint": {"hostPrefix": "workflows-"}, "idempotent": true}, "DeleteSequenceStore": {"http": {"method": "DELETE", "requestUri": "/sequencestore/{id}", "responseCode": 200}, "input": {"type": "structure", "required": ["id"], "members": {"id": {"location": "uri", "locationName": "id"}}}, "output": {"type": "structure", "members": {}}, "endpoint": {"hostPrefix": "control-storage-"}, "idempotent": true}, "DeleteVariantStore": {"http": {"method": "DELETE", "requestUri": "/variantStore/{name}", "responseCode": 200}, "input": {"type": "structure", "required": ["name"], "members": {"force": {"location": "querystring", "locationName": "force", "type": "boolean"}, "name": {"location": "uri", "locationName": "name"}}}, "output": {"type": "structure", "required": ["status"], "members": {"status": {}}}, "endpoint": {"hostPrefix": "analytics-"}, "idempotent": true}, "DeleteWorkflow": {"http": {"method": "DELETE", "requestUri": "/workflow/{id}", "responseCode": 202}, "input": {"type": "structure", "required": ["id"], "members": {"id": {"location": "uri", "locationName": "id"}}}, "endpoint": {"hostPrefix": "workflows-"}, "idempotent": true}, "GetAnnotationImportJob": {"http": {"method": "GET", "requestUri": "/import/annotation/{jobId}", "responseCode": 200}, "input": {"type": "structure", "required": ["jobId"], "members": {"jobId": {"location": "uri", "locationName": "jobId"}}}, "output": {"type": "structure", "required": ["completionTime", "creationTime", "destinationName", "formatOptions", "id", "items", "roleArn", "runLeftNormalization", "status", "statusMessage", "updateTime"], "members": {"completionTime": {"shape": "S2s"}, "creationTime": {"shape": "S12"}, "destinationName": {}, "formatOptions": {"shape": "S2u"}, "id": {}, "items": {"type": "list", "member": {"type": "structure", "required": ["jobStatus", "source"], "members": {"jobStatus": {}, "source": {}}}}, "roleArn": {}, "runLeftNormalization": {"type": "boolean"}, "status": {}, "statusMessage": {}, "updateTime": {"shape": "S3e"}}}, "endpoint": {"hostPrefix": "analytics-"}}, "GetAnnotationStore": {"http": {"method": "GET", "requestUri": "/annotationStore/{name}", "responseCode": 200}, "input": {"type": "structure", "required": ["name"], "members": {"name": {"location": "uri", "locationName": "name"}}}, "output": {"type": "structure", "required": ["creationTime", "description", "id", "name", "reference", "sseConfig", "status", "statusMessage", "storeArn", "storeSizeBytes", "tags", "updateTime"], "members": {"creationTime": {"shape": "S12"}, "description": {}, "id": {}, "name": {}, "reference": {"shape": "Sj"}, "sseConfig": {"shape": "Sl"}, "status": {}, "statusMessage": {}, "storeArn": {}, "storeFormat": {}, "storeOptions": {"shape": "Sp"}, "storeSizeBytes": {"type": "long"}, "tags": {"shape": "Sy"}, "updateTime": {"shape": "S3e"}}}, "endpoint": {"hostPrefix": "analytics-"}}, "GetReadSet": {"http": {"method": "GET", "requestUri": "/sequencestore/{sequenceStoreId}/readset/{id}", "responseCode": 200}, "input": {"type": "structure", "required": ["id", "partNumber", "sequenceStoreId"], "members": {"file": {"location": "querystring", "locationName": "file"}, "id": {"location": "uri", "locationName": "id"}, "partNumber": {"location": "querystring", "locationName": "partNumber", "type": "integer"}, "sequenceStoreId": {"location": "uri", "locationName": "sequenceStoreId"}}}, "output": {"type": "structure", "members": {"payload": {"type": "blob", "streaming": true}}, "payload": "payload"}, "endpoint": {"hostPrefix": "storage-"}}, "GetReadSetActivationJob": {"http": {"method": "GET", "requestUri": "/sequencestore/{sequenceStoreId}/activationjob/{id}", "responseCode": 200}, "input": {"type": "structure", "required": ["id", "sequenceStoreId"], "members": {"id": {"location": "uri", "locationName": "id"}, "sequenceStoreId": {"location": "uri", "locationName": "sequenceStoreId"}}}, "output": {"type": "structure", "required": ["creationTime", "id", "sequenceStoreId", "status"], "members": {"completionTime": {"shape": "S1a"}, "creationTime": {"shape": "S1a"}, "id": {}, "sequenceStoreId": {}, "sources": {"type": "list", "member": {"type": "structure", "required": ["readSetId", "status"], "members": {"readSetId": {}, "status": {}, "statusMessage": {}}}}, "status": {}, "statusMessage": {}}}, "endpoint": {"hostPrefix": "control-storage-"}}, "GetReadSetExportJob": {"http": {"method": "GET", "requestUri": "/sequencestore/{sequenceStoreId}/exportjob/{id}", "responseCode": 200}, "input": {"type": "structure", "required": ["id", "sequenceStoreId"], "members": {"id": {"location": "uri", "locationName": "id"}, "sequenceStoreId": {"location": "uri", "locationName": "sequenceStoreId"}}}, "output": {"type": "structure", "required": ["creationTime", "destination", "id", "sequenceStoreId", "status"], "members": {"completionTime": {"shape": "S1a"}, "creationTime": {"shape": "S1a"}, "destination": {}, "id": {}, "readSets": {"type": "list", "member": {"type": "structure", "required": ["id", "status"], "members": {"id": {}, "status": {}, "statusMessage": {}}}}, "sequenceStoreId": {}, "status": {}, "statusMessage": {}}}, "endpoint": {"hostPrefix": "control-storage-"}}, "GetReadSetImportJob": {"http": {"method": "GET", "requestUri": "/sequencestore/{sequenceStoreId}/importjob/{id}", "responseCode": 200}, "input": {"type": "structure", "required": ["id", "sequenceStoreId"], "members": {"id": {"location": "uri", "locationName": "id"}, "sequenceStoreId": {"location": "uri", "locationName": "sequenceStoreId"}}}, "output": {"type": "structure", "required": ["creationTime", "id", "roleArn", "sequenceStoreId", "sources", "status"], "members": {"completionTime": {"shape": "S1a"}, "creationTime": {"shape": "S1a"}, "id": {}, "roleArn": {}, "sequenceStoreId": {}, "sources": {"type": "list", "member": {"type": "structure", "required": ["sampleId", "sourceFileType", "sourceFiles", "status", "subjectId"], "members": {"description": {}, "generatedFrom": {}, "name": {}, "referenceArn": {}, "sampleId": {}, "sourceFileType": {}, "sourceFiles": {"shape": "S4f"}, "status": {}, "statusMessage": {}, "subjectId": {}, "tags": {"shape": "Sy"}}}}, "status": {}, "statusMessage": {}}}, "endpoint": {"hostPrefix": "control-storage-"}}, "GetReadSetMetadata": {"http": {"method": "GET", "requestUri": "/sequencestore/{sequenceStoreId}/readset/{id}/metadata", "responseCode": 200}, "input": {"type": "structure", "required": ["id", "sequenceStoreId"], "members": {"id": {"location": "uri", "locationName": "id"}, "sequenceStoreId": {"location": "uri", "locationName": "sequenceStoreId"}}}, "output": {"type": "structure", "required": ["arn", "creationTime", "fileType", "id", "sequenceStoreId", "status"], "members": {"arn": {}, "creationTime": {"shape": "S1a"}, "description": {}, "fileType": {}, "files": {"type": "structure", "members": {"index": {"shape": "S4n"}, "source1": {"shape": "S4n"}, "source2": {"shape": "S4n"}}}, "id": {}, "name": {}, "referenceArn": {}, "sampleId": {}, "sequenceInformation": {"shape": "S4r"}, "sequenceStoreId": {}, "status": {}, "subjectId": {}}}, "endpoint": {"hostPrefix": "control-storage-"}}, "GetReference": {"http": {"method": "GET", "requestUri": "/referencestore/{referenceStoreId}/reference/{id}", "responseCode": 200}, "input": {"type": "structure", "required": ["id", "partNumber", "referenceStoreId"], "members": {"file": {"location": "querystring", "locationName": "file"}, "id": {"location": "uri", "locationName": "id"}, "partNumber": {"location": "querystring", "locationName": "partNumber", "type": "integer"}, "range": {"location": "header", "locationName": "Range"}, "referenceStoreId": {"location": "uri", "locationName": "referenceStoreId"}}}, "output": {"type": "structure", "members": {"payload": {"type": "blob", "streaming": true}}, "payload": "payload"}, "endpoint": {"hostPrefix": "storage-"}}, "GetReferenceImportJob": {"http": {"method": "GET", "requestUri": "/referencestore/{referenceStoreId}/importjob/{id}", "responseCode": 200}, "input": {"type": "structure", "required": ["id", "referenceStoreId"], "members": {"id": {"location": "uri", "locationName": "id"}, "referenceStoreId": {"location": "uri", "locationName": "referenceStoreId"}}}, "output": {"type": "structure", "required": ["creationTime", "id", "referenceStoreId", "roleArn", "sources", "status"], "members": {"completionTime": {"shape": "S1a"}, "creationTime": {"shape": "S1a"}, "id": {}, "referenceStoreId": {}, "roleArn": {}, "sources": {"type": "list", "member": {"type": "structure", "required": ["status"], "members": {"description": {}, "name": {}, "sourceFile": {}, "status": {}, "statusMessage": {}, "tags": {"shape": "Sy"}}}}, "status": {}, "statusMessage": {}}}, "endpoint": {"hostPrefix": "control-storage-"}}, "GetReferenceMetadata": {"http": {"method": "GET", "requestUri": "/referencestore/{referenceStoreId}/reference/{id}/metadata", "responseCode": 200}, "input": {"type": "structure", "required": ["id", "referenceStoreId"], "members": {"id": {"location": "uri", "locationName": "id"}, "referenceStoreId": {"location": "uri", "locationName": "referenceStoreId"}}}, "output": {"type": "structure", "required": ["arn", "creationTime", "id", "md5", "referenceStoreId", "updateTime"], "members": {"arn": {}, "creationTime": {"shape": "S1a"}, "description": {}, "files": {"type": "structure", "members": {"index": {"shape": "S4n"}, "source": {"shape": "S4n"}}}, "id": {}, "md5": {}, "name": {}, "referenceStoreId": {}, "status": {}, "updateTime": {"shape": "S1a"}}}, "endpoint": {"hostPrefix": "control-storage-"}}, "GetReferenceStore": {"http": {"method": "GET", "requestUri": "/referencestore/{id}", "responseCode": 200}, "input": {"type": "structure", "required": ["id"], "members": {"id": {"location": "uri", "locationName": "id"}}}, "output": {"type": "structure", "required": ["arn", "creationTime", "id"], "members": {"arn": {}, "creationTime": {"shape": "S1a"}, "description": {}, "id": {}, "name": {}, "sseConfig": {"shape": "Sl"}}}, "endpoint": {"hostPrefix": "control-storage-"}}, "GetRun": {"http": {"method": "GET", "requestUri": "/run/{id}", "responseCode": 200}, "input": {"type": "structure", "required": ["id"], "members": {"export": {"location": "querystring", "locationName": "export", "type": "list", "member": {}}, "id": {"location": "uri", "locationName": "id"}}}, "output": {"type": "structure", "members": {"arn": {}, "creationTime": {"shape": "S5j"}, "definition": {}, "digest": {}, "id": {}, "logLevel": {}, "name": {}, "outputUri": {}, "parameters": {"shape": "S5o"}, "priority": {"type": "integer"}, "resourceDigests": {"type": "map", "key": {}, "value": {}}, "roleArn": {}, "runGroupId": {}, "runId": {}, "startTime": {"shape": "S5j"}, "startedBy": {}, "status": {}, "statusMessage": {}, "stopTime": {"shape": "S5j"}, "storageCapacity": {"type": "integer"}, "tags": {"shape": "Sy"}, "workflowId": {}, "workflowType": {}}}, "endpoint": {"hostPrefix": "workflows-"}}, "GetRunGroup": {"http": {"method": "GET", "requestUri": "/runGroup/{id}", "responseCode": 200}, "input": {"type": "structure", "required": ["id"], "members": {"id": {"location": "uri", "locationName": "id"}}}, "output": {"type": "structure", "members": {"arn": {}, "creationTime": {"shape": "S61"}, "id": {}, "maxCpus": {"type": "integer"}, "maxDuration": {"type": "integer"}, "maxRuns": {"type": "integer"}, "name": {}, "tags": {"shape": "Sy"}}}, "endpoint": {"hostPrefix": "workflows-"}}, "GetRunTask": {"http": {"method": "GET", "requestUri": "/run/{id}/task/{taskId}", "responseCode": 200}, "input": {"type": "structure", "required": ["id", "taskId"], "members": {"id": {"location": "uri", "locationName": "id"}, "taskId": {"location": "uri", "locationName": "taskId"}}}, "output": {"type": "structure", "members": {"cpus": {"type": "integer"}, "creationTime": {"shape": "S69"}, "logStream": {}, "memory": {"type": "integer"}, "name": {}, "startTime": {"shape": "S69"}, "status": {}, "statusMessage": {}, "stopTime": {"shape": "S69"}, "taskId": {}}}, "endpoint": {"hostPrefix": "workflows-"}}, "GetSequenceStore": {"http": {"method": "GET", "requestUri": "/sequencestore/{id}", "responseCode": 200}, "input": {"type": "structure", "required": ["id"], "members": {"id": {"location": "uri", "locationName": "id"}}}, "output": {"type": "structure", "required": ["arn", "creationTime", "id"], "members": {"arn": {}, "creationTime": {"shape": "S1a"}, "description": {}, "id": {}, "name": {}, "sseConfig": {"shape": "Sl"}}}, "endpoint": {"hostPrefix": "control-storage-"}}, "GetVariantImportJob": {"http": {"method": "GET", "requestUri": "/import/variant/{jobId}", "responseCode": 200}, "input": {"type": "structure", "required": ["jobId"], "members": {"jobId": {"location": "uri", "locationName": "jobId"}}}, "output": {"type": "structure", "required": ["creationTime", "destinationName", "id", "items", "roleArn", "runLeftNormalization", "status", "statusMessage", "updateTime"], "members": {"completionTime": {"shape": "S2s"}, "creationTime": {"shape": "S12"}, "destinationName": {}, "id": {}, "items": {"type": "list", "member": {"type": "structure", "required": ["jobStatus", "source"], "members": {"jobStatus": {}, "source": {}}}}, "roleArn": {}, "runLeftNormalization": {"type": "boolean"}, "status": {}, "statusMessage": {}, "updateTime": {"shape": "S3e"}}}, "endpoint": {"hostPrefix": "analytics-"}}, "GetVariantStore": {"http": {"method": "GET", "requestUri": "/variantStore/{name}", "responseCode": 200}, "input": {"type": "structure", "required": ["name"], "members": {"name": {"location": "uri", "locationName": "name"}}}, "output": {"type": "structure", "required": ["creationTime", "description", "id", "name", "reference", "sseConfig", "status", "statusMessage", "storeArn", "storeSizeBytes", "tags", "updateTime"], "members": {"creationTime": {"shape": "S12"}, "description": {}, "id": {}, "name": {}, "reference": {"shape": "Sj"}, "sseConfig": {"shape": "Sl"}, "status": {}, "statusMessage": {}, "storeArn": {}, "storeSizeBytes": {"type": "long"}, "tags": {"shape": "Sy"}, "updateTime": {"shape": "S3e"}}}, "endpoint": {"hostPrefix": "analytics-"}}, "GetWorkflow": {"http": {"method": "GET", "requestUri": "/workflow/{id}", "responseCode": 200}, "input": {"type": "structure", "required": ["id"], "members": {"export": {"location": "querystring", "locationName": "export", "type": "list", "member": {}}, "id": {"location": "uri", "locationName": "id"}, "type": {"location": "querystring", "locationName": "type"}}}, "output": {"type": "structure", "members": {"arn": {}, "creationTime": {"shape": "S6r"}, "definition": {}, "description": {}, "digest": {}, "engine": {}, "id": {}, "main": {}, "name": {}, "parameterTemplate": {"shape": "S20"}, "status": {}, "statusMessage": {}, "storageCapacity": {"type": "integer"}, "tags": {"shape": "Sy"}, "type": {}}}, "endpoint": {"hostPrefix": "workflows-"}}, "ListAnnotationImportJobs": {"http": {"requestUri": "/import/annotations", "responseCode": 200}, "input": {"type": "structure", "members": {"filter": {"type": "structure", "members": {"status": {}, "storeName": {}}}, "ids": {"type": "list", "member": {}}, "maxResults": {"location": "querystring", "locationName": "maxResults", "type": "integer"}, "nextToken": {"location": "querystring", "locationName": "nextToken"}}}, "output": {"type": "structure", "members": {"annotationImportJobs": {"type": "list", "member": {"type": "structure", "required": ["creationTime", "destinationName", "id", "roleArn", "status", "updateTime"], "members": {"completionTime": {"shape": "S2s"}, "creationTime": {"shape": "S12"}, "destinationName": {}, "id": {}, "roleArn": {}, "runLeftNormalization": {"type": "boolean"}, "status": {}, "updateTime": {"shape": "S3e"}}}}, "nextToken": {}}}, "endpoint": {"hostPrefix": "analytics-"}}, "ListAnnotationStores": {"http": {"requestUri": "/annotationStores", "responseCode": 200}, "input": {"type": "structure", "members": {"filter": {"type": "structure", "members": {"status": {}}}, "ids": {"type": "list", "member": {}}, "maxResults": {"location": "querystring", "locationName": "maxResults", "type": "integer"}, "nextToken": {"location": "querystring", "locationName": "nextToken"}}}, "output": {"type": "structure", "members": {"annotationStores": {"type": "list", "member": {"type": "structure", "required": ["creationTime", "description", "id", "name", "reference", "sseConfig", "status", "statusMessage", "storeArn", "storeFormat", "storeSizeBytes", "updateTime"], "members": {"creationTime": {"shape": "S12"}, "description": {}, "id": {}, "name": {}, "reference": {"shape": "Sj"}, "sseConfig": {"shape": "Sl"}, "status": {}, "statusMessage": {}, "storeArn": {}, "storeFormat": {}, "storeSizeBytes": {"type": "long"}, "updateTime": {"shape": "S3e"}}}}, "nextToken": {}}}, "endpoint": {"hostPrefix": "analytics-"}}, "ListReadSetActivationJobs": {"http": {"requestUri": "/sequencestore/{sequenceStoreId}/activationjobs", "responseCode": 200}, "input": {"type": "structure", "required": ["sequenceStoreId"], "members": {"filter": {"type": "structure", "members": {"createdAfter": {"shape": "S1a"}, "createdBefore": {"shape": "S1a"}, "status": {}}}, "maxResults": {"location": "querystring", "locationName": "maxResults", "type": "integer"}, "nextToken": {"location": "querystring", "locationName": "nextToken"}, "sequenceStoreId": {"location": "uri", "locationName": "sequenceStoreId"}}}, "output": {"type": "structure", "members": {"activationJobs": {"type": "list", "member": {"type": "structure", "required": ["creationTime", "id", "sequenceStoreId", "status"], "members": {"completionTime": {"shape": "S1a"}, "creationTime": {"shape": "S1a"}, "id": {}, "sequenceStoreId": {}, "status": {}}}}, "nextToken": {}}}, "endpoint": {"hostPrefix": "control-storage-"}}, "ListReadSetExportJobs": {"http": {"requestUri": "/sequencestore/{sequenceStoreId}/exportjobs", "responseCode": 200}, "input": {"type": "structure", "required": ["sequenceStoreId"], "members": {"filter": {"type": "structure", "members": {"createdAfter": {"shape": "S1a"}, "createdBefore": {"shape": "S1a"}, "status": {}}}, "maxResults": {"location": "querystring", "locationName": "maxResults", "type": "integer"}, "nextToken": {"location": "querystring", "locationName": "nextToken"}, "sequenceStoreId": {"location": "uri", "locationName": "sequenceStoreId"}}}, "output": {"type": "structure", "members": {"exportJobs": {"type": "list", "member": {"type": "structure", "required": ["creationTime", "destination", "id", "sequenceStoreId", "status"], "members": {"completionTime": {"shape": "S1a"}, "creationTime": {"shape": "S1a"}, "destination": {}, "id": {}, "sequenceStoreId": {}, "status": {}}}}, "nextToken": {}}}, "endpoint": {"hostPrefix": "control-storage-"}}, "ListReadSetImportJobs": {"http": {"requestUri": "/sequencestore/{sequenceStoreId}/importjobs", "responseCode": 200}, "input": {"type": "structure", "required": ["sequenceStoreId"], "members": {"filter": {"type": "structure", "members": {"createdAfter": {"shape": "S1a"}, "createdBefore": {"shape": "S1a"}, "status": {}}}, "maxResults": {"location": "querystring", "locationName": "maxResults", "type": "integer"}, "nextToken": {"location": "querystring", "locationName": "nextToken"}, "sequenceStoreId": {"location": "uri", "locationName": "sequenceStoreId"}}}, "output": {"type": "structure", "members": {"importJobs": {"type": "list", "member": {"type": "structure", "required": ["creationTime", "id", "roleArn", "sequenceStoreId", "status"], "members": {"completionTime": {"shape": "S1a"}, "creationTime": {"shape": "S1a"}, "id": {}, "roleArn": {}, "sequenceStoreId": {}, "status": {}}}}, "nextToken": {}}}, "endpoint": {"hostPrefix": "control-storage-"}}, "ListReadSets": {"http": {"requestUri": "/sequencestore/{sequenceStoreId}/readsets", "responseCode": 200}, "input": {"type": "structure", "required": ["sequenceStoreId"], "members": {"filter": {"type": "structure", "members": {"createdAfter": {"shape": "S1a"}, "createdBefore": {"shape": "S1a"}, "name": {}, "referenceArn": {}, "status": {}}}, "maxResults": {"location": "querystring", "locationName": "maxResults", "type": "integer"}, "nextToken": {"location": "querystring", "locationName": "nextToken"}, "sequenceStoreId": {"location": "uri", "locationName": "sequenceStoreId"}}}, "output": {"type": "structure", "required": ["readSets"], "members": {"nextToken": {}, "readSets": {"type": "list", "member": {"type": "structure", "required": ["arn", "creationTime", "fileType", "id", "sequenceStoreId", "status"], "members": {"arn": {}, "creationTime": {"shape": "S1a"}, "description": {}, "fileType": {}, "id": {}, "name": {}, "referenceArn": {}, "sampleId": {}, "sequenceInformation": {"shape": "S4r"}, "sequenceStoreId": {}, "status": {}, "subjectId": {}}}}}}, "endpoint": {"hostPrefix": "control-storage-"}}, "ListReferenceImportJobs": {"http": {"requestUri": "/referencestore/{referenceStoreId}/importjobs", "responseCode": 200}, "input": {"type": "structure", "required": ["referenceStoreId"], "members": {"filter": {"type": "structure", "members": {"createdAfter": {"shape": "S1a"}, "createdBefore": {"shape": "S1a"}, "status": {}}}, "maxResults": {"location": "querystring", "locationName": "maxResults", "type": "integer"}, "nextToken": {"location": "querystring", "locationName": "nextToken"}, "referenceStoreId": {"location": "uri", "locationName": "referenceStoreId"}}}, "output": {"type": "structure", "members": {"importJobs": {"type": "list", "member": {"type": "structure", "required": ["creationTime", "id", "referenceStoreId", "roleArn", "status"], "members": {"completionTime": {"shape": "S1a"}, "creationTime": {"shape": "S1a"}, "id": {}, "referenceStoreId": {}, "roleArn": {}, "status": {}}}}, "nextToken": {}}}, "endpoint": {"hostPrefix": "control-storage-"}}, "ListReferenceStores": {"http": {"requestUri": "/referencestores", "responseCode": 200}, "input": {"type": "structure", "members": {"filter": {"type": "structure", "members": {"createdAfter": {"shape": "S1a"}, "createdBefore": {"shape": "S1a"}, "name": {}}}, "maxResults": {"location": "querystring", "locationName": "maxResults", "type": "integer"}, "nextToken": {"location": "querystring", "locationName": "nextToken"}}}, "output": {"type": "structure", "required": ["referenceStores"], "members": {"nextToken": {}, "referenceStores": {"type": "list", "member": {"type": "structure", "required": ["arn", "creationTime", "id"], "members": {"arn": {}, "creationTime": {"shape": "S1a"}, "description": {}, "id": {}, "name": {}, "sseConfig": {"shape": "Sl"}}}}}}, "endpoint": {"hostPrefix": "control-storage-"}}, "ListReferences": {"http": {"requestUri": "/referencestore/{referenceStoreId}/references", "responseCode": 200}, "input": {"type": "structure", "required": ["referenceStoreId"], "members": {"filter": {"type": "structure", "members": {"createdAfter": {"shape": "S1a"}, "createdBefore": {"shape": "S1a"}, "md5": {}, "name": {}}}, "maxResults": {"location": "querystring", "locationName": "maxResults", "type": "integer"}, "nextToken": {"location": "querystring", "locationName": "nextToken"}, "referenceStoreId": {"location": "uri", "locationName": "referenceStoreId"}}}, "output": {"type": "structure", "required": ["references"], "members": {"nextToken": {}, "references": {"type": "list", "member": {"type": "structure", "required": ["arn", "creationTime", "id", "md5", "referenceStoreId", "updateTime"], "members": {"arn": {}, "creationTime": {"shape": "S1a"}, "description": {}, "id": {}, "md5": {}, "name": {}, "referenceStoreId": {}, "status": {}, "updateTime": {"shape": "S1a"}}}}}}, "endpoint": {"hostPrefix": "control-storage-"}}, "ListRunGroups": {"http": {"method": "GET", "requestUri": "/runGroup", "responseCode": 200}, "input": {"type": "structure", "members": {"maxResults": {"location": "querystring", "locationName": "maxResults", "type": "integer"}, "name": {"location": "querystring", "locationName": "name"}, "startingToken": {"location": "querystring", "locationName": "startingToken"}}}, "output": {"type": "structure", "members": {"items": {"type": "list", "member": {"type": "structure", "members": {"arn": {}, "creationTime": {"shape": "S61"}, "id": {}, "maxCpus": {"type": "integer"}, "maxDuration": {"type": "integer"}, "maxRuns": {"type": "integer"}, "name": {}}}}, "nextToken": {}}}, "endpoint": {"hostPrefix": "workflows-"}}, "ListRunTasks": {"http": {"method": "GET", "requestUri": "/run/{id}/task", "responseCode": 200}, "input": {"type": "structure", "required": ["id"], "members": {"id": {"location": "uri", "locationName": "id"}, "maxResults": {"location": "querystring", "locationName": "maxResults", "type": "integer"}, "startingToken": {"location": "querystring", "locationName": "startingToken"}, "status": {"location": "querystring", "locationName": "status"}}}, "output": {"type": "structure", "members": {"items": {"type": "list", "member": {"type": "structure", "members": {"cpus": {"type": "integer"}, "creationTime": {"shape": "S69"}, "memory": {"type": "integer"}, "name": {}, "startTime": {"shape": "S69"}, "status": {}, "stopTime": {"shape": "S69"}, "taskId": {}}}}, "nextToken": {}}}, "endpoint": {"hostPrefix": "workflows-"}}, "ListRuns": {"http": {"method": "GET", "requestUri": "/run", "responseCode": 200}, "input": {"type": "structure", "members": {"maxResults": {"location": "querystring", "locationName": "maxResults", "type": "integer"}, "name": {"location": "querystring", "locationName": "name"}, "runGroupId": {"location": "querystring", "locationName": "runGroupId"}, "startingToken": {"location": "querystring", "locationName": "startingToken"}}}, "output": {"type": "structure", "members": {"items": {"type": "list", "member": {"type": "structure", "members": {"arn": {}, "creationTime": {"shape": "S5j"}, "id": {}, "name": {}, "priority": {"type": "integer"}, "startTime": {"shape": "S5j"}, "status": {}, "stopTime": {"shape": "S5j"}, "storageCapacity": {"type": "integer"}, "workflowId": {}}}}, "nextToken": {}}}, "endpoint": {"hostPrefix": "workflows-"}}, "ListSequenceStores": {"http": {"requestUri": "/sequencestores", "responseCode": 200}, "input": {"type": "structure", "members": {"filter": {"type": "structure", "members": {"createdAfter": {"shape": "S1a"}, "createdBefore": {"shape": "S1a"}, "name": {}}}, "maxResults": {"location": "querystring", "locationName": "maxResults", "type": "integer"}, "nextToken": {"location": "querystring", "locationName": "nextToken"}}}, "output": {"type": "structure", "required": ["sequenceStores"], "members": {"nextToken": {}, "sequenceStores": {"type": "list", "member": {"type": "structure", "required": ["arn", "creationTime", "id"], "members": {"arn": {}, "creationTime": {"shape": "S1a"}, "description": {}, "id": {}, "name": {}, "sseConfig": {"shape": "Sl"}}}}}}, "endpoint": {"hostPrefix": "control-storage-"}}, "ListTagsForResource": {"http": {"method": "GET", "requestUri": "/tags/{resourceArn}", "responseCode": 200}, "input": {"type": "structure", "required": ["resourceArn"], "members": {"resourceArn": {"location": "uri", "locationName": "resourceArn"}}}, "output": {"type": "structure", "required": ["tags"], "members": {"tags": {"shape": "Sy"}}}, "endpoint": {"hostPrefix": "tags-"}}, "ListVariantImportJobs": {"http": {"requestUri": "/import/variants", "responseCode": 200}, "input": {"type": "structure", "members": {"filter": {"type": "structure", "members": {"status": {}, "storeName": {}}}, "ids": {"type": "list", "member": {}}, "maxResults": {"location": "querystring", "locationName": "maxResults", "type": "integer"}, "nextToken": {"location": "querystring", "locationName": "nextToken"}}}, "output": {"type": "structure", "members": {"nextToken": {}, "variantImportJobs": {"type": "list", "member": {"type": "structure", "required": ["creationTime", "destinationName", "id", "roleArn", "status", "updateTime"], "members": {"completionTime": {"shape": "S2s"}, "creationTime": {"shape": "S12"}, "destinationName": {}, "id": {}, "roleArn": {}, "runLeftNormalization": {"type": "boolean"}, "status": {}, "updateTime": {"shape": "S3e"}}}}}}, "endpoint": {"hostPrefix": "analytics-"}}, "ListVariantStores": {"http": {"requestUri": "/variantStores", "responseCode": 200}, "input": {"type": "structure", "members": {"filter": {"type": "structure", "members": {"status": {}}}, "ids": {"type": "list", "member": {}}, "maxResults": {"location": "querystring", "locationName": "maxResults", "type": "integer"}, "nextToken": {"location": "querystring", "locationName": "nextToken"}}}, "output": {"type": "structure", "members": {"nextToken": {}, "variantStores": {"type": "list", "member": {"type": "structure", "required": ["creationTime", "description", "id", "name", "reference", "sseConfig", "status", "statusMessage", "storeArn", "storeSizeBytes", "updateTime"], "members": {"creationTime": {"shape": "S12"}, "description": {}, "id": {}, "name": {}, "reference": {"shape": "Sj"}, "sseConfig": {"shape": "Sl"}, "status": {}, "statusMessage": {}, "storeArn": {}, "storeSizeBytes": {"type": "long"}, "updateTime": {"shape": "S3e"}}}}}}, "endpoint": {"hostPrefix": "analytics-"}}, "ListWorkflows": {"http": {"method": "GET", "requestUri": "/workflow", "responseCode": 200}, "input": {"type": "structure", "members": {"maxResults": {"location": "querystring", "locationName": "maxResults", "type": "integer"}, "name": {"location": "querystring", "locationName": "name"}, "startingToken": {"location": "querystring", "locationName": "startingToken"}, "type": {"location": "querystring", "locationName": "type"}}}, "output": {"type": "structure", "members": {"items": {"type": "list", "member": {"type": "structure", "members": {"arn": {}, "creationTime": {"shape": "S6r"}, "digest": {}, "id": {}, "name": {}, "status": {}, "type": {}}}}, "nextToken": {}}}, "endpoint": {"hostPrefix": "workflows-"}}, "StartAnnotationImportJob": {"http": {"requestUri": "/import/annotation", "responseCode": 200}, "input": {"type": "structure", "required": ["destinationName", "items", "roleArn"], "members": {"destinationName": {}, "formatOptions": {"shape": "S2u"}, "items": {"type": "list", "member": {"type": "structure", "required": ["source"], "members": {"source": {}}}}, "roleArn": {}, "runLeftNormalization": {"type": "boolean"}}}, "output": {"type": "structure", "required": ["jobId"], "members": {"jobId": {}}}, "endpoint": {"hostPrefix": "analytics-"}}, "StartReadSetActivationJob": {"http": {"requestUri": "/sequencestore/{sequenceStoreId}/activationjob", "responseCode": 200}, "input": {"type": "structure", "required": ["sequenceStoreId", "sources"], "members": {"clientToken": {}, "sequenceStoreId": {"location": "uri", "locationName": "sequenceStoreId"}, "sources": {"type": "list", "member": {"type": "structure", "required": ["readSetId"], "members": {"readSetId": {}}}}}}, "output": {"type": "structure", "required": ["creationTime", "id", "sequenceStoreId", "status"], "members": {"creationTime": {"shape": "S1a"}, "id": {}, "sequenceStoreId": {}, "status": {}}}, "endpoint": {"hostPrefix": "control-storage-"}}, "StartReadSetExportJob": {"http": {"requestUri": "/sequencestore/{sequenceStoreId}/exportjob", "responseCode": 200}, "input": {"type": "structure", "required": ["destination", "roleArn", "sequenceStoreId", "sources"], "members": {"clientToken": {}, "destination": {}, "roleArn": {}, "sequenceStoreId": {"location": "uri", "locationName": "sequenceStoreId"}, "sources": {"type": "list", "member": {"type": "structure", "required": ["readSetId"], "members": {"readSetId": {}}}}}}, "output": {"type": "structure", "required": ["creationTime", "destination", "id", "sequenceStoreId", "status"], "members": {"creationTime": {"shape": "S1a"}, "destination": {}, "id": {}, "sequenceStoreId": {}, "status": {}}}, "endpoint": {"hostPrefix": "control-storage-"}}, "StartReadSetImportJob": {"http": {"requestUri": "/sequencestore/{sequenceStoreId}/importjob", "responseCode": 200}, "input": {"type": "structure", "required": ["roleArn", "sequenceStoreId", "sources"], "members": {"clientToken": {}, "roleArn": {}, "sequenceStoreId": {"location": "uri", "locationName": "sequenceStoreId"}, "sources": {"type": "list", "member": {"type": "structure", "required": ["referenceArn", "sampleId", "sourceFileType", "sourceFiles", "subjectId"], "members": {"description": {}, "generatedFrom": {}, "name": {}, "referenceArn": {}, "sampleId": {}, "sourceFileType": {}, "sourceFiles": {"shape": "S4f"}, "subjectId": {}, "tags": {"shape": "Sy"}}}}}}, "output": {"type": "structure", "required": ["creationTime", "id", "roleArn", "sequenceStoreId", "status"], "members": {"creationTime": {"shape": "S1a"}, "id": {}, "roleArn": {}, "sequenceStoreId": {}, "status": {}}}, "endpoint": {"hostPrefix": "control-storage-"}}, "StartReferenceImportJob": {"http": {"requestUri": "/referencestore/{referenceStoreId}/importjob", "responseCode": 200}, "input": {"type": "structure", "required": ["referenceStoreId", "roleArn", "sources"], "members": {"clientToken": {}, "referenceStoreId": {"location": "uri", "locationName": "referenceStoreId"}, "roleArn": {}, "sources": {"type": "list", "member": {"type": "structure", "required": ["name", "sourceFile"], "members": {"description": {}, "name": {}, "sourceFile": {}, "tags": {"shape": "Sy"}}}}}}, "output": {"type": "structure", "required": ["creationTime", "id", "referenceStoreId", "roleArn", "status"], "members": {"creationTime": {"shape": "S1a"}, "id": {}, "referenceStoreId": {}, "roleArn": {}, "status": {}}}, "endpoint": {"hostPrefix": "control-storage-"}}, "StartRun": {"http": {"requestUri": "/run", "responseCode": 201}, "input": {"type": "structure", "required": ["requestId", "roleArn"], "members": {"logLevel": {}, "name": {}, "outputUri": {}, "parameters": {"shape": "S5o"}, "priority": {"type": "integer"}, "requestId": {"idempotencyToken": true}, "roleArn": {}, "runGroupId": {}, "runId": {}, "storageCapacity": {"type": "integer"}, "tags": {"shape": "Sy"}, "workflowId": {}, "workflowType": {}}}, "output": {"type": "structure", "members": {"arn": {}, "id": {}, "status": {}, "tags": {"shape": "Sy"}}}, "endpoint": {"hostPrefix": "workflows-"}}, "StartVariantImportJob": {"http": {"requestUri": "/import/variant", "responseCode": 200}, "input": {"type": "structure", "required": ["destinationName", "items", "roleArn"], "members": {"destinationName": {}, "items": {"type": "list", "member": {"type": "structure", "required": ["source"], "members": {"source": {}}}}, "roleArn": {}, "runLeftNormalization": {"type": "boolean"}}}, "output": {"type": "structure", "required": ["jobId"], "members": {"jobId": {}}}, "endpoint": {"hostPrefix": "analytics-"}}, "TagResource": {"http": {"requestUri": "/tags/{resourceArn}", "responseCode": 204}, "input": {"type": "structure", "required": ["resourceArn", "tags"], "members": {"resourceArn": {"location": "uri", "locationName": "resourceArn"}, "tags": {"type": "map", "key": {}, "value": {}}}}, "output": {"type": "structure", "members": {}}, "endpoint": {"hostPrefix": "tags-"}}, "UntagResource": {"http": {"method": "DELETE", "requestUri": "/tags/{resourceArn}", "responseCode": 204}, "input": {"type": "structure", "required": ["resourceArn", "tagKeys"], "members": {"resourceArn": {"location": "uri", "locationName": "resourceArn"}, "tagKeys": {"location": "querystring", "locationName": "tagKeys", "type": "list", "member": {}}}}, "output": {"type": "structure", "members": {}}, "endpoint": {"hostPrefix": "tags-"}, "idempotent": true}, "UpdateAnnotationStore": {"http": {"requestUri": "/annotationStore/{name}", "responseCode": 200}, "input": {"type": "structure", "required": ["name"], "members": {"description": {}, "name": {"location": "uri", "locationName": "name"}}}, "output": {"type": "structure", "required": ["creationTime", "description", "id", "name", "reference", "status", "updateTime"], "members": {"creationTime": {"shape": "S12"}, "description": {}, "id": {}, "name": {}, "reference": {"shape": "Sj"}, "status": {}, "storeFormat": {}, "storeOptions": {"shape": "Sp"}, "updateTime": {"shape": "S3e"}}}, "endpoint": {"hostPrefix": "analytics-"}}, "UpdateRunGroup": {"http": {"requestUri": "/runGroup/{id}", "responseCode": 202}, "input": {"type": "structure", "required": ["id"], "members": {"id": {"location": "uri", "locationName": "id"}, "maxCpus": {"type": "integer"}, "maxDuration": {"type": "integer"}, "maxRuns": {"type": "integer"}, "name": {}}}, "endpoint": {"hostPrefix": "workflows-"}}, "UpdateVariantStore": {"http": {"requestUri": "/variantStore/{name}", "responseCode": 200}, "input": {"type": "structure", "required": ["name"], "members": {"description": {}, "name": {"location": "uri", "locationName": "name"}}}, "output": {"type": "structure", "required": ["creationTime", "description", "id", "name", "reference", "status", "updateTime"], "members": {"creationTime": {"shape": "S12"}, "description": {}, "id": {}, "name": {}, "reference": {"shape": "Sj"}, "status": {}, "updateTime": {"shape": "S3e"}}}, "endpoint": {"hostPrefix": "analytics-"}}, "UpdateWorkflow": {"http": {"requestUri": "/workflow/{id}", "responseCode": 202}, "input": {"type": "structure", "required": ["id"], "members": {"description": {}, "id": {"location": "uri", "locationName": "id"}, "name": {}}}, "endpoint": {"hostPrefix": "workflows-"}}}, "shapes": {"Sj": {"type": "structure", "members": {"referenceArn": {}}, "union": true}, "Sl": {"type": "structure", "required": ["type"], "members": {"keyArn": {}, "type": {}}}, "Sp": {"type": "structure", "members": {"tsvStoreOptions": {"type": "structure", "members": {"annotationType": {}, "formatToHeader": {"type": "map", "key": {}, "value": {}}, "schema": {"type": "list", "member": {"type": "map", "key": {}, "value": {}}}}}}, "union": true}, "Sy": {"type": "map", "key": {}, "value": {}}, "S12": {"type": "timestamp", "timestampFormat": "iso8601"}, "S1a": {"type": "timestamp", "timestampFormat": "iso8601"}, "S20": {"type": "map", "key": {}, "value": {"type": "structure", "members": {"description": {}, "optional": {"type": "boolean"}}}}, "S2s": {"type": "timestamp", "timestampFormat": "iso8601"}, "S2u": {"type": "structure", "members": {"tsvOptions": {"type": "structure", "members": {"readOptions": {"type": "structure", "members": {"comment": {}, "encoding": {}, "escape": {}, "escapeQuotes": {"type": "boolean"}, "header": {"type": "boolean"}, "lineSep": {}, "quote": {}, "quoteAll": {"type": "boolean"}, "sep": {}}}}}, "vcfOptions": {"type": "structure", "members": {"ignoreFilterField": {"type": "boolean"}, "ignoreQualField": {"type": "boolean"}}}}, "union": true}, "S3e": {"type": "timestamp", "timestampFormat": "iso8601"}, "S4f": {"type": "structure", "required": ["source1"], "members": {"source1": {}, "source2": {}}}, "S4n": {"type": "structure", "members": {"contentLength": {"type": "long"}, "partSize": {"type": "long"}, "totalParts": {"type": "integer"}}}, "S4r": {"type": "structure", "members": {"alignment": {}, "generatedFrom": {}, "totalBaseCount": {"type": "long"}, "totalReadCount": {"type": "long"}}}, "S5j": {"type": "timestamp", "timestampFormat": "iso8601"}, "S5o": {"type": "structure", "members": {}, "document": true}, "S61": {"type": "timestamp", "timestampFormat": "iso8601"}, "S69": {"type": "timestamp", "timestampFormat": "iso8601"}, "S6r": {"type": "timestamp", "timestampFormat": "iso8601"}}}