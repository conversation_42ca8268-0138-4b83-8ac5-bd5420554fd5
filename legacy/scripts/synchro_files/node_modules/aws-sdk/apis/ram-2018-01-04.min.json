{"version": "2.0", "metadata": {"apiVersion": "2018-01-04", "endpointPrefix": "ram", "jsonVersion": "1.1", "protocol": "rest-json", "serviceAbbreviation": "RAM", "serviceFullName": "AWS Resource Access Manager", "serviceId": "RAM", "signatureVersion": "v4", "uid": "ram-2018-01-04"}, "operations": {"AcceptResourceShareInvitation": {"http": {"requestUri": "/acceptresourceshareinvitation"}, "input": {"type": "structure", "required": ["resourceShareInvitationArn"], "members": {"resourceShareInvitationArn": {}, "clientToken": {}}}, "output": {"type": "structure", "members": {"resourceShareInvitation": {"shape": "S4"}, "clientToken": {}}}}, "AssociateResourceShare": {"http": {"requestUri": "/associateresourceshare"}, "input": {"type": "structure", "required": ["resourceShareArn"], "members": {"resourceShareArn": {}, "resourceArns": {"shape": "Sd"}, "principals": {"shape": "Se"}, "clientToken": {}}}, "output": {"type": "structure", "members": {"resourceShareAssociations": {"shape": "S7"}, "clientToken": {}}}}, "AssociateResourceSharePermission": {"http": {"requestUri": "/associateresourcesharepermission"}, "input": {"type": "structure", "required": ["resourceShareArn", "permissionArn"], "members": {"resourceShareArn": {}, "permissionArn": {}, "replace": {"type": "boolean"}, "clientToken": {}, "permissionVersion": {"type": "integer"}}}, "output": {"type": "structure", "members": {"returnValue": {"type": "boolean"}, "clientToken": {}}}}, "CreateResourceShare": {"http": {"requestUri": "/createresourceshare"}, "input": {"type": "structure", "required": ["name"], "members": {"name": {}, "resourceArns": {"shape": "Sd"}, "principals": {"shape": "Se"}, "tags": {"shape": "Sk"}, "allowExternalPrincipals": {"type": "boolean"}, "clientToken": {}, "permissionArns": {"type": "list", "member": {}}}}, "output": {"type": "structure", "members": {"resourceShare": {"shape": "Sq"}, "clientToken": {}}}}, "DeleteResourceShare": {"http": {"method": "DELETE", "requestUri": "/deleteresourceshare"}, "input": {"type": "structure", "required": ["resourceShareArn"], "members": {"resourceShareArn": {"location": "querystring", "locationName": "resourceShareArn"}, "clientToken": {"location": "querystring", "locationName": "clientToken"}}}, "output": {"type": "structure", "members": {"returnValue": {"type": "boolean"}, "clientToken": {}}}}, "DisassociateResourceShare": {"http": {"requestUri": "/disassociateresourceshare"}, "input": {"type": "structure", "required": ["resourceShareArn"], "members": {"resourceShareArn": {}, "resourceArns": {"shape": "Sd"}, "principals": {"shape": "Se"}, "clientToken": {}}}, "output": {"type": "structure", "members": {"resourceShareAssociations": {"shape": "S7"}, "clientToken": {}}}}, "DisassociateResourceSharePermission": {"http": {"requestUri": "/disassociateresourcesharepermission"}, "input": {"type": "structure", "required": ["resourceShareArn", "permissionArn"], "members": {"resourceShareArn": {}, "permissionArn": {}, "clientToken": {}}}, "output": {"type": "structure", "members": {"returnValue": {"type": "boolean"}, "clientToken": {}}}}, "EnableSharingWithAwsOrganization": {"http": {"requestUri": "/enablesharingwithawsorganization"}, "input": {"type": "structure", "members": {}}, "output": {"type": "structure", "members": {"returnValue": {"type": "boolean"}}}}, "GetPermission": {"http": {"requestUri": "/getpermission"}, "input": {"type": "structure", "required": ["permissionArn"], "members": {"permissionArn": {}, "permissionVersion": {"type": "integer"}}}, "output": {"type": "structure", "members": {"permission": {"type": "structure", "members": {"arn": {}, "version": {}, "defaultVersion": {"type": "boolean"}, "name": {}, "resourceType": {}, "permission": {}, "creationTime": {"type": "timestamp"}, "lastUpdatedTime": {"type": "timestamp"}, "isResourceTypeDefault": {"type": "boolean"}}}}}}, "GetResourcePolicies": {"http": {"requestUri": "/getresourcepolicies"}, "input": {"type": "structure", "required": ["resourceArns"], "members": {"resourceArns": {"shape": "Sd"}, "principal": {}, "nextToken": {}, "maxResults": {"type": "integer"}}}, "output": {"type": "structure", "members": {"policies": {"type": "list", "member": {}}, "nextToken": {}}}}, "GetResourceShareAssociations": {"http": {"requestUri": "/getresourceshareassociations"}, "input": {"type": "structure", "required": ["associationType"], "members": {"associationType": {}, "resourceShareArns": {"shape": "S1a"}, "resourceArn": {}, "principal": {}, "associationStatus": {}, "nextToken": {}, "maxResults": {"type": "integer"}}}, "output": {"type": "structure", "members": {"resourceShareAssociations": {"shape": "S7"}, "nextToken": {}}}}, "GetResourceShareInvitations": {"http": {"requestUri": "/getresourceshareinvitations"}, "input": {"type": "structure", "members": {"resourceShareInvitationArns": {"type": "list", "member": {}}, "resourceShareArns": {"shape": "S1a"}, "nextToken": {}, "maxResults": {"type": "integer"}}}, "output": {"type": "structure", "members": {"resourceShareInvitations": {"type": "list", "member": {"shape": "S4"}}, "nextToken": {}}}}, "GetResourceShares": {"http": {"requestUri": "/getresourceshares"}, "input": {"type": "structure", "required": ["resourceOwner"], "members": {"resourceShareArns": {"shape": "S1a"}, "resourceShareStatus": {}, "resourceOwner": {}, "name": {}, "tagFilters": {"type": "list", "member": {"type": "structure", "members": {"tagKey": {}, "tagValues": {"type": "list", "member": {}}}}}, "nextToken": {}, "maxResults": {"type": "integer"}, "permissionArn": {}}}, "output": {"type": "structure", "members": {"resourceShares": {"type": "list", "member": {"shape": "Sq"}}, "nextToken": {}}}}, "ListPendingInvitationResources": {"http": {"requestUri": "/listpendinginvitationresources"}, "input": {"type": "structure", "required": ["resourceShareInvitationArn"], "members": {"resourceShareInvitationArn": {}, "nextToken": {}, "maxResults": {"type": "integer"}, "resourceRegionScope": {}}}, "output": {"type": "structure", "members": {"resources": {"shape": "S1q"}, "nextToken": {}}}}, "ListPermissionVersions": {"http": {"requestUri": "/listpermissionversions"}, "input": {"type": "structure", "required": ["permissionArn"], "members": {"permissionArn": {}, "nextToken": {}, "maxResults": {"type": "integer"}}}, "output": {"type": "structure", "members": {"permissions": {"shape": "S1w"}, "nextToken": {}}}}, "ListPermissions": {"http": {"requestUri": "/listpermissions"}, "input": {"type": "structure", "members": {"resourceType": {}, "nextToken": {}, "maxResults": {"type": "integer"}}}, "output": {"type": "structure", "members": {"permissions": {"shape": "S1w"}, "nextToken": {}}}}, "ListPrincipals": {"http": {"requestUri": "/listprincipals"}, "input": {"type": "structure", "required": ["resourceOwner"], "members": {"resourceOwner": {}, "resourceArn": {}, "principals": {"shape": "Se"}, "resourceType": {}, "resourceShareArns": {"shape": "S1a"}, "nextToken": {}, "maxResults": {"type": "integer"}}}, "output": {"type": "structure", "members": {"principals": {"type": "list", "member": {"type": "structure", "members": {"id": {}, "resourceShareArn": {}, "creationTime": {"type": "timestamp"}, "lastUpdatedTime": {"type": "timestamp"}, "external": {"type": "boolean"}}}}, "nextToken": {}}}}, "ListResourceSharePermissions": {"http": {"requestUri": "/listresourcesharepermissions"}, "input": {"type": "structure", "required": ["resourceShareArn"], "members": {"resourceShareArn": {}, "nextToken": {}, "maxResults": {"type": "integer"}}}, "output": {"type": "structure", "members": {"permissions": {"shape": "S1w"}, "nextToken": {}}}}, "ListResourceTypes": {"http": {"requestUri": "/listresourcetypes"}, "input": {"type": "structure", "members": {"nextToken": {}, "maxResults": {"type": "integer"}, "resourceRegionScope": {}}}, "output": {"type": "structure", "members": {"resourceTypes": {"type": "list", "member": {"type": "structure", "members": {"resourceType": {}, "serviceName": {}, "resourceRegionScope": {}}}}, "nextToken": {}}}}, "ListResources": {"http": {"requestUri": "/listresources"}, "input": {"type": "structure", "required": ["resourceOwner"], "members": {"resourceOwner": {}, "principal": {}, "resourceType": {}, "resourceArns": {"shape": "Sd"}, "resourceShareArns": {"shape": "S1a"}, "nextToken": {}, "maxResults": {"type": "integer"}, "resourceRegionScope": {}}}, "output": {"type": "structure", "members": {"resources": {"shape": "S1q"}, "nextToken": {}}}}, "PromoteResourceShareCreatedFromPolicy": {"http": {"requestUri": "/promoteresourcesharecreatedfrompolicy"}, "input": {"type": "structure", "required": ["resourceShareArn"], "members": {"resourceShareArn": {"location": "querystring", "locationName": "resourceShareArn"}}}, "output": {"type": "structure", "members": {"returnValue": {"type": "boolean"}}}}, "RejectResourceShareInvitation": {"http": {"requestUri": "/rejectresourceshareinvitation"}, "input": {"type": "structure", "required": ["resourceShareInvitationArn"], "members": {"resourceShareInvitationArn": {}, "clientToken": {}}}, "output": {"type": "structure", "members": {"resourceShareInvitation": {"shape": "S4"}, "clientToken": {}}}}, "TagResource": {"http": {"requestUri": "/tagresource"}, "input": {"type": "structure", "required": ["resourceShareArn", "tags"], "members": {"resourceShareArn": {}, "tags": {"shape": "Sk"}}}, "output": {"type": "structure", "members": {}}}, "UntagResource": {"http": {"requestUri": "/untagresource"}, "input": {"type": "structure", "required": ["resourceShareArn", "tagKeys"], "members": {"resourceShareArn": {}, "tagKeys": {"type": "list", "member": {}}}}, "output": {"type": "structure", "members": {}}}, "UpdateResourceShare": {"http": {"requestUri": "/updateresourceshare"}, "input": {"type": "structure", "required": ["resourceShareArn"], "members": {"resourceShareArn": {}, "name": {}, "allowExternalPrincipals": {"type": "boolean"}, "clientToken": {}}}, "output": {"type": "structure", "members": {"resourceShare": {"shape": "Sq"}, "clientToken": {}}}}}, "shapes": {"S4": {"type": "structure", "members": {"resourceShareInvitationArn": {}, "resourceShareName": {}, "resourceShareArn": {}, "senderAccountId": {}, "receiverAccountId": {}, "invitationTimestamp": {"type": "timestamp"}, "status": {}, "resourceShareAssociations": {"shape": "S7", "deprecated": true, "deprecatedMessage": "This member has been deprecated. Use ListPendingInvitationResources."}, "receiverArn": {}}}, "S7": {"type": "list", "member": {"type": "structure", "members": {"resourceShareArn": {}, "resourceShareName": {}, "associatedEntity": {}, "associationType": {}, "status": {}, "statusMessage": {}, "creationTime": {"type": "timestamp"}, "lastUpdatedTime": {"type": "timestamp"}, "external": {"type": "boolean"}}}}, "Sd": {"type": "list", "member": {}}, "Se": {"type": "list", "member": {}}, "Sk": {"type": "list", "member": {"type": "structure", "members": {"key": {}, "value": {}}}}, "Sq": {"type": "structure", "members": {"resourceShareArn": {}, "name": {}, "owningAccountId": {}, "allowExternalPrincipals": {"type": "boolean"}, "status": {}, "statusMessage": {}, "tags": {"shape": "Sk"}, "creationTime": {"type": "timestamp"}, "lastUpdatedTime": {"type": "timestamp"}, "featureSet": {}}}, "S1a": {"type": "list", "member": {}}, "S1q": {"type": "list", "member": {"type": "structure", "members": {"arn": {}, "type": {}, "resourceShareArn": {}, "resourceGroupArn": {}, "status": {}, "statusMessage": {}, "creationTime": {"type": "timestamp"}, "lastUpdatedTime": {"type": "timestamp"}, "resourceRegionScope": {}}}}, "S1w": {"type": "list", "member": {"type": "structure", "members": {"arn": {}, "version": {}, "defaultVersion": {"type": "boolean"}, "name": {}, "resourceType": {}, "status": {}, "creationTime": {"type": "timestamp"}, "lastUpdatedTime": {"type": "timestamp"}, "isResourceTypeDefault": {"type": "boolean"}}}}}}