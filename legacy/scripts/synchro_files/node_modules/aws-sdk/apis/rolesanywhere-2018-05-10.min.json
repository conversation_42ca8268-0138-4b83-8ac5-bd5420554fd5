{"version": "2.0", "metadata": {"apiVersion": "2018-05-10", "endpointPrefix": "rolesanywhere", "jsonVersion": "1.1", "protocol": "rest-json", "serviceFullName": "IAM Roles Anywhere", "serviceId": "RolesAnywhere", "signatureVersion": "v4", "signingName": "rolesanywhere", "uid": "rolesanywhere-2018-05-10"}, "operations": {"CreateProfile": {"http": {"requestUri": "/profiles", "responseCode": 201}, "input": {"type": "structure", "required": ["name", "roleArns"], "members": {"durationSeconds": {"type": "integer"}, "enabled": {"type": "boolean"}, "managedPolicyArns": {"shape": "S4"}, "name": {}, "requireInstanceProperties": {"type": "boolean"}, "roleArns": {"shape": "S7"}, "sessionPolicy": {}, "tags": {"shape": "Sa"}}}, "output": {"shape": "Se"}}, "CreateTrustAnchor": {"http": {"requestUri": "/trustanchors", "responseCode": 201}, "input": {"type": "structure", "required": ["name", "source"], "members": {"enabled": {"type": "boolean"}, "name": {}, "source": {"shape": "Sl"}, "tags": {"shape": "Sa"}}}, "output": {"shape": "So"}}, "DeleteCrl": {"http": {"method": "DELETE", "requestUri": "/crl/{crlId}", "responseCode": 200}, "input": {"shape": "Sq"}, "output": {"shape": "<PERSON>"}, "idempotent": true}, "DeleteProfile": {"http": {"method": "DELETE", "requestUri": "/profile/{profileId}", "responseCode": 200}, "input": {"shape": "Su"}, "output": {"shape": "Se"}, "idempotent": true}, "DeleteTrustAnchor": {"http": {"method": "DELETE", "requestUri": "/trustanchor/{trustAnchorId}", "responseCode": 200}, "input": {"shape": "Sv"}, "output": {"shape": "So"}, "idempotent": true}, "DisableCrl": {"http": {"requestUri": "/crl/{crlId}/disable", "responseCode": 200}, "input": {"shape": "Sq"}, "output": {"shape": "<PERSON>"}}, "DisableProfile": {"http": {"requestUri": "/profile/{profileId}/disable", "responseCode": 200}, "input": {"shape": "Su"}, "output": {"shape": "Se"}}, "DisableTrustAnchor": {"http": {"requestUri": "/trustanchor/{trustAnchorId}/disable", "responseCode": 200}, "input": {"shape": "Sv"}, "output": {"shape": "So"}}, "EnableCrl": {"http": {"requestUri": "/crl/{crlId}/enable", "responseCode": 200}, "input": {"shape": "Sq"}, "output": {"shape": "<PERSON>"}}, "EnableProfile": {"http": {"requestUri": "/profile/{profileId}/enable", "responseCode": 200}, "input": {"shape": "Su"}, "output": {"shape": "Se"}}, "EnableTrustAnchor": {"http": {"requestUri": "/trustanchor/{trustAnchorId}/enable", "responseCode": 200}, "input": {"shape": "Sv"}, "output": {"shape": "So"}}, "GetCrl": {"http": {"method": "GET", "requestUri": "/crl/{crlId}", "responseCode": 200}, "input": {"shape": "Sq"}, "output": {"shape": "<PERSON>"}}, "GetProfile": {"http": {"method": "GET", "requestUri": "/profile/{profileId}", "responseCode": 200}, "input": {"shape": "Su"}, "output": {"shape": "Se"}}, "GetSubject": {"http": {"method": "GET", "requestUri": "/subject/{subjectId}", "responseCode": 200}, "input": {"type": "structure", "required": ["subjectId"], "members": {"subjectId": {"location": "uri", "locationName": "subjectId"}}}, "output": {"type": "structure", "members": {"subject": {"type": "structure", "members": {"createdAt": {"shape": "Sg"}, "credentials": {"type": "list", "member": {"type": "structure", "members": {"enabled": {"type": "boolean"}, "failed": {"type": "boolean"}, "issuer": {}, "seenAt": {"shape": "Sg"}, "serialNumber": {}, "x509CertificateData": {}}}}, "enabled": {"type": "boolean"}, "instanceProperties": {"type": "list", "member": {"type": "structure", "members": {"failed": {"type": "boolean"}, "properties": {"type": "map", "key": {}, "value": {}}, "seenAt": {"shape": "Sg"}}}}, "lastSeenAt": {"shape": "Sg"}, "subjectArn": {}, "subjectId": {}, "updatedAt": {"shape": "Sg"}, "x509Subject": {}}}}}}, "GetTrustAnchor": {"http": {"method": "GET", "requestUri": "/trustanchor/{trustAnchorId}", "responseCode": 200}, "input": {"shape": "Sv"}, "output": {"shape": "So"}}, "ImportCrl": {"http": {"requestUri": "/crls", "responseCode": 201}, "input": {"type": "structure", "required": ["crlData", "name", "trustAnchorArn"], "members": {"crlData": {"type": "blob"}, "enabled": {"type": "boolean"}, "name": {}, "tags": {"shape": "Sa"}, "trustAnchorArn": {}}}, "output": {"shape": "<PERSON>"}}, "ListCrls": {"http": {"method": "GET", "requestUri": "/crls", "responseCode": 200}, "input": {"shape": "S19"}, "output": {"type": "structure", "members": {"crls": {"type": "list", "member": {"shape": "Ss"}}, "nextToken": {}}}}, "ListProfiles": {"http": {"method": "GET", "requestUri": "/profiles", "responseCode": 200}, "input": {"shape": "S19"}, "output": {"type": "structure", "members": {"nextToken": {}, "profiles": {"type": "list", "member": {"shape": "Sf"}}}}}, "ListSubjects": {"http": {"method": "GET", "requestUri": "/subjects", "responseCode": 200}, "input": {"shape": "S19"}, "output": {"type": "structure", "members": {"nextToken": {}, "subjects": {"type": "list", "member": {"type": "structure", "members": {"createdAt": {"shape": "Sg"}, "enabled": {"type": "boolean"}, "lastSeenAt": {"shape": "Sg"}, "subjectArn": {}, "subjectId": {}, "updatedAt": {"shape": "Sg"}, "x509Subject": {}}}}}}}, "ListTagsForResource": {"http": {"method": "GET", "requestUri": "/ListTagsForResource", "responseCode": 200}, "input": {"type": "structure", "required": ["resourceArn"], "members": {"resourceArn": {"location": "querystring", "locationName": "resourceArn"}}}, "output": {"type": "structure", "members": {"tags": {"shape": "Sa"}}}}, "ListTrustAnchors": {"http": {"method": "GET", "requestUri": "/trustanchors", "responseCode": 200}, "input": {"shape": "S19"}, "output": {"type": "structure", "members": {"nextToken": {}, "trustAnchors": {"type": "list", "member": {"shape": "Sp"}}}}}, "TagResource": {"http": {"requestUri": "/TagResource", "responseCode": 201}, "input": {"type": "structure", "required": ["resourceArn", "tags"], "members": {"resourceArn": {}, "tags": {"shape": "Sa"}}}, "output": {"type": "structure", "members": {}}}, "UntagResource": {"http": {"requestUri": "/UntagResource", "responseCode": 200}, "input": {"type": "structure", "required": ["resourceArn", "tagKeys"], "members": {"resourceArn": {}, "tagKeys": {"type": "list", "member": {"shape": "Sc"}}}}, "output": {"type": "structure", "members": {}}}, "UpdateCrl": {"http": {"method": "PATCH", "requestUri": "/crl/{crlId}", "responseCode": 200}, "input": {"type": "structure", "required": ["crlId"], "members": {"crlData": {"type": "blob"}, "crlId": {"location": "uri", "locationName": "crlId"}, "name": {}}}, "output": {"shape": "<PERSON>"}}, "UpdateProfile": {"http": {"method": "PATCH", "requestUri": "/profile/{profileId}", "responseCode": 200}, "input": {"type": "structure", "required": ["profileId"], "members": {"durationSeconds": {"type": "integer"}, "managedPolicyArns": {"shape": "S4"}, "name": {}, "profileId": {"location": "uri", "locationName": "profileId"}, "roleArns": {"shape": "S7"}, "sessionPolicy": {}}}, "output": {"shape": "Se"}, "idempotent": true}, "UpdateTrustAnchor": {"http": {"method": "PATCH", "requestUri": "/trustanchor/{trustAnchorId}", "responseCode": 200}, "input": {"type": "structure", "required": ["trustAnchorId"], "members": {"name": {}, "source": {"shape": "Sl"}, "trustAnchorId": {"location": "uri", "locationName": "trustAnchorId"}}}, "output": {"shape": "So"}, "idempotent": true}}, "shapes": {"S4": {"type": "list", "member": {}}, "S7": {"type": "list", "member": {}}, "Sa": {"type": "list", "member": {"type": "structure", "required": ["key", "value"], "members": {"key": {"shape": "Sc"}, "value": {"type": "string", "sensitive": true}}}}, "Sc": {"type": "string", "sensitive": true}, "Se": {"type": "structure", "members": {"profile": {"shape": "Sf"}}}, "Sf": {"type": "structure", "members": {"createdAt": {"shape": "Sg"}, "createdBy": {}, "durationSeconds": {"type": "integer"}, "enabled": {"type": "boolean"}, "managedPolicyArns": {"shape": "S4"}, "name": {}, "profileArn": {}, "profileId": {}, "requireInstanceProperties": {"type": "boolean"}, "roleArns": {"shape": "S7"}, "sessionPolicy": {}, "updatedAt": {"shape": "Sg"}}}, "Sg": {"type": "timestamp", "timestampFormat": "iso8601"}, "Sl": {"type": "structure", "members": {"sourceData": {"type": "structure", "members": {"acmPcaArn": {}, "x509CertificateData": {}}, "union": true}, "sourceType": {}}}, "So": {"type": "structure", "required": ["trustAnchor"], "members": {"trustAnchor": {"shape": "Sp"}}}, "Sp": {"type": "structure", "members": {"createdAt": {"shape": "Sg"}, "enabled": {"type": "boolean"}, "name": {}, "source": {"shape": "Sl"}, "trustAnchorArn": {}, "trustAnchorId": {}, "updatedAt": {"shape": "Sg"}}}, "Sq": {"type": "structure", "required": ["crlId"], "members": {"crlId": {"location": "uri", "locationName": "crlId"}}}, "Sr": {"type": "structure", "required": ["crl"], "members": {"crl": {"shape": "Ss"}}}, "Ss": {"type": "structure", "members": {"createdAt": {"shape": "Sg"}, "crlArn": {}, "crlData": {"type": "blob"}, "crlId": {}, "enabled": {"type": "boolean"}, "name": {}, "trustAnchorArn": {}, "updatedAt": {"shape": "Sg"}}}, "Su": {"type": "structure", "required": ["profileId"], "members": {"profileId": {"location": "uri", "locationName": "profileId"}}}, "Sv": {"type": "structure", "required": ["trustAnchorId"], "members": {"trustAnchorId": {"location": "uri", "locationName": "trustAnchorId"}}}, "S19": {"type": "structure", "members": {"nextToken": {"location": "querystring", "locationName": "nextToken"}, "pageSize": {"location": "querystring", "locationName": "pageSize", "type": "integer"}}}}}