{"metadata": {"apiVersion": "2018-11-14", "endpointPrefix": "mediaconnect", "signingName": "mediaconnect", "serviceFullName": "AWS MediaConnect", "serviceId": "MediaConnect", "protocol": "rest-json", "jsonVersion": "1.1", "uid": "mediaconnect-2018-11-14", "signatureVersion": "v4"}, "operations": {"AddFlowMediaStreams": {"http": {"requestUri": "/v1/flows/{flowArn}/mediaStreams", "responseCode": 201}, "input": {"type": "structure", "members": {"FlowArn": {"location": "uri", "locationName": "flowArn"}, "MediaStreams": {"shape": "S3", "locationName": "mediaStreams"}}, "required": ["FlowArn", "MediaStreams"]}, "output": {"type": "structure", "members": {"FlowArn": {"locationName": "flowArn"}, "MediaStreams": {"shape": "Se", "locationName": "mediaStreams"}}}}, "AddFlowOutputs": {"http": {"requestUri": "/v1/flows/{flowArn}/outputs", "responseCode": 201}, "input": {"type": "structure", "members": {"FlowArn": {"location": "uri", "locationName": "flowArn"}, "Outputs": {"shape": "Sj", "locationName": "outputs"}}, "required": ["FlowArn", "Outputs"]}, "output": {"type": "structure", "members": {"FlowArn": {"locationName": "flowArn"}, "Outputs": {"shape": "S11", "locationName": "outputs"}}}}, "AddFlowSources": {"http": {"requestUri": "/v1/flows/{flowArn}/source", "responseCode": 201}, "input": {"type": "structure", "members": {"FlowArn": {"location": "uri", "locationName": "flowArn"}, "Sources": {"shape": "S1b", "locationName": "sources"}}, "required": ["FlowArn", "Sources"]}, "output": {"type": "structure", "members": {"FlowArn": {"locationName": "flowArn"}, "Sources": {"shape": "S1i", "locationName": "sources"}}}}, "AddFlowVpcInterfaces": {"http": {"requestUri": "/v1/flows/{flowArn}/vpcInterfaces", "responseCode": 201}, "input": {"type": "structure", "members": {"FlowArn": {"location": "uri", "locationName": "flowArn"}, "VpcInterfaces": {"shape": "S1p", "locationName": "vpcInterfaces"}}, "required": ["FlowArn", "VpcInterfaces"]}, "output": {"type": "structure", "members": {"FlowArn": {"locationName": "flowArn"}, "VpcInterfaces": {"shape": "S1t", "locationName": "vpcInterfaces"}}}}, "CreateFlow": {"http": {"requestUri": "/v1/flows", "responseCode": 201}, "input": {"type": "structure", "members": {"AvailabilityZone": {"locationName": "availabilityZone"}, "Entitlements": {"shape": "S1w", "locationName": "entitlements"}, "MediaStreams": {"shape": "S3", "locationName": "mediaStreams"}, "Name": {"locationName": "name"}, "Outputs": {"shape": "Sj", "locationName": "outputs"}, "Source": {"shape": "S1c", "locationName": "source"}, "SourceFailoverConfig": {"shape": "S1z", "locationName": "sourceFailoverConfig"}, "Sources": {"shape": "S1b", "locationName": "sources"}, "VpcInterfaces": {"shape": "S1p", "locationName": "vpcInterfaces"}, "Maintenance": {"locationName": "maintenance", "type": "structure", "members": {"MaintenanceDay": {"locationName": "maintenanceDay"}, "MaintenanceStartHour": {"locationName": "maintenanceStartHour"}}, "required": ["MaintenanceDay", "MaintenanceStartHour"]}}, "required": ["Name"]}, "output": {"type": "structure", "members": {"Flow": {"shape": "S26", "locationName": "flow"}}}}, "DeleteFlow": {"http": {"method": "DELETE", "requestUri": "/v1/flows/{flowArn}", "responseCode": 202}, "input": {"type": "structure", "members": {"FlowArn": {"location": "uri", "locationName": "flowArn"}}, "required": ["FlowArn"]}, "output": {"type": "structure", "members": {"FlowArn": {"locationName": "flowArn"}, "Status": {"locationName": "status"}}}}, "DescribeFlow": {"http": {"method": "GET", "requestUri": "/v1/flows/{flowArn}", "responseCode": 200}, "input": {"type": "structure", "members": {"FlowArn": {"location": "uri", "locationName": "flowArn"}}, "required": ["FlowArn"]}, "output": {"type": "structure", "members": {"Flow": {"shape": "S26", "locationName": "flow"}, "Messages": {"locationName": "messages", "type": "structure", "members": {"Errors": {"shape": "Sl", "locationName": "errors"}}, "required": ["Errors"]}}}}, "DescribeOffering": {"http": {"method": "GET", "requestUri": "/v1/offerings/{offeringArn}", "responseCode": 200}, "input": {"type": "structure", "members": {"OfferingArn": {"location": "uri", "locationName": "offeringArn"}}, "required": ["OfferingArn"]}, "output": {"type": "structure", "members": {"Offering": {"shape": "S2i", "locationName": "offering"}}}}, "DescribeReservation": {"http": {"method": "GET", "requestUri": "/v1/reservations/{reservationArn}", "responseCode": 200}, "input": {"type": "structure", "members": {"ReservationArn": {"location": "uri", "locationName": "reservationArn"}}, "required": ["ReservationArn"]}, "output": {"type": "structure", "members": {"Reservation": {"shape": "S2p", "locationName": "reservation"}}}}, "GrantFlowEntitlements": {"http": {"requestUri": "/v1/flows/{flowArn}/entitlements", "responseCode": 200}, "input": {"type": "structure", "members": {"Entitlements": {"shape": "S1w", "locationName": "entitlements"}, "FlowArn": {"location": "uri", "locationName": "flowArn"}}, "required": ["FlowArn", "Entitlements"]}, "output": {"type": "structure", "members": {"Entitlements": {"shape": "S27", "locationName": "entitlements"}, "FlowArn": {"locationName": "flowArn"}}}}, "ListEntitlements": {"http": {"method": "GET", "requestUri": "/v1/entitlements", "responseCode": 200}, "input": {"type": "structure", "members": {"MaxResults": {"location": "querystring", "locationName": "maxResults", "type": "integer"}, "NextToken": {"location": "querystring", "locationName": "nextToken"}}}, "output": {"type": "structure", "members": {"Entitlements": {"locationName": "entitlements", "type": "list", "member": {"type": "structure", "members": {"DataTransferSubscriberFeePercent": {"locationName": "dataTransferSubscriberFeePercent", "type": "integer"}, "EntitlementArn": {"locationName": "entitlementArn"}, "EntitlementName": {"locationName": "entitlementName"}}, "required": ["EntitlementArn", "EntitlementName"]}}, "NextToken": {"locationName": "nextToken"}}}}, "ListFlows": {"http": {"method": "GET", "requestUri": "/v1/flows", "responseCode": 200}, "input": {"type": "structure", "members": {"MaxResults": {"location": "querystring", "locationName": "maxResults", "type": "integer"}, "NextToken": {"location": "querystring", "locationName": "nextToken"}}}, "output": {"type": "structure", "members": {"Flows": {"locationName": "flows", "type": "list", "member": {"type": "structure", "members": {"AvailabilityZone": {"locationName": "availabilityZone"}, "Description": {"locationName": "description"}, "FlowArn": {"locationName": "flowArn"}, "Name": {"locationName": "name"}, "SourceType": {"locationName": "sourceType"}, "Status": {"locationName": "status"}, "Maintenance": {"shape": "S2a", "locationName": "maintenance"}}, "required": ["Status", "Description", "SourceType", "AvailabilityZone", "FlowArn", "Name"]}}, "NextToken": {"locationName": "nextToken"}}}}, "ListOfferings": {"http": {"method": "GET", "requestUri": "/v1/offerings", "responseCode": 200}, "input": {"type": "structure", "members": {"MaxResults": {"location": "querystring", "locationName": "maxResults", "type": "integer"}, "NextToken": {"location": "querystring", "locationName": "nextToken"}}}, "output": {"type": "structure", "members": {"NextToken": {"locationName": "nextToken"}, "Offerings": {"locationName": "offerings", "type": "list", "member": {"shape": "S2i"}}}}}, "ListReservations": {"http": {"method": "GET", "requestUri": "/v1/reservations", "responseCode": 200}, "input": {"type": "structure", "members": {"MaxResults": {"location": "querystring", "locationName": "maxResults", "type": "integer"}, "NextToken": {"location": "querystring", "locationName": "nextToken"}}}, "output": {"type": "structure", "members": {"NextToken": {"locationName": "nextToken"}, "Reservations": {"locationName": "reservations", "type": "list", "member": {"shape": "S2p"}}}}}, "ListTagsForResource": {"http": {"method": "GET", "requestUri": "/tags/{resourceArn}", "responseCode": 200}, "input": {"type": "structure", "members": {"ResourceArn": {"location": "uri", "locationName": "resourceArn"}}, "required": ["ResourceArn"]}, "output": {"type": "structure", "members": {"Tags": {"shape": "S3b", "locationName": "tags"}}}}, "PurchaseOffering": {"http": {"requestUri": "/v1/offerings/{offeringArn}", "responseCode": 201}, "input": {"type": "structure", "members": {"OfferingArn": {"location": "uri", "locationName": "offeringArn"}, "ReservationName": {"locationName": "reservationName"}, "Start": {"locationName": "start"}}, "required": ["OfferingArn", "Start", "ReservationName"]}, "output": {"type": "structure", "members": {"Reservation": {"shape": "S2p", "locationName": "reservation"}}}}, "RemoveFlowMediaStream": {"http": {"method": "DELETE", "requestUri": "/v1/flows/{flowArn}/mediaStreams/{mediaStreamName}", "responseCode": 200}, "input": {"type": "structure", "members": {"FlowArn": {"location": "uri", "locationName": "flowArn"}, "MediaStreamName": {"location": "uri", "locationName": "mediaStreamName"}}, "required": ["FlowArn", "MediaStreamName"]}, "output": {"type": "structure", "members": {"FlowArn": {"locationName": "flowArn"}, "MediaStreamName": {"locationName": "mediaStreamName"}}}}, "RemoveFlowOutput": {"http": {"method": "DELETE", "requestUri": "/v1/flows/{flowArn}/outputs/{outputArn}", "responseCode": 202}, "input": {"type": "structure", "members": {"FlowArn": {"location": "uri", "locationName": "flowArn"}, "OutputArn": {"location": "uri", "locationName": "outputArn"}}, "required": ["FlowArn", "OutputArn"]}, "output": {"type": "structure", "members": {"FlowArn": {"locationName": "flowArn"}, "OutputArn": {"locationName": "outputArn"}}}}, "RemoveFlowSource": {"http": {"method": "DELETE", "requestUri": "/v1/flows/{flowArn}/source/{sourceArn}", "responseCode": 202}, "input": {"type": "structure", "members": {"FlowArn": {"location": "uri", "locationName": "flowArn"}, "SourceArn": {"location": "uri", "locationName": "sourceArn"}}, "required": ["FlowArn", "SourceArn"]}, "output": {"type": "structure", "members": {"FlowArn": {"locationName": "flowArn"}, "SourceArn": {"locationName": "sourceArn"}}}}, "RemoveFlowVpcInterface": {"http": {"method": "DELETE", "requestUri": "/v1/flows/{flowArn}/vpcInterfaces/{vpcInterfaceName}", "responseCode": 200}, "input": {"type": "structure", "members": {"FlowArn": {"location": "uri", "locationName": "flowArn"}, "VpcInterfaceName": {"location": "uri", "locationName": "vpcInterfaceName"}}, "required": ["FlowArn", "VpcInterfaceName"]}, "output": {"type": "structure", "members": {"FlowArn": {"locationName": "flowArn"}, "NonDeletedNetworkInterfaceIds": {"shape": "Sl", "locationName": "nonDeletedNetworkInterfaceIds"}, "VpcInterfaceName": {"locationName": "vpcInterfaceName"}}}}, "RevokeFlowEntitlement": {"http": {"method": "DELETE", "requestUri": "/v1/flows/{flowArn}/entitlements/{entitlementArn}", "responseCode": 202}, "input": {"type": "structure", "members": {"EntitlementArn": {"location": "uri", "locationName": "entitlementArn"}, "FlowArn": {"location": "uri", "locationName": "flowArn"}}, "required": ["FlowArn", "EntitlementArn"]}, "output": {"type": "structure", "members": {"EntitlementArn": {"locationName": "entitlementArn"}, "FlowArn": {"locationName": "flowArn"}}}}, "StartFlow": {"http": {"requestUri": "/v1/flows/start/{flowArn}", "responseCode": 202}, "input": {"type": "structure", "members": {"FlowArn": {"location": "uri", "locationName": "flowArn"}}, "required": ["FlowArn"]}, "output": {"type": "structure", "members": {"FlowArn": {"locationName": "flowArn"}, "Status": {"locationName": "status"}}}}, "StopFlow": {"http": {"requestUri": "/v1/flows/stop/{flowArn}", "responseCode": 202}, "input": {"type": "structure", "members": {"FlowArn": {"location": "uri", "locationName": "flowArn"}}, "required": ["FlowArn"]}, "output": {"type": "structure", "members": {"FlowArn": {"locationName": "flowArn"}, "Status": {"locationName": "status"}}}}, "TagResource": {"http": {"requestUri": "/tags/{resourceArn}", "responseCode": 204}, "input": {"type": "structure", "members": {"ResourceArn": {"location": "uri", "locationName": "resourceArn"}, "Tags": {"shape": "S3b", "locationName": "tags"}}, "required": ["ResourceArn", "Tags"]}}, "UntagResource": {"http": {"method": "DELETE", "requestUri": "/tags/{resourceArn}", "responseCode": 204}, "input": {"type": "structure", "members": {"ResourceArn": {"location": "uri", "locationName": "resourceArn"}, "TagKeys": {"shape": "Sl", "location": "querystring", "locationName": "tagKeys"}}, "required": ["TagKeys", "ResourceArn"]}}, "UpdateFlow": {"http": {"method": "PUT", "requestUri": "/v1/flows/{flowArn}", "responseCode": 202}, "input": {"type": "structure", "members": {"FlowArn": {"location": "uri", "locationName": "flowArn"}, "SourceFailoverConfig": {"locationName": "sourceFailoverConfig", "type": "structure", "members": {"FailoverMode": {"locationName": "failoverMode"}, "RecoveryWindow": {"locationName": "recoveryWindow", "type": "integer"}, "SourcePriority": {"shape": "S21", "locationName": "sourcePriority"}, "State": {"locationName": "state"}}}, "Maintenance": {"locationName": "maintenance", "type": "structure", "members": {"MaintenanceDay": {"locationName": "maintenanceDay"}, "MaintenanceScheduledDate": {"locationName": "maintenanceScheduledDate"}, "MaintenanceStartHour": {"locationName": "maintenanceStartHour"}}}}, "required": ["FlowArn"]}, "output": {"type": "structure", "members": {"Flow": {"shape": "S26", "locationName": "flow"}}}}, "UpdateFlowEntitlement": {"http": {"method": "PUT", "requestUri": "/v1/flows/{flowArn}/entitlements/{entitlementArn}", "responseCode": 202}, "input": {"type": "structure", "members": {"Description": {"locationName": "description"}, "Encryption": {"shape": "S3z", "locationName": "encryption"}, "EntitlementArn": {"location": "uri", "locationName": "entitlementArn"}, "EntitlementStatus": {"locationName": "entitlementStatus"}, "FlowArn": {"location": "uri", "locationName": "flowArn"}, "Subscribers": {"shape": "Sl", "locationName": "subscribers"}}, "required": ["FlowArn", "EntitlementArn"]}, "output": {"type": "structure", "members": {"Entitlement": {"shape": "S28", "locationName": "entitlement"}, "FlowArn": {"locationName": "flowArn"}}}}, "UpdateFlowMediaStream": {"http": {"method": "PUT", "requestUri": "/v1/flows/{flowArn}/mediaStreams/{mediaStreamName}", "responseCode": 202}, "input": {"type": "structure", "members": {"Attributes": {"shape": "S5", "locationName": "attributes"}, "ClockRate": {"locationName": "clockRate", "type": "integer"}, "Description": {"locationName": "description"}, "FlowArn": {"location": "uri", "locationName": "flowArn"}, "MediaStreamName": {"location": "uri", "locationName": "mediaStreamName"}, "MediaStreamType": {"locationName": "mediaStreamType"}, "VideoFormat": {"locationName": "videoFormat"}}, "required": ["FlowArn", "MediaStreamName"]}, "output": {"type": "structure", "members": {"FlowArn": {"locationName": "flowArn"}, "MediaStream": {"shape": "Sf", "locationName": "mediaStream"}}}}, "UpdateFlowOutput": {"http": {"method": "PUT", "requestUri": "/v1/flows/{flowArn}/outputs/{outputArn}", "responseCode": 202}, "input": {"type": "structure", "members": {"CidrAllowList": {"shape": "Sl", "locationName": "cidrAllowList"}, "Description": {"locationName": "description"}, "Destination": {"locationName": "destination"}, "Encryption": {"shape": "S3z", "locationName": "encryption"}, "FlowArn": {"location": "uri", "locationName": "flowArn"}, "MaxLatency": {"locationName": "maxLatency", "type": "integer"}, "MediaStreamOutputConfigurations": {"shape": "Sp", "locationName": "mediaStreamOutputConfigurations"}, "MinLatency": {"locationName": "minLatency", "type": "integer"}, "OutputArn": {"location": "uri", "locationName": "outputArn"}, "Port": {"locationName": "port", "type": "integer"}, "Protocol": {"locationName": "protocol"}, "RemoteId": {"locationName": "remoteId"}, "SenderControlPort": {"locationName": "senderControlPort", "type": "integer"}, "SenderIpAddress": {"locationName": "sender<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "SmoothingLatency": {"locationName": "smoothingLatency", "type": "integer"}, "StreamId": {"locationName": "streamId"}, "VpcInterfaceAttachment": {"shape": "Sz", "locationName": "vpcInterfaceAttachment"}}, "required": ["FlowArn", "OutputArn"]}, "output": {"type": "structure", "members": {"FlowArn": {"locationName": "flowArn"}, "Output": {"shape": "S12", "locationName": "output"}}}}, "UpdateFlowSource": {"http": {"method": "PUT", "requestUri": "/v1/flows/{flowArn}/source/{sourceArn}", "responseCode": 202}, "input": {"type": "structure", "members": {"Decryption": {"shape": "S3z", "locationName": "decryption"}, "Description": {"locationName": "description"}, "EntitlementArn": {"locationName": "entitlementArn"}, "FlowArn": {"location": "uri", "locationName": "flowArn"}, "IngestPort": {"locationName": "ingestPort", "type": "integer"}, "MaxBitrate": {"locationName": "maxBitrate", "type": "integer"}, "MaxLatency": {"locationName": "maxLatency", "type": "integer"}, "MaxSyncBuffer": {"locationName": "maxSyncBuffer", "type": "integer"}, "MediaStreamSourceConfigurations": {"shape": "S1d", "locationName": "mediaStreamSourceConfigurations"}, "MinLatency": {"locationName": "minLatency", "type": "integer"}, "Protocol": {"locationName": "protocol"}, "SenderControlPort": {"locationName": "senderControlPort", "type": "integer"}, "SenderIpAddress": {"locationName": "sender<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "SourceArn": {"location": "uri", "locationName": "sourceArn"}, "SourceListenerAddress": {"locationName": "sourceListenerAddress"}, "SourceListenerPort": {"locationName": "sourceListenerPort", "type": "integer"}, "StreamId": {"locationName": "streamId"}, "VpcInterfaceName": {"locationName": "vpcInterfaceName"}, "WhitelistCidr": {"locationName": "whitelist<PERSON><PERSON><PERSON>"}}, "required": ["FlowArn", "SourceArn"]}, "output": {"type": "structure", "members": {"FlowArn": {"locationName": "flowArn"}, "Source": {"shape": "S1j", "locationName": "source"}}}}}, "shapes": {"S3": {"type": "list", "member": {"type": "structure", "members": {"Attributes": {"shape": "S5", "locationName": "attributes"}, "ClockRate": {"locationName": "clockRate", "type": "integer"}, "Description": {"locationName": "description"}, "MediaStreamId": {"locationName": "mediaStreamId", "type": "integer"}, "MediaStreamName": {"locationName": "mediaStreamName"}, "MediaStreamType": {"locationName": "mediaStreamType"}, "VideoFormat": {"locationName": "videoFormat"}}, "required": ["MediaStreamType", "MediaStreamId", "MediaStreamName"]}}, "S5": {"type": "structure", "members": {"Fmtp": {"locationName": "fmtp", "type": "structure", "members": {"ChannelOrder": {"locationName": "channelOrder"}, "Colorimetry": {"locationName": "colorimetry"}, "ExactFramerate": {"locationName": "exactFramerate"}, "Par": {"locationName": "par"}, "Range": {"locationName": "range"}, "ScanMode": {"locationName": "scanMode"}, "Tcs": {"locationName": "tcs"}}}, "Lang": {"locationName": "lang"}}}, "Se": {"type": "list", "member": {"shape": "Sf"}}, "Sf": {"type": "structure", "members": {"Attributes": {"locationName": "attributes", "type": "structure", "members": {"Fmtp": {"locationName": "fmtp", "type": "structure", "members": {"ChannelOrder": {"locationName": "channelOrder"}, "Colorimetry": {"locationName": "colorimetry"}, "ExactFramerate": {"locationName": "exactFramerate"}, "Par": {"locationName": "par"}, "Range": {"locationName": "range"}, "ScanMode": {"locationName": "scanMode"}, "Tcs": {"locationName": "tcs"}}}, "Lang": {"locationName": "lang"}}, "required": ["Fmtp"]}, "ClockRate": {"locationName": "clockRate", "type": "integer"}, "Description": {"locationName": "description"}, "Fmt": {"locationName": "fmt", "type": "integer"}, "MediaStreamId": {"locationName": "mediaStreamId", "type": "integer"}, "MediaStreamName": {"locationName": "mediaStreamName"}, "MediaStreamType": {"locationName": "mediaStreamType"}, "VideoFormat": {"locationName": "videoFormat"}}, "required": ["MediaStreamType", "MediaStreamId", "MediaStreamName", "Fmt"]}, "Sj": {"type": "list", "member": {"type": "structure", "members": {"CidrAllowList": {"shape": "Sl", "locationName": "cidrAllowList"}, "Description": {"locationName": "description"}, "Destination": {"locationName": "destination"}, "Encryption": {"shape": "Sm", "locationName": "encryption"}, "MaxLatency": {"locationName": "maxLatency", "type": "integer"}, "MediaStreamOutputConfigurations": {"shape": "Sp", "locationName": "mediaStreamOutputConfigurations"}, "MinLatency": {"locationName": "minLatency", "type": "integer"}, "Name": {"locationName": "name"}, "Port": {"locationName": "port", "type": "integer"}, "Protocol": {"locationName": "protocol"}, "RemoteId": {"locationName": "remoteId"}, "SenderControlPort": {"locationName": "senderControlPort", "type": "integer"}, "SmoothingLatency": {"locationName": "smoothingLatency", "type": "integer"}, "StreamId": {"locationName": "streamId"}, "VpcInterfaceAttachment": {"shape": "Sz", "locationName": "vpcInterfaceAttachment"}}, "required": ["Protocol"]}}, "Sl": {"type": "list", "member": {}}, "Sm": {"type": "structure", "members": {"Algorithm": {"locationName": "algorithm"}, "ConstantInitializationVector": {"locationName": "constantInitializationVector"}, "DeviceId": {"locationName": "deviceId"}, "KeyType": {"locationName": "keyType"}, "Region": {"locationName": "region"}, "ResourceId": {"locationName": "resourceId"}, "RoleArn": {"locationName": "roleArn"}, "SecretArn": {"locationName": "secretArn"}, "Url": {"locationName": "url"}}, "required": ["RoleArn"]}, "Sp": {"type": "list", "member": {"type": "structure", "members": {"DestinationConfigurations": {"locationName": "destinationConfigurations", "type": "list", "member": {"type": "structure", "members": {"DestinationIp": {"locationName": "destinationIp"}, "DestinationPort": {"locationName": "destinationPort", "type": "integer"}, "Interface": {"shape": "St", "locationName": "interface"}}, "required": ["DestinationIp", "DestinationPort", "Interface"]}}, "EncodingName": {"locationName": "encodingName"}, "EncodingParameters": {"locationName": "encodingParameters", "type": "structure", "members": {"CompressionFactor": {"locationName": "compressionFactor", "type": "double"}, "EncoderProfile": {"locationName": "encoderProfile"}}, "required": ["EncoderProfile", "CompressionFactor"]}, "MediaStreamName": {"locationName": "mediaStreamName"}}, "required": ["MediaStreamName", "EncodingName"]}}, "St": {"type": "structure", "members": {"Name": {"locationName": "name"}}, "required": ["Name"]}, "Sz": {"type": "structure", "members": {"VpcInterfaceName": {"locationName": "vpcInterfaceName"}}}, "S11": {"type": "list", "member": {"shape": "S12"}}, "S12": {"type": "structure", "members": {"DataTransferSubscriberFeePercent": {"locationName": "dataTransferSubscriberFeePercent", "type": "integer"}, "Description": {"locationName": "description"}, "Destination": {"locationName": "destination"}, "Encryption": {"shape": "Sm", "locationName": "encryption"}, "EntitlementArn": {"locationName": "entitlementArn"}, "ListenerAddress": {"locationName": "listenerAddress"}, "MediaLiveInputArn": {"locationName": "mediaLiveInputArn"}, "MediaStreamOutputConfigurations": {"locationName": "mediaStreamOutputConfigurations", "type": "list", "member": {"type": "structure", "members": {"DestinationConfigurations": {"locationName": "destinationConfigurations", "type": "list", "member": {"type": "structure", "members": {"DestinationIp": {"locationName": "destinationIp"}, "DestinationPort": {"locationName": "destinationPort", "type": "integer"}, "Interface": {"shape": "S17", "locationName": "interface"}, "OutboundIp": {"locationName": "outboundIp"}}, "required": ["DestinationIp", "DestinationPort", "Interface", "OutboundIp"]}}, "EncodingName": {"locationName": "encodingName"}, "EncodingParameters": {"locationName": "encodingParameters", "type": "structure", "members": {"CompressionFactor": {"locationName": "compressionFactor", "type": "double"}, "EncoderProfile": {"locationName": "encoderProfile"}}, "required": ["EncoderProfile", "CompressionFactor"]}, "MediaStreamName": {"locationName": "mediaStreamName"}}, "required": ["MediaStreamName", "EncodingName"]}}, "Name": {"locationName": "name"}, "OutputArn": {"locationName": "outputArn"}, "Port": {"locationName": "port", "type": "integer"}, "Transport": {"shape": "S19", "locationName": "transport"}, "VpcInterfaceAttachment": {"shape": "Sz", "locationName": "vpcInterfaceAttachment"}}, "required": ["OutputArn", "Name"]}, "S17": {"type": "structure", "members": {"Name": {"locationName": "name"}}, "required": ["Name"]}, "S19": {"type": "structure", "members": {"CidrAllowList": {"shape": "Sl", "locationName": "cidrAllowList"}, "MaxBitrate": {"locationName": "maxBitrate", "type": "integer"}, "MaxLatency": {"locationName": "maxLatency", "type": "integer"}, "MaxSyncBuffer": {"locationName": "maxSyncBuffer", "type": "integer"}, "MinLatency": {"locationName": "minLatency", "type": "integer"}, "Protocol": {"locationName": "protocol"}, "RemoteId": {"locationName": "remoteId"}, "SenderControlPort": {"locationName": "senderControlPort", "type": "integer"}, "SenderIpAddress": {"locationName": "sender<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "SmoothingLatency": {"locationName": "smoothingLatency", "type": "integer"}, "SourceListenerAddress": {"locationName": "sourceListenerAddress"}, "SourceListenerPort": {"locationName": "sourceListenerPort", "type": "integer"}, "StreamId": {"locationName": "streamId"}}, "required": ["Protocol"]}, "S1b": {"type": "list", "member": {"shape": "S1c"}}, "S1c": {"type": "structure", "members": {"Decryption": {"shape": "Sm", "locationName": "decryption"}, "Description": {"locationName": "description"}, "EntitlementArn": {"locationName": "entitlementArn"}, "IngestPort": {"locationName": "ingestPort", "type": "integer"}, "MaxBitrate": {"locationName": "maxBitrate", "type": "integer"}, "MaxLatency": {"locationName": "maxLatency", "type": "integer"}, "MaxSyncBuffer": {"locationName": "maxSyncBuffer", "type": "integer"}, "MediaStreamSourceConfigurations": {"shape": "S1d", "locationName": "mediaStreamSourceConfigurations"}, "MinLatency": {"locationName": "minLatency", "type": "integer"}, "Name": {"locationName": "name"}, "Protocol": {"locationName": "protocol"}, "SenderControlPort": {"locationName": "senderControlPort", "type": "integer"}, "SenderIpAddress": {"locationName": "sender<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "SourceListenerAddress": {"locationName": "sourceListenerAddress"}, "SourceListenerPort": {"locationName": "sourceListenerPort", "type": "integer"}, "StreamId": {"locationName": "streamId"}, "VpcInterfaceName": {"locationName": "vpcInterfaceName"}, "WhitelistCidr": {"locationName": "whitelist<PERSON><PERSON><PERSON>"}}}, "S1d": {"type": "list", "member": {"type": "structure", "members": {"EncodingName": {"locationName": "encodingName"}, "InputConfigurations": {"locationName": "inputConfigurations", "type": "list", "member": {"type": "structure", "members": {"InputPort": {"locationName": "inputPort", "type": "integer"}, "Interface": {"shape": "St", "locationName": "interface"}}, "required": ["InputPort", "Interface"]}}, "MediaStreamName": {"locationName": "mediaStreamName"}}, "required": ["MediaStreamName", "EncodingName"]}}, "S1i": {"type": "list", "member": {"shape": "S1j"}}, "S1j": {"type": "structure", "members": {"DataTransferSubscriberFeePercent": {"locationName": "dataTransferSubscriberFeePercent", "type": "integer"}, "Decryption": {"shape": "Sm", "locationName": "decryption"}, "Description": {"locationName": "description"}, "EntitlementArn": {"locationName": "entitlementArn"}, "IngestIp": {"locationName": "ingestIp"}, "IngestPort": {"locationName": "ingestPort", "type": "integer"}, "MediaStreamSourceConfigurations": {"locationName": "mediaStreamSourceConfigurations", "type": "list", "member": {"type": "structure", "members": {"EncodingName": {"locationName": "encodingName"}, "InputConfigurations": {"locationName": "inputConfigurations", "type": "list", "member": {"type": "structure", "members": {"InputIp": {"locationName": "inputIp"}, "InputPort": {"locationName": "inputPort", "type": "integer"}, "Interface": {"shape": "S17", "locationName": "interface"}}, "required": ["InputPort", "InputIp", "Interface"]}}, "MediaStreamName": {"locationName": "mediaStreamName"}}, "required": ["MediaStreamName", "EncodingName"]}}, "Name": {"locationName": "name"}, "SenderControlPort": {"locationName": "senderControlPort", "type": "integer"}, "SenderIpAddress": {"locationName": "sender<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "SourceArn": {"locationName": "sourceArn"}, "Transport": {"shape": "S19", "locationName": "transport"}, "VpcInterfaceName": {"locationName": "vpcInterfaceName"}, "WhitelistCidr": {"locationName": "whitelist<PERSON><PERSON><PERSON>"}}, "required": ["SourceArn", "Name"]}, "S1p": {"type": "list", "member": {"type": "structure", "members": {"Name": {"locationName": "name"}, "NetworkInterfaceType": {"locationName": "networkInterfaceType"}, "RoleArn": {"locationName": "roleArn"}, "SecurityGroupIds": {"shape": "Sl", "locationName": "securityGroupIds"}, "SubnetId": {"locationName": "subnetId"}}, "required": ["SubnetId", "SecurityGroupIds", "RoleArn", "Name"]}}, "S1t": {"type": "list", "member": {"type": "structure", "members": {"Name": {"locationName": "name"}, "NetworkInterfaceIds": {"shape": "Sl", "locationName": "networkInterfaceIds"}, "NetworkInterfaceType": {"locationName": "networkInterfaceType"}, "RoleArn": {"locationName": "roleArn"}, "SecurityGroupIds": {"shape": "Sl", "locationName": "securityGroupIds"}, "SubnetId": {"locationName": "subnetId"}}, "required": ["NetworkInterfaceType", "NetworkInterfaceIds", "SubnetId", "SecurityGroupIds", "RoleArn", "Name"]}}, "S1w": {"type": "list", "member": {"type": "structure", "members": {"DataTransferSubscriberFeePercent": {"locationName": "dataTransferSubscriberFeePercent", "type": "integer"}, "Description": {"locationName": "description"}, "Encryption": {"shape": "Sm", "locationName": "encryption"}, "EntitlementStatus": {"locationName": "entitlementStatus"}, "Name": {"locationName": "name"}, "Subscribers": {"shape": "Sl", "locationName": "subscribers"}}, "required": ["Subscribers"]}}, "S1z": {"type": "structure", "members": {"FailoverMode": {"locationName": "failoverMode"}, "RecoveryWindow": {"locationName": "recoveryWindow", "type": "integer"}, "SourcePriority": {"shape": "S21", "locationName": "sourcePriority"}, "State": {"locationName": "state"}}}, "S21": {"type": "structure", "members": {"PrimarySource": {"locationName": "primarySource"}}}, "S26": {"type": "structure", "members": {"AvailabilityZone": {"locationName": "availabilityZone"}, "Description": {"locationName": "description"}, "EgressIp": {"locationName": "egressIp"}, "Entitlements": {"shape": "S27", "locationName": "entitlements"}, "FlowArn": {"locationName": "flowArn"}, "MediaStreams": {"shape": "Se", "locationName": "mediaStreams"}, "Name": {"locationName": "name"}, "Outputs": {"shape": "S11", "locationName": "outputs"}, "Source": {"shape": "S1j", "locationName": "source"}, "SourceFailoverConfig": {"shape": "S1z", "locationName": "sourceFailoverConfig"}, "Sources": {"shape": "S1i", "locationName": "sources"}, "Status": {"locationName": "status"}, "VpcInterfaces": {"shape": "S1t", "locationName": "vpcInterfaces"}, "Maintenance": {"shape": "S2a", "locationName": "maintenance"}}, "required": ["Status", "Entitlements", "Outputs", "AvailabilityZone", "FlowArn", "Source", "Name"]}, "S27": {"type": "list", "member": {"shape": "S28"}}, "S28": {"type": "structure", "members": {"DataTransferSubscriberFeePercent": {"locationName": "dataTransferSubscriberFeePercent", "type": "integer"}, "Description": {"locationName": "description"}, "Encryption": {"shape": "Sm", "locationName": "encryption"}, "EntitlementArn": {"locationName": "entitlementArn"}, "EntitlementStatus": {"locationName": "entitlementStatus"}, "Name": {"locationName": "name"}, "Subscribers": {"shape": "Sl", "locationName": "subscribers"}}, "required": ["EntitlementArn", "Subscribers", "Name"]}, "S2a": {"type": "structure", "members": {"MaintenanceDay": {"locationName": "maintenanceDay"}, "MaintenanceDeadline": {"locationName": "maintenanceDeadline"}, "MaintenanceScheduledDate": {"locationName": "maintenanceScheduledDate"}, "MaintenanceStartHour": {"locationName": "maintenanceStartHour"}}}, "S2i": {"type": "structure", "members": {"CurrencyCode": {"locationName": "currencyCode"}, "Duration": {"locationName": "duration", "type": "integer"}, "DurationUnits": {"locationName": "durationUnits"}, "OfferingArn": {"locationName": "offeringArn"}, "OfferingDescription": {"locationName": "offeringDescription"}, "PricePerUnit": {"locationName": "pricePerUnit"}, "PriceUnits": {"locationName": "priceUnits"}, "ResourceSpecification": {"shape": "S2l", "locationName": "resourceSpecification"}}, "required": ["CurrencyCode", "OfferingArn", "OfferingDescription", "DurationUnits", "Duration", "PricePerUnit", "ResourceSpecification", "PriceUnits"]}, "S2l": {"type": "structure", "members": {"ReservedBitrate": {"locationName": "reservedBitrate", "type": "integer"}, "ResourceType": {"locationName": "resourceType"}}, "required": ["ResourceType"]}, "S2p": {"type": "structure", "members": {"CurrencyCode": {"locationName": "currencyCode"}, "Duration": {"locationName": "duration", "type": "integer"}, "DurationUnits": {"locationName": "durationUnits"}, "End": {"locationName": "end"}, "OfferingArn": {"locationName": "offeringArn"}, "OfferingDescription": {"locationName": "offeringDescription"}, "PricePerUnit": {"locationName": "pricePerUnit"}, "PriceUnits": {"locationName": "priceUnits"}, "ReservationArn": {"locationName": "reservationArn"}, "ReservationName": {"locationName": "reservationName"}, "ReservationState": {"locationName": "reservationState"}, "ResourceSpecification": {"shape": "S2l", "locationName": "resourceSpecification"}, "Start": {"locationName": "start"}}, "required": ["CurrencyCode", "ReservationState", "OfferingArn", "ReservationArn", "Start", "OfferingDescription", "ReservationName", "End", "Duration", "DurationUnits", "PricePerUnit", "ResourceSpecification", "PriceUnits"]}, "S3b": {"type": "map", "key": {}, "value": {}}, "S3z": {"type": "structure", "members": {"Algorithm": {"locationName": "algorithm"}, "ConstantInitializationVector": {"locationName": "constantInitializationVector"}, "DeviceId": {"locationName": "deviceId"}, "KeyType": {"locationName": "keyType"}, "Region": {"locationName": "region"}, "ResourceId": {"locationName": "resourceId"}, "RoleArn": {"locationName": "roleArn"}, "SecretArn": {"locationName": "secretArn"}, "Url": {"locationName": "url"}}}}}