{"version": "2.0", "metadata": {"apiVersion": "2020-07-20", "endpointPrefix": "proton", "jsonVersion": "1.0", "protocol": "json", "serviceFullName": "AWS Proton", "serviceId": "Proton", "signatureVersion": "v4", "signingName": "proton", "targetPrefix": "AwsProton20200720", "uid": "proton-2020-07-20"}, "operations": {"AcceptEnvironmentAccountConnection": {"input": {"type": "structure", "required": ["id"], "members": {"id": {}}}, "output": {"type": "structure", "required": ["environmentAccountConnection"], "members": {"environmentAccountConnection": {"shape": "S4"}}}, "idempotent": true}, "CancelComponentDeployment": {"input": {"type": "structure", "required": ["componentName"], "members": {"componentName": {}}}, "output": {"type": "structure", "required": ["component"], "members": {"component": {"shape": "Se"}}}}, "CancelEnvironmentDeployment": {"input": {"type": "structure", "required": ["environmentName"], "members": {"environmentName": {}}}, "output": {"type": "structure", "required": ["environment"], "members": {"environment": {"shape": "Sm"}}}}, "CancelServiceInstanceDeployment": {"input": {"type": "structure", "required": ["serviceInstanceName", "serviceName"], "members": {"serviceInstanceName": {}, "serviceName": {}}}, "output": {"type": "structure", "required": ["serviceInstance"], "members": {"serviceInstance": {"shape": "Sx"}}}}, "CancelServicePipelineDeployment": {"input": {"type": "structure", "required": ["serviceName"], "members": {"serviceName": {}}}, "output": {"type": "structure", "required": ["pipeline"], "members": {"pipeline": {"shape": "S11"}}}}, "CreateComponent": {"input": {"type": "structure", "required": ["manifest", "name", "templateFile"], "members": {"description": {"shape": "Si"}, "environmentName": {}, "manifest": {"type": "string", "sensitive": true}, "name": {}, "serviceInstanceName": {}, "serviceName": {}, "serviceSpec": {"shape": "Sj"}, "tags": {"shape": "S14"}, "templateFile": {"shape": "S18"}}}, "output": {"type": "structure", "required": ["component"], "members": {"component": {"shape": "Se"}}}, "idempotent": true}, "CreateEnvironment": {"input": {"type": "structure", "required": ["name", "spec", "templateMajorVersion", "templateName"], "members": {"codebuildRoleArn": {}, "componentRoleArn": {}, "description": {"shape": "Si"}, "environmentAccountConnectionId": {}, "name": {}, "protonServiceRoleArn": {}, "provisioningRepository": {"shape": "S1b"}, "spec": {"shape": "Sj"}, "tags": {"shape": "S14"}, "templateMajorVersion": {}, "templateMinorVersion": {}, "templateName": {}}}, "output": {"type": "structure", "required": ["environment"], "members": {"environment": {"shape": "Sm"}}}, "idempotent": true}, "CreateEnvironmentAccountConnection": {"input": {"type": "structure", "required": ["environmentName", "managementAccountId"], "members": {"clientToken": {"idempotencyToken": true}, "codebuildRoleArn": {}, "componentRoleArn": {}, "environmentName": {}, "managementAccountId": {}, "roleArn": {}, "tags": {"shape": "S14"}}}, "output": {"type": "structure", "required": ["environmentAccountConnection"], "members": {"environmentAccountConnection": {"shape": "S4"}}}, "idempotent": true}, "CreateEnvironmentTemplate": {"input": {"type": "structure", "required": ["name"], "members": {"description": {"shape": "Si"}, "displayName": {"shape": "S1h"}, "encryptionKey": {}, "name": {}, "provisioning": {}, "tags": {"shape": "S14"}}}, "output": {"type": "structure", "required": ["environmentTemplate"], "members": {"environmentTemplate": {"shape": "S1j"}}}, "idempotent": true}, "CreateEnvironmentTemplateVersion": {"input": {"type": "structure", "required": ["source", "templateName"], "members": {"clientToken": {"idempotencyToken": true}, "description": {"shape": "Si"}, "majorVersion": {}, "source": {"shape": "S1n"}, "tags": {"shape": "S14"}, "templateName": {}}}, "output": {"type": "structure", "required": ["environmentTemplateVersion"], "members": {"environmentTemplateVersion": {"shape": "S1s"}}}, "idempotent": true}, "CreateRepository": {"input": {"type": "structure", "required": ["connectionArn", "name", "provider"], "members": {"connectionArn": {}, "encryptionKey": {}, "name": {}, "provider": {}, "tags": {"shape": "S14"}}}, "output": {"type": "structure", "required": ["repository"], "members": {"repository": {"shape": "S1y"}}}, "idempotent": true}, "CreateService": {"input": {"type": "structure", "required": ["name", "spec", "templateMajorVersion", "templateName"], "members": {"branchName": {}, "description": {"shape": "Si"}, "name": {}, "repositoryConnectionArn": {}, "repositoryId": {}, "spec": {"shape": "Sj"}, "tags": {"shape": "S14"}, "templateMajorVersion": {}, "templateMinorVersion": {}, "templateName": {}}}, "output": {"type": "structure", "required": ["service"], "members": {"service": {"shape": "S22"}}}, "idempotent": true}, "CreateServiceTemplate": {"input": {"type": "structure", "required": ["name"], "members": {"description": {"shape": "Si"}, "displayName": {"shape": "S1h"}, "encryptionKey": {}, "name": {}, "pipelineProvisioning": {}, "tags": {"shape": "S14"}}}, "output": {"type": "structure", "required": ["serviceTemplate"], "members": {"serviceTemplate": {"shape": "S27"}}}, "idempotent": true}, "CreateServiceTemplateVersion": {"input": {"type": "structure", "required": ["compatibleEnvironmentTemplates", "source", "templateName"], "members": {"clientToken": {"idempotencyToken": true}, "compatibleEnvironmentTemplates": {"shape": "S2a"}, "description": {"shape": "Si"}, "majorVersion": {}, "source": {"shape": "S1n"}, "supportedComponentSources": {"shape": "S2c"}, "tags": {"shape": "S14"}, "templateName": {}}}, "output": {"type": "structure", "required": ["serviceTemplateVersion"], "members": {"serviceTemplateVersion": {"shape": "S2f"}}}, "idempotent": true}, "CreateTemplateSyncConfig": {"input": {"type": "structure", "required": ["branch", "repositoryName", "repositoryProvider", "templateName", "templateType"], "members": {"branch": {}, "repositoryName": {}, "repositoryProvider": {}, "subdirectory": {}, "templateName": {}, "templateType": {}}}, "output": {"type": "structure", "members": {"templateSyncConfig": {"shape": "S2n"}}}, "idempotent": true}, "DeleteComponent": {"input": {"type": "structure", "required": ["name"], "members": {"name": {}}}, "output": {"type": "structure", "members": {"component": {"shape": "Se"}}}, "idempotent": true}, "DeleteEnvironment": {"input": {"type": "structure", "required": ["name"], "members": {"name": {}}}, "output": {"type": "structure", "members": {"environment": {"shape": "Sm"}}}, "idempotent": true}, "DeleteEnvironmentAccountConnection": {"input": {"type": "structure", "required": ["id"], "members": {"id": {}}}, "output": {"type": "structure", "members": {"environmentAccountConnection": {"shape": "S4"}}}, "idempotent": true}, "DeleteEnvironmentTemplate": {"input": {"type": "structure", "required": ["name"], "members": {"name": {}}}, "output": {"type": "structure", "members": {"environmentTemplate": {"shape": "S1j"}}}, "idempotent": true}, "DeleteEnvironmentTemplateVersion": {"input": {"type": "structure", "required": ["majorVersion", "minorVersion", "templateName"], "members": {"majorVersion": {}, "minorVersion": {}, "templateName": {}}}, "output": {"type": "structure", "members": {"environmentTemplateVersion": {"shape": "S1s"}}}, "idempotent": true}, "DeleteRepository": {"input": {"type": "structure", "required": ["name", "provider"], "members": {"name": {}, "provider": {}}}, "output": {"type": "structure", "members": {"repository": {"shape": "S1y"}}}, "idempotent": true}, "DeleteService": {"input": {"type": "structure", "required": ["name"], "members": {"name": {}}}, "output": {"type": "structure", "members": {"service": {"shape": "S22"}}}, "idempotent": true}, "DeleteServiceTemplate": {"input": {"type": "structure", "required": ["name"], "members": {"name": {}}}, "output": {"type": "structure", "members": {"serviceTemplate": {"shape": "S27"}}}, "idempotent": true}, "DeleteServiceTemplateVersion": {"input": {"type": "structure", "required": ["majorVersion", "minorVersion", "templateName"], "members": {"majorVersion": {}, "minorVersion": {}, "templateName": {}}}, "output": {"type": "structure", "members": {"serviceTemplateVersion": {"shape": "S2f"}}}, "idempotent": true}, "DeleteTemplateSyncConfig": {"input": {"type": "structure", "required": ["templateName", "templateType"], "members": {"templateName": {}, "templateType": {}}}, "output": {"type": "structure", "members": {"templateSyncConfig": {"shape": "S2n"}}}, "idempotent": true}, "GetAccountSettings": {"input": {"type": "structure", "members": {}}, "output": {"type": "structure", "members": {"accountSettings": {"shape": "S3a"}}}}, "GetComponent": {"input": {"type": "structure", "required": ["name"], "members": {"name": {}}}, "output": {"type": "structure", "members": {"component": {"shape": "Se"}}}}, "GetEnvironment": {"input": {"type": "structure", "required": ["name"], "members": {"name": {}}}, "output": {"type": "structure", "required": ["environment"], "members": {"environment": {"shape": "Sm"}}}}, "GetEnvironmentAccountConnection": {"input": {"type": "structure", "required": ["id"], "members": {"id": {}}}, "output": {"type": "structure", "required": ["environmentAccountConnection"], "members": {"environmentAccountConnection": {"shape": "S4"}}}}, "GetEnvironmentTemplate": {"input": {"type": "structure", "required": ["name"], "members": {"name": {}}}, "output": {"type": "structure", "required": ["environmentTemplate"], "members": {"environmentTemplate": {"shape": "S1j"}}}}, "GetEnvironmentTemplateVersion": {"input": {"type": "structure", "required": ["majorVersion", "minorVersion", "templateName"], "members": {"majorVersion": {}, "minorVersion": {}, "templateName": {}}}, "output": {"type": "structure", "required": ["environmentTemplateVersion"], "members": {"environmentTemplateVersion": {"shape": "S1s"}}}}, "GetRepository": {"input": {"type": "structure", "required": ["name", "provider"], "members": {"name": {}, "provider": {}}}, "output": {"type": "structure", "required": ["repository"], "members": {"repository": {"shape": "S1y"}}}}, "GetRepositorySyncStatus": {"input": {"type": "structure", "required": ["branch", "repositoryName", "repositoryProvider", "syncType"], "members": {"branch": {}, "repositoryName": {}, "repositoryProvider": {}, "syncType": {}}}, "output": {"type": "structure", "members": {"latestSync": {"type": "structure", "required": ["events", "startedAt", "status"], "members": {"events": {"type": "list", "member": {"type": "structure", "required": ["event", "time", "type"], "members": {"event": {}, "externalId": {}, "time": {"type": "timestamp"}, "type": {}}}}, "startedAt": {"type": "timestamp"}, "status": {}}}}}}, "GetResourcesSummary": {"input": {"type": "structure", "members": {}}, "output": {"type": "structure", "required": ["counts"], "members": {"counts": {"type": "structure", "members": {"components": {"shape": "S3z"}, "environmentTemplates": {"shape": "S3z"}, "environments": {"shape": "S3z"}, "pipelines": {"shape": "S3z"}, "serviceInstances": {"shape": "S3z"}, "serviceTemplates": {"shape": "S3z"}, "services": {"shape": "S3z"}}}}}}, "GetService": {"input": {"type": "structure", "required": ["name"], "members": {"name": {}}}, "output": {"type": "structure", "members": {"service": {"shape": "S22"}}}}, "GetServiceInstance": {"input": {"type": "structure", "required": ["name", "serviceName"], "members": {"name": {}, "serviceName": {}}}, "output": {"type": "structure", "required": ["serviceInstance"], "members": {"serviceInstance": {"shape": "Sx"}}}}, "GetServiceTemplate": {"input": {"type": "structure", "required": ["name"], "members": {"name": {}}}, "output": {"type": "structure", "required": ["serviceTemplate"], "members": {"serviceTemplate": {"shape": "S27"}}}}, "GetServiceTemplateVersion": {"input": {"type": "structure", "required": ["majorVersion", "minorVersion", "templateName"], "members": {"majorVersion": {}, "minorVersion": {}, "templateName": {}}}, "output": {"type": "structure", "required": ["serviceTemplateVersion"], "members": {"serviceTemplateVersion": {"shape": "S2f"}}}}, "GetTemplateSyncConfig": {"input": {"type": "structure", "required": ["templateName", "templateType"], "members": {"templateName": {}, "templateType": {}}}, "output": {"type": "structure", "members": {"templateSyncConfig": {"shape": "S2n"}}}}, "GetTemplateSyncStatus": {"input": {"type": "structure", "required": ["templateName", "templateType", "templateVersion"], "members": {"templateName": {}, "templateType": {}, "templateVersion": {}}}, "output": {"type": "structure", "members": {"desiredState": {"shape": "S4d"}, "latestSuccessfulSync": {"shape": "S4f"}, "latestSync": {"shape": "S4f"}}}}, "ListComponentOutputs": {"input": {"type": "structure", "required": ["componentName"], "members": {"componentName": {}, "nextToken": {}}}, "output": {"type": "structure", "required": ["outputs"], "members": {"nextToken": {}, "outputs": {"shape": "S4m"}}}}, "ListComponentProvisionedResources": {"input": {"type": "structure", "required": ["componentName"], "members": {"componentName": {}, "nextToken": {}}}, "output": {"type": "structure", "required": ["provisionedResources"], "members": {"nextToken": {}, "provisionedResources": {"shape": "S4s"}}}}, "ListComponents": {"input": {"type": "structure", "members": {"environmentName": {}, "maxResults": {"type": "integer"}, "nextToken": {}, "serviceInstanceName": {}, "serviceName": {}}}, "output": {"type": "structure", "required": ["components"], "members": {"components": {"type": "list", "member": {"type": "structure", "required": ["arn", "createdAt", "deploymentStatus", "environmentName", "lastModifiedAt", "name"], "members": {"arn": {}, "createdAt": {"type": "timestamp"}, "deploymentStatus": {}, "deploymentStatusMessage": {"shape": "Sh"}, "environmentName": {}, "lastDeploymentAttemptedAt": {"type": "timestamp"}, "lastDeploymentSucceededAt": {"type": "timestamp"}, "lastModifiedAt": {"type": "timestamp"}, "name": {}, "serviceInstanceName": {}, "serviceName": {}}}}, "nextToken": {}}}}, "ListEnvironmentAccountConnections": {"input": {"type": "structure", "required": ["requestedBy"], "members": {"environmentName": {}, "maxResults": {"type": "integer"}, "nextToken": {}, "requestedBy": {}, "statuses": {"type": "list", "member": {}}}}, "output": {"type": "structure", "required": ["environmentAccountConnections"], "members": {"environmentAccountConnections": {"type": "list", "member": {"type": "structure", "required": ["arn", "environmentAccountId", "environmentName", "id", "lastModifiedAt", "managementAccountId", "requestedAt", "roleArn", "status"], "members": {"arn": {}, "componentRoleArn": {}, "environmentAccountId": {}, "environmentName": {}, "id": {}, "lastModifiedAt": {"type": "timestamp"}, "managementAccountId": {}, "requestedAt": {"type": "timestamp"}, "roleArn": {}, "status": {}}}}, "nextToken": {}}}}, "ListEnvironmentOutputs": {"input": {"type": "structure", "required": ["environmentName"], "members": {"environmentName": {}, "nextToken": {}}}, "output": {"type": "structure", "required": ["outputs"], "members": {"nextToken": {}, "outputs": {"shape": "S4m"}}}}, "ListEnvironmentProvisionedResources": {"input": {"type": "structure", "required": ["environmentName"], "members": {"environmentName": {}, "nextToken": {}}}, "output": {"type": "structure", "required": ["provisionedResources"], "members": {"nextToken": {}, "provisionedResources": {"shape": "S4s"}}}}, "ListEnvironmentTemplateVersions": {"input": {"type": "structure", "required": ["templateName"], "members": {"majorVersion": {}, "maxResults": {"type": "integer"}, "nextToken": {}, "templateName": {}}}, "output": {"type": "structure", "required": ["templateVersions"], "members": {"nextToken": {}, "templateVersions": {"type": "list", "member": {"type": "structure", "required": ["arn", "createdAt", "lastModifiedAt", "majorVersion", "minorVersion", "status", "templateName"], "members": {"arn": {}, "createdAt": {"type": "timestamp"}, "description": {"shape": "Si"}, "lastModifiedAt": {"type": "timestamp"}, "majorVersion": {}, "minorVersion": {}, "recommendedMinorVersion": {}, "status": {}, "statusMessage": {"shape": "Sh"}, "templateName": {}}}}}}}, "ListEnvironmentTemplates": {"input": {"type": "structure", "members": {"maxResults": {"type": "integer"}, "nextToken": {}}}, "output": {"type": "structure", "required": ["templates"], "members": {"nextToken": {}, "templates": {"type": "list", "member": {"type": "structure", "required": ["arn", "createdAt", "lastModifiedAt", "name"], "members": {"arn": {}, "createdAt": {"type": "timestamp"}, "description": {"shape": "Si"}, "displayName": {"shape": "S1h"}, "lastModifiedAt": {"type": "timestamp"}, "name": {}, "provisioning": {}, "recommendedVersion": {}}}}}}}, "ListEnvironments": {"input": {"type": "structure", "members": {"environmentTemplates": {"type": "list", "member": {"type": "structure", "required": ["majorVersion", "templateName"], "members": {"majorVersion": {}, "templateName": {}}}}, "maxResults": {"type": "integer"}, "nextToken": {}}}, "output": {"type": "structure", "required": ["environments"], "members": {"environments": {"type": "list", "member": {"type": "structure", "required": ["arn", "createdAt", "deploymentStatus", "lastDeploymentAttemptedAt", "lastDeploymentSucceededAt", "name", "templateMajorVersion", "templateMinorVersion", "templateName"], "members": {"arn": {}, "componentRoleArn": {}, "createdAt": {"type": "timestamp"}, "deploymentStatus": {}, "deploymentStatusMessage": {"shape": "Sh"}, "description": {"shape": "Si"}, "environmentAccountConnectionId": {}, "environmentAccountId": {}, "lastDeploymentAttemptedAt": {"type": "timestamp"}, "lastDeploymentSucceededAt": {"type": "timestamp"}, "name": {}, "protonServiceRoleArn": {}, "provisioning": {}, "templateMajorVersion": {}, "templateMinorVersion": {}, "templateName": {}}}}, "nextToken": {}}}}, "ListRepositories": {"input": {"type": "structure", "members": {"maxResults": {"type": "integer"}, "nextToken": {}}}, "output": {"type": "structure", "required": ["repositories"], "members": {"nextToken": {}, "repositories": {"type": "list", "member": {"type": "structure", "required": ["arn", "name", "provider"], "members": {"arn": {}, "name": {}, "provider": {}}}}}}}, "ListRepositorySyncDefinitions": {"input": {"type": "structure", "required": ["repositoryName", "repositoryProvider", "syncType"], "members": {"nextToken": {}, "repositoryName": {}, "repositoryProvider": {}, "syncType": {}}}, "output": {"type": "structure", "required": ["syncDefinitions"], "members": {"nextToken": {}, "syncDefinitions": {"type": "list", "member": {"type": "structure", "required": ["branch", "directory", "parent", "target"], "members": {"branch": {}, "directory": {}, "parent": {}, "target": {}}}}}}}, "ListServiceInstanceOutputs": {"input": {"type": "structure", "required": ["serviceInstanceName", "serviceName"], "members": {"nextToken": {}, "serviceInstanceName": {}, "serviceName": {}}}, "output": {"type": "structure", "required": ["outputs"], "members": {"nextToken": {}, "outputs": {"shape": "S4m"}}}}, "ListServiceInstanceProvisionedResources": {"input": {"type": "structure", "required": ["serviceInstanceName", "serviceName"], "members": {"nextToken": {}, "serviceInstanceName": {}, "serviceName": {}}}, "output": {"type": "structure", "required": ["provisionedResources"], "members": {"nextToken": {}, "provisionedResources": {"shape": "S4s"}}}}, "ListServiceInstances": {"input": {"type": "structure", "members": {"filters": {"type": "list", "member": {"type": "structure", "members": {"key": {}, "value": {}}}}, "maxResults": {"type": "integer"}, "nextToken": {}, "serviceName": {}, "sortBy": {}, "sortOrder": {}}}, "output": {"type": "structure", "required": ["serviceInstances"], "members": {"nextToken": {}, "serviceInstances": {"type": "list", "member": {"type": "structure", "required": ["arn", "createdAt", "deploymentStatus", "environmentName", "lastDeploymentAttemptedAt", "lastDeploymentSucceededAt", "name", "serviceName", "templateMajorVersion", "templateMinorVersion", "templateName"], "members": {"arn": {}, "createdAt": {"type": "timestamp"}, "deploymentStatus": {}, "deploymentStatusMessage": {"shape": "Sh"}, "environmentName": {}, "lastDeploymentAttemptedAt": {"type": "timestamp"}, "lastDeploymentSucceededAt": {"type": "timestamp"}, "name": {}, "serviceName": {}, "templateMajorVersion": {}, "templateMinorVersion": {}, "templateName": {}}}}}}}, "ListServicePipelineOutputs": {"input": {"type": "structure", "required": ["serviceName"], "members": {"nextToken": {}, "serviceName": {}}}, "output": {"type": "structure", "required": ["outputs"], "members": {"nextToken": {}, "outputs": {"shape": "S4m"}}}}, "ListServicePipelineProvisionedResources": {"input": {"type": "structure", "required": ["serviceName"], "members": {"nextToken": {}, "serviceName": {}}}, "output": {"type": "structure", "required": ["provisionedResources"], "members": {"nextToken": {}, "provisionedResources": {"shape": "S4s"}}}}, "ListServiceTemplateVersions": {"input": {"type": "structure", "required": ["templateName"], "members": {"majorVersion": {}, "maxResults": {"type": "integer"}, "nextToken": {}, "templateName": {}}}, "output": {"type": "structure", "required": ["templateVersions"], "members": {"nextToken": {}, "templateVersions": {"type": "list", "member": {"type": "structure", "required": ["arn", "createdAt", "lastModifiedAt", "majorVersion", "minorVersion", "status", "templateName"], "members": {"arn": {}, "createdAt": {"type": "timestamp"}, "description": {"shape": "Si"}, "lastModifiedAt": {"type": "timestamp"}, "majorVersion": {}, "minorVersion": {}, "recommendedMinorVersion": {}, "status": {}, "statusMessage": {"shape": "Sh"}, "templateName": {}}}}}}}, "ListServiceTemplates": {"input": {"type": "structure", "members": {"maxResults": {"type": "integer"}, "nextToken": {}}}, "output": {"type": "structure", "required": ["templates"], "members": {"nextToken": {}, "templates": {"type": "list", "member": {"type": "structure", "required": ["arn", "createdAt", "lastModifiedAt", "name"], "members": {"arn": {}, "createdAt": {"type": "timestamp"}, "description": {"shape": "Si"}, "displayName": {"shape": "S1h"}, "lastModifiedAt": {"type": "timestamp"}, "name": {}, "pipelineProvisioning": {}, "recommendedVersion": {}}}}}}}, "ListServices": {"input": {"type": "structure", "members": {"maxResults": {"type": "integer"}, "nextToken": {}}}, "output": {"type": "structure", "required": ["services"], "members": {"nextToken": {}, "services": {"type": "list", "member": {"type": "structure", "required": ["arn", "createdAt", "lastModifiedAt", "name", "status", "templateName"], "members": {"arn": {}, "createdAt": {"type": "timestamp"}, "description": {"shape": "Si"}, "lastModifiedAt": {"type": "timestamp"}, "name": {}, "status": {}, "statusMessage": {"shape": "Sh"}, "templateName": {}}}}}}}, "ListTagsForResource": {"input": {"type": "structure", "required": ["resourceArn"], "members": {"maxResults": {"type": "integer"}, "nextToken": {}, "resourceArn": {}}}, "output": {"type": "structure", "required": ["tags"], "members": {"nextToken": {}, "tags": {"shape": "S14"}}}}, "NotifyResourceDeploymentStatusChange": {"input": {"type": "structure", "required": ["resourceArn"], "members": {"deploymentId": {}, "outputs": {"type": "list", "member": {"shape": "S4n"}}, "resourceArn": {}, "status": {}, "statusMessage": {"type": "string", "sensitive": true}}}, "output": {"type": "structure", "members": {}}}, "RejectEnvironmentAccountConnection": {"input": {"type": "structure", "required": ["id"], "members": {"id": {}}}, "output": {"type": "structure", "required": ["environmentAccountConnection"], "members": {"environmentAccountConnection": {"shape": "S4"}}}, "idempotent": true}, "TagResource": {"input": {"type": "structure", "required": ["resourceArn", "tags"], "members": {"resourceArn": {}, "tags": {"shape": "S14"}}}, "output": {"type": "structure", "members": {}}, "idempotent": true}, "UntagResource": {"input": {"type": "structure", "required": ["resourceArn", "tagKeys"], "members": {"resourceArn": {}, "tagKeys": {"type": "list", "member": {}}}}, "output": {"type": "structure", "members": {}}, "idempotent": true}, "UpdateAccountSettings": {"input": {"type": "structure", "members": {"deletePipelineProvisioningRepository": {"type": "boolean"}, "pipelineCodebuildRoleArn": {}, "pipelineProvisioningRepository": {"shape": "S1b"}, "pipelineServiceRoleArn": {}}}, "output": {"type": "structure", "required": ["accountSettings"], "members": {"accountSettings": {"shape": "S3a"}}}}, "UpdateComponent": {"input": {"type": "structure", "required": ["deploymentType", "name"], "members": {"deploymentType": {}, "description": {"shape": "Si"}, "name": {}, "serviceInstanceName": {}, "serviceName": {}, "serviceSpec": {"shape": "Sj"}, "templateFile": {"shape": "S18"}}}, "output": {"type": "structure", "required": ["component"], "members": {"component": {"shape": "Se"}}}}, "UpdateEnvironment": {"input": {"type": "structure", "required": ["deploymentType", "name"], "members": {"codebuildRoleArn": {}, "componentRoleArn": {}, "deploymentType": {}, "description": {"shape": "Si"}, "environmentAccountConnectionId": {}, "name": {}, "protonServiceRoleArn": {}, "provisioningRepository": {"shape": "S1b"}, "spec": {"shape": "Sj"}, "templateMajorVersion": {}, "templateMinorVersion": {}}}, "output": {"type": "structure", "required": ["environment"], "members": {"environment": {"shape": "Sm"}}}}, "UpdateEnvironmentAccountConnection": {"input": {"type": "structure", "required": ["id"], "members": {"codebuildRoleArn": {}, "componentRoleArn": {}, "id": {}, "roleArn": {}}}, "output": {"type": "structure", "required": ["environmentAccountConnection"], "members": {"environmentAccountConnection": {"shape": "S4"}}}, "idempotent": true}, "UpdateEnvironmentTemplate": {"input": {"type": "structure", "required": ["name"], "members": {"description": {"shape": "Si"}, "displayName": {"shape": "S1h"}, "name": {}}}, "output": {"type": "structure", "required": ["environmentTemplate"], "members": {"environmentTemplate": {"shape": "S1j"}}}}, "UpdateEnvironmentTemplateVersion": {"input": {"type": "structure", "required": ["majorVersion", "minorVersion", "templateName"], "members": {"description": {"shape": "Si"}, "majorVersion": {}, "minorVersion": {}, "status": {}, "templateName": {}}}, "output": {"type": "structure", "required": ["environmentTemplateVersion"], "members": {"environmentTemplateVersion": {"shape": "S1s"}}}}, "UpdateService": {"input": {"type": "structure", "required": ["name"], "members": {"description": {"shape": "Si"}, "name": {}, "spec": {"shape": "Sj"}}}, "output": {"type": "structure", "required": ["service"], "members": {"service": {"shape": "S22"}}}}, "UpdateServiceInstance": {"input": {"type": "structure", "required": ["deploymentType", "name", "serviceName"], "members": {"deploymentType": {}, "name": {}, "serviceName": {}, "spec": {"shape": "Sj"}, "templateMajorVersion": {}, "templateMinorVersion": {}}}, "output": {"type": "structure", "required": ["serviceInstance"], "members": {"serviceInstance": {"shape": "Sx"}}}}, "UpdateServicePipeline": {"input": {"type": "structure", "required": ["deploymentType", "serviceName", "spec"], "members": {"deploymentType": {}, "serviceName": {}, "spec": {"shape": "Sj"}, "templateMajorVersion": {}, "templateMinorVersion": {}}}, "output": {"type": "structure", "required": ["pipeline"], "members": {"pipeline": {"shape": "S11"}}}}, "UpdateServiceTemplate": {"input": {"type": "structure", "required": ["name"], "members": {"description": {"shape": "Si"}, "displayName": {"shape": "S1h"}, "name": {}}}, "output": {"type": "structure", "required": ["serviceTemplate"], "members": {"serviceTemplate": {"shape": "S27"}}}}, "UpdateServiceTemplateVersion": {"input": {"type": "structure", "required": ["majorVersion", "minorVersion", "templateName"], "members": {"compatibleEnvironmentTemplates": {"shape": "S2a"}, "description": {"shape": "Si"}, "majorVersion": {}, "minorVersion": {}, "status": {}, "supportedComponentSources": {"shape": "S2c"}, "templateName": {}}}, "output": {"type": "structure", "required": ["serviceTemplateVersion"], "members": {"serviceTemplateVersion": {"shape": "S2f"}}}}, "UpdateTemplateSyncConfig": {"input": {"type": "structure", "required": ["branch", "repositoryName", "repositoryProvider", "templateName", "templateType"], "members": {"branch": {}, "repositoryName": {}, "repositoryProvider": {}, "subdirectory": {}, "templateName": {}, "templateType": {}}}, "output": {"type": "structure", "members": {"templateSyncConfig": {"shape": "S2n"}}}}}, "shapes": {"S4": {"type": "structure", "required": ["arn", "environmentAccountId", "environmentName", "id", "lastModifiedAt", "managementAccountId", "requestedAt", "roleArn", "status"], "members": {"arn": {}, "codebuildRoleArn": {}, "componentRoleArn": {}, "environmentAccountId": {}, "environmentName": {}, "id": {}, "lastModifiedAt": {"type": "timestamp"}, "managementAccountId": {}, "requestedAt": {"type": "timestamp"}, "roleArn": {}, "status": {}}}, "Se": {"type": "structure", "required": ["arn", "createdAt", "deploymentStatus", "environmentName", "lastModifiedAt", "name"], "members": {"arn": {}, "createdAt": {"type": "timestamp"}, "deploymentStatus": {}, "deploymentStatusMessage": {"shape": "Sh"}, "description": {"shape": "Si"}, "environmentName": {}, "lastDeploymentAttemptedAt": {"type": "timestamp"}, "lastDeploymentSucceededAt": {"type": "timestamp"}, "lastModifiedAt": {"type": "timestamp"}, "name": {}, "serviceInstanceName": {}, "serviceName": {}, "serviceSpec": {"shape": "Sj"}}}, "Sh": {"type": "string", "sensitive": true}, "Si": {"type": "string", "sensitive": true}, "Sj": {"type": "string", "sensitive": true}, "Sm": {"type": "structure", "required": ["arn", "createdAt", "deploymentStatus", "lastDeploymentAttemptedAt", "lastDeploymentSucceededAt", "name", "templateMajorVersion", "templateMinorVersion", "templateName"], "members": {"arn": {}, "codebuildRoleArn": {}, "componentRoleArn": {}, "createdAt": {"type": "timestamp"}, "deploymentStatus": {}, "deploymentStatusMessage": {"shape": "Sh"}, "description": {"shape": "Si"}, "environmentAccountConnectionId": {}, "environmentAccountId": {}, "lastDeploymentAttemptedAt": {"type": "timestamp"}, "lastDeploymentSucceededAt": {"type": "timestamp"}, "name": {}, "protonServiceRoleArn": {}, "provisioning": {}, "provisioningRepository": {"shape": "Sp"}, "spec": {"shape": "Sj"}, "templateMajorVersion": {}, "templateMinorVersion": {}, "templateName": {}}}, "Sp": {"type": "structure", "required": ["arn", "branch", "name", "provider"], "members": {"arn": {}, "branch": {}, "name": {}, "provider": {}}}, "Sx": {"type": "structure", "required": ["arn", "createdAt", "deploymentStatus", "environmentName", "lastDeploymentAttemptedAt", "lastDeploymentSucceededAt", "name", "serviceName", "templateMajorVersion", "templateMinorVersion", "templateName"], "members": {"arn": {}, "createdAt": {"type": "timestamp"}, "deploymentStatus": {}, "deploymentStatusMessage": {"shape": "Sh"}, "environmentName": {}, "lastDeploymentAttemptedAt": {"type": "timestamp"}, "lastDeploymentSucceededAt": {"type": "timestamp"}, "name": {}, "serviceName": {}, "spec": {"shape": "Sj"}, "templateMajorVersion": {}, "templateMinorVersion": {}, "templateName": {}}}, "S11": {"type": "structure", "required": ["arn", "createdAt", "deploymentStatus", "lastDeploymentAttemptedAt", "lastDeploymentSucceededAt", "templateMajorVersion", "templateMinorVersion", "templateName"], "members": {"arn": {}, "createdAt": {"type": "timestamp"}, "deploymentStatus": {}, "deploymentStatusMessage": {"shape": "Sh"}, "lastDeploymentAttemptedAt": {"type": "timestamp"}, "lastDeploymentSucceededAt": {"type": "timestamp"}, "spec": {"shape": "Sj"}, "templateMajorVersion": {}, "templateMinorVersion": {}, "templateName": {}}}, "S14": {"type": "list", "member": {"type": "structure", "required": ["key", "value"], "members": {"key": {}, "value": {}}}}, "S18": {"type": "string", "sensitive": true}, "S1b": {"type": "structure", "required": ["branch", "name", "provider"], "members": {"branch": {}, "name": {}, "provider": {}}}, "S1h": {"type": "string", "sensitive": true}, "S1j": {"type": "structure", "required": ["arn", "createdAt", "lastModifiedAt", "name"], "members": {"arn": {}, "createdAt": {"type": "timestamp"}, "description": {"shape": "Si"}, "displayName": {"shape": "S1h"}, "encryptionKey": {}, "lastModifiedAt": {"type": "timestamp"}, "name": {}, "provisioning": {}, "recommendedVersion": {}}}, "S1n": {"type": "structure", "members": {"s3": {"type": "structure", "required": ["bucket", "key"], "members": {"bucket": {}, "key": {}}}}, "union": true}, "S1s": {"type": "structure", "required": ["arn", "createdAt", "lastModifiedAt", "majorVersion", "minorVersion", "status", "templateName"], "members": {"arn": {}, "createdAt": {"type": "timestamp"}, "description": {"shape": "Si"}, "lastModifiedAt": {"type": "timestamp"}, "majorVersion": {}, "minorVersion": {}, "recommendedMinorVersion": {}, "schema": {"shape": "S1u"}, "status": {}, "statusMessage": {"shape": "Sh"}, "templateName": {}}}, "S1u": {"type": "string", "sensitive": true}, "S1y": {"type": "structure", "required": ["arn", "connectionArn", "name", "provider"], "members": {"arn": {}, "connectionArn": {}, "encryptionKey": {}, "name": {}, "provider": {}}}, "S22": {"type": "structure", "required": ["arn", "createdAt", "lastModifiedAt", "name", "spec", "status", "templateName"], "members": {"arn": {}, "branchName": {}, "createdAt": {"type": "timestamp"}, "description": {"shape": "Si"}, "lastModifiedAt": {"type": "timestamp"}, "name": {}, "pipeline": {"shape": "S11"}, "repositoryConnectionArn": {}, "repositoryId": {}, "spec": {"shape": "Sj"}, "status": {}, "statusMessage": {"shape": "Sh"}, "templateName": {}}}, "S27": {"type": "structure", "required": ["arn", "createdAt", "lastModifiedAt", "name"], "members": {"arn": {}, "createdAt": {"type": "timestamp"}, "description": {"shape": "Si"}, "displayName": {"shape": "S1h"}, "encryptionKey": {}, "lastModifiedAt": {"type": "timestamp"}, "name": {}, "pipelineProvisioning": {}, "recommendedVersion": {}}}, "S2a": {"type": "list", "member": {"type": "structure", "required": ["majorVersion", "templateName"], "members": {"majorVersion": {}, "templateName": {}}}}, "S2c": {"type": "list", "member": {}}, "S2f": {"type": "structure", "required": ["arn", "compatibleEnvironmentTemplates", "createdAt", "lastModifiedAt", "majorVersion", "minorVersion", "status", "templateName"], "members": {"arn": {}, "compatibleEnvironmentTemplates": {"type": "list", "member": {"type": "structure", "required": ["majorVersion", "templateName"], "members": {"majorVersion": {}, "templateName": {}}}}, "createdAt": {"type": "timestamp"}, "description": {"shape": "Si"}, "lastModifiedAt": {"type": "timestamp"}, "majorVersion": {}, "minorVersion": {}, "recommendedMinorVersion": {}, "schema": {"shape": "S1u"}, "status": {}, "statusMessage": {"shape": "Sh"}, "supportedComponentSources": {"shape": "S2c"}, "templateName": {}}}, "S2n": {"type": "structure", "required": ["branch", "repositoryName", "repositoryProvider", "templateName", "templateType"], "members": {"branch": {}, "repositoryName": {}, "repositoryProvider": {}, "subdirectory": {}, "templateName": {}, "templateType": {}}}, "S3a": {"type": "structure", "members": {"pipelineCodebuildRoleArn": {}, "pipelineProvisioningRepository": {"shape": "Sp"}, "pipelineServiceRoleArn": {}}}, "S3z": {"type": "structure", "required": ["total"], "members": {"behindMajor": {"type": "integer"}, "behindMinor": {"type": "integer"}, "failed": {"type": "integer"}, "total": {"type": "integer"}, "upToDate": {"type": "integer"}}}, "S4d": {"type": "structure", "required": ["branch", "directory", "repositoryName", "repositoryProvider", "sha"], "members": {"branch": {}, "directory": {}, "repositoryName": {}, "repositoryProvider": {}, "sha": {}}}, "S4f": {"type": "structure", "required": ["events", "initialRevision", "startedAt", "status", "target", "targetRevision"], "members": {"events": {"type": "list", "member": {"type": "structure", "required": ["event", "time", "type"], "members": {"event": {}, "externalId": {}, "time": {"type": "timestamp"}, "type": {}}}}, "initialRevision": {"shape": "S4d"}, "startedAt": {"type": "timestamp"}, "status": {}, "target": {}, "targetRevision": {"shape": "S4d"}}}, "S4m": {"type": "list", "member": {"shape": "S4n"}}, "S4n": {"type": "structure", "members": {"key": {}, "valueString": {}}, "sensitive": true}, "S4s": {"type": "list", "member": {"type": "structure", "members": {"identifier": {}, "name": {}, "provisioningEngine": {}}}}}}