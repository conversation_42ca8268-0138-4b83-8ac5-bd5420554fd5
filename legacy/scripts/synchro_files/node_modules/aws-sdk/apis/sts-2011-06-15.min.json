{"version": "2.0", "metadata": {"apiVersion": "2011-06-15", "endpointPrefix": "sts", "globalEndpoint": "sts.amazonaws.com", "protocol": "query", "serviceAbbreviation": "AWS STS", "serviceFullName": "AWS Security Token Service", "serviceId": "STS", "signatureVersion": "v4", "uid": "sts-2011-06-15", "xmlNamespace": "https://sts.amazonaws.com/doc/2011-06-15/"}, "operations": {"AssumeRole": {"input": {"type": "structure", "required": ["RoleArn", "RoleSessionName"], "members": {"RoleArn": {}, "RoleSessionName": {}, "PolicyArns": {"shape": "S4"}, "Policy": {}, "DurationSeconds": {"type": "integer"}, "Tags": {"shape": "S8"}, "TransitiveTagKeys": {"type": "list", "member": {}}, "ExternalId": {}, "SerialNumber": {}, "TokenCode": {}, "SourceIdentity": {}}}, "output": {"resultWrapper": "AssumeRoleResult", "type": "structure", "members": {"Credentials": {"shape": "Si"}, "AssumedRoleUser": {"shape": "Sn"}, "PackedPolicySize": {"type": "integer"}, "SourceIdentity": {}}}}, "AssumeRoleWithSAML": {"input": {"type": "structure", "required": ["RoleArn", "PrincipalArn", "SAMLAssertion"], "members": {"RoleArn": {}, "PrincipalArn": {}, "SAMLAssertion": {}, "PolicyArns": {"shape": "S4"}, "Policy": {}, "DurationSeconds": {"type": "integer"}}}, "output": {"resultWrapper": "AssumeRoleWithSAMLResult", "type": "structure", "members": {"Credentials": {"shape": "Si"}, "AssumedRoleUser": {"shape": "Sn"}, "PackedPolicySize": {"type": "integer"}, "Subject": {}, "SubjectType": {}, "Issuer": {}, "Audience": {}, "NameQualifier": {}, "SourceIdentity": {}}}}, "AssumeRoleWithWebIdentity": {"input": {"type": "structure", "required": ["RoleArn", "RoleSessionName", "WebIdentityToken"], "members": {"RoleArn": {}, "RoleSessionName": {}, "WebIdentityToken": {}, "ProviderId": {}, "PolicyArns": {"shape": "S4"}, "Policy": {}, "DurationSeconds": {"type": "integer"}}}, "output": {"resultWrapper": "AssumeRoleWithWebIdentityResult", "type": "structure", "members": {"Credentials": {"shape": "Si"}, "SubjectFromWebIdentityToken": {}, "AssumedRoleUser": {"shape": "Sn"}, "PackedPolicySize": {"type": "integer"}, "Provider": {}, "Audience": {}, "SourceIdentity": {}}}}, "DecodeAuthorizationMessage": {"input": {"type": "structure", "required": ["EncodedMessage"], "members": {"EncodedMessage": {}}}, "output": {"resultWrapper": "DecodeAuthorizationMessageResult", "type": "structure", "members": {"DecodedMessage": {}}}}, "GetAccessKeyInfo": {"input": {"type": "structure", "required": ["AccessKeyId"], "members": {"AccessKeyId": {}}}, "output": {"resultWrapper": "GetAccessKeyInfoResult", "type": "structure", "members": {"Account": {}}}}, "GetCallerIdentity": {"input": {"type": "structure", "members": {}}, "output": {"resultWrapper": "GetCallerIdentityResult", "type": "structure", "members": {"UserId": {}, "Account": {}, "Arn": {}}}}, "GetFederationToken": {"input": {"type": "structure", "required": ["Name"], "members": {"Name": {}, "Policy": {}, "PolicyArns": {"shape": "S4"}, "DurationSeconds": {"type": "integer"}, "Tags": {"shape": "S8"}}}, "output": {"resultWrapper": "GetFederationTokenResult", "type": "structure", "members": {"Credentials": {"shape": "Si"}, "FederatedUser": {"type": "structure", "required": ["FederatedUserId", "<PERSON><PERSON>"], "members": {"FederatedUserId": {}, "Arn": {}}}, "PackedPolicySize": {"type": "integer"}}}}, "GetSessionToken": {"input": {"type": "structure", "members": {"DurationSeconds": {"type": "integer"}, "SerialNumber": {}, "TokenCode": {}}}, "output": {"resultWrapper": "GetSessionTokenResult", "type": "structure", "members": {"Credentials": {"shape": "Si"}}}}}, "shapes": {"S4": {"type": "list", "member": {"type": "structure", "members": {"arn": {}}}}, "S8": {"type": "list", "member": {"type": "structure", "required": ["Key", "Value"], "members": {"Key": {}, "Value": {}}}}, "Si": {"type": "structure", "required": ["AccessKeyId", "SecretAccess<PERSON>ey", "SessionToken", "Expiration"], "members": {"AccessKeyId": {}, "SecretAccessKey": {}, "SessionToken": {}, "Expiration": {"type": "timestamp"}}}, "Sn": {"type": "structure", "required": ["AssumedRoleId", "<PERSON><PERSON>"], "members": {"AssumedRoleId": {}, "Arn": {}}}}}