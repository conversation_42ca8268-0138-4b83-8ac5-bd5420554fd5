{"version": "2.0", "metadata": {"apiVersion": "2020-07-20", "endpointPrefix": "sso", "jsonVersion": "1.1", "protocol": "json", "serviceAbbreviation": "SSO Admin", "serviceFullName": "AWS Single Sign-On Admin", "serviceId": "SSO Admin", "signatureVersion": "v4", "signingName": "sso", "targetPrefix": "SWBExternalService", "uid": "sso-admin-2020-07-20"}, "operations": {"AttachCustomerManagedPolicyReferenceToPermissionSet": {"input": {"type": "structure", "required": ["InstanceArn", "PermissionSetArn", "CustomerManagedPolicyReference"], "members": {"InstanceArn": {}, "PermissionSetArn": {}, "CustomerManagedPolicyReference": {"shape": "S4"}}}, "output": {"type": "structure", "members": {}}}, "AttachManagedPolicyToPermissionSet": {"input": {"type": "structure", "required": ["InstanceArn", "PermissionSetArn", "ManagedPolicyArn"], "members": {"InstanceArn": {}, "PermissionSetArn": {}, "ManagedPolicyArn": {}}}, "output": {"type": "structure", "members": {}}}, "CreateAccountAssignment": {"input": {"type": "structure", "required": ["InstanceArn", "TargetId", "TargetType", "PermissionSetArn", "PrincipalType", "PrincipalId"], "members": {"InstanceArn": {}, "TargetId": {}, "TargetType": {}, "PermissionSetArn": {}, "PrincipalType": {}, "PrincipalId": {}}}, "output": {"type": "structure", "members": {"AccountAssignmentCreationStatus": {"shape": "Sh"}}}}, "CreateInstanceAccessControlAttributeConfiguration": {"input": {"type": "structure", "required": ["InstanceArn", "InstanceAccessControlAttributeConfiguration"], "members": {"InstanceArn": {}, "InstanceAccessControlAttributeConfiguration": {"shape": "Sn"}}}, "output": {"type": "structure", "members": {}}}, "CreatePermissionSet": {"input": {"type": "structure", "required": ["Name", "InstanceArn"], "members": {"Name": {}, "Description": {}, "InstanceArn": {}, "SessionDuration": {}, "RelayState": {}, "Tags": {"shape": "S10"}}}, "output": {"type": "structure", "members": {"PermissionSet": {"shape": "S15"}}}}, "DeleteAccountAssignment": {"input": {"type": "structure", "required": ["InstanceArn", "TargetId", "TargetType", "PermissionSetArn", "PrincipalType", "PrincipalId"], "members": {"InstanceArn": {}, "TargetId": {}, "TargetType": {}, "PermissionSetArn": {}, "PrincipalType": {}, "PrincipalId": {}}}, "output": {"type": "structure", "members": {"AccountAssignmentDeletionStatus": {"shape": "Sh"}}}}, "DeleteInlinePolicyFromPermissionSet": {"input": {"type": "structure", "required": ["InstanceArn", "PermissionSetArn"], "members": {"InstanceArn": {}, "PermissionSetArn": {}}}, "output": {"type": "structure", "members": {}}}, "DeleteInstanceAccessControlAttributeConfiguration": {"input": {"type": "structure", "required": ["InstanceArn"], "members": {"InstanceArn": {}}}, "output": {"type": "structure", "members": {}}}, "DeletePermissionSet": {"input": {"type": "structure", "required": ["InstanceArn", "PermissionSetArn"], "members": {"InstanceArn": {}, "PermissionSetArn": {}}}, "output": {"type": "structure", "members": {}}}, "DeletePermissionsBoundaryFromPermissionSet": {"input": {"type": "structure", "required": ["InstanceArn", "PermissionSetArn"], "members": {"InstanceArn": {}, "PermissionSetArn": {}}}, "output": {"type": "structure", "members": {}}}, "DescribeAccountAssignmentCreationStatus": {"input": {"type": "structure", "required": ["InstanceArn", "AccountAssignmentCreationRequestId"], "members": {"InstanceArn": {}, "AccountAssignmentCreationRequestId": {}}}, "output": {"type": "structure", "members": {"AccountAssignmentCreationStatus": {"shape": "Sh"}}}}, "DescribeAccountAssignmentDeletionStatus": {"input": {"type": "structure", "required": ["InstanceArn", "AccountAssignmentDeletionRequestId"], "members": {"InstanceArn": {}, "AccountAssignmentDeletionRequestId": {}}}, "output": {"type": "structure", "members": {"AccountAssignmentDeletionStatus": {"shape": "Sh"}}}}, "DescribeInstanceAccessControlAttributeConfiguration": {"input": {"type": "structure", "required": ["InstanceArn"], "members": {"InstanceArn": {}}}, "output": {"type": "structure", "members": {"Status": {}, "StatusReason": {}, "InstanceAccessControlAttributeConfiguration": {"shape": "Sn"}}}}, "DescribePermissionSet": {"input": {"type": "structure", "required": ["InstanceArn", "PermissionSetArn"], "members": {"InstanceArn": {}, "PermissionSetArn": {}}}, "output": {"type": "structure", "members": {"PermissionSet": {"shape": "S15"}}}}, "DescribePermissionSetProvisioningStatus": {"input": {"type": "structure", "required": ["InstanceArn", "ProvisionPermissionSetRequestId"], "members": {"InstanceArn": {}, "ProvisionPermissionSetRequestId": {}}}, "output": {"type": "structure", "members": {"PermissionSetProvisioningStatus": {"shape": "S1s"}}}}, "DetachCustomerManagedPolicyReferenceFromPermissionSet": {"input": {"type": "structure", "required": ["InstanceArn", "PermissionSetArn", "CustomerManagedPolicyReference"], "members": {"InstanceArn": {}, "PermissionSetArn": {}, "CustomerManagedPolicyReference": {"shape": "S4"}}}, "output": {"type": "structure", "members": {}}}, "DetachManagedPolicyFromPermissionSet": {"input": {"type": "structure", "required": ["InstanceArn", "PermissionSetArn", "ManagedPolicyArn"], "members": {"InstanceArn": {}, "PermissionSetArn": {}, "ManagedPolicyArn": {}}}, "output": {"type": "structure", "members": {}}}, "GetInlinePolicyForPermissionSet": {"input": {"type": "structure", "required": ["InstanceArn", "PermissionSetArn"], "members": {"InstanceArn": {}, "PermissionSetArn": {}}}, "output": {"type": "structure", "members": {"InlinePolicy": {}}}}, "GetPermissionsBoundaryForPermissionSet": {"input": {"type": "structure", "required": ["InstanceArn", "PermissionSetArn"], "members": {"InstanceArn": {}, "PermissionSetArn": {}}}, "output": {"type": "structure", "members": {"PermissionsBoundary": {"shape": "S23"}}}}, "ListAccountAssignmentCreationStatus": {"input": {"type": "structure", "required": ["InstanceArn"], "members": {"InstanceArn": {}, "MaxResults": {"type": "integer"}, "NextToken": {}, "Filter": {"shape": "S27"}}}, "output": {"type": "structure", "members": {"AccountAssignmentsCreationStatus": {"shape": "S29"}, "NextToken": {}}}}, "ListAccountAssignmentDeletionStatus": {"input": {"type": "structure", "required": ["InstanceArn"], "members": {"InstanceArn": {}, "MaxResults": {"type": "integer"}, "NextToken": {}, "Filter": {"shape": "S27"}}}, "output": {"type": "structure", "members": {"AccountAssignmentsDeletionStatus": {"shape": "S29"}, "NextToken": {}}}}, "ListAccountAssignments": {"input": {"type": "structure", "required": ["InstanceArn", "AccountId", "PermissionSetArn"], "members": {"InstanceArn": {}, "AccountId": {}, "PermissionSetArn": {}, "MaxResults": {"type": "integer"}, "NextToken": {}}}, "output": {"type": "structure", "members": {"AccountAssignments": {"type": "list", "member": {"type": "structure", "members": {"AccountId": {}, "PermissionSetArn": {}, "PrincipalType": {}, "PrincipalId": {}}}}, "NextToken": {}}}}, "ListAccountsForProvisionedPermissionSet": {"input": {"type": "structure", "required": ["InstanceArn", "PermissionSetArn"], "members": {"InstanceArn": {}, "PermissionSetArn": {}, "ProvisioningStatus": {}, "MaxResults": {"type": "integer"}, "NextToken": {}}}, "output": {"type": "structure", "members": {"AccountIds": {"type": "list", "member": {}}, "NextToken": {}}}}, "ListCustomerManagedPolicyReferencesInPermissionSet": {"input": {"type": "structure", "required": ["InstanceArn", "PermissionSetArn"], "members": {"InstanceArn": {}, "PermissionSetArn": {}, "MaxResults": {"type": "integer"}, "NextToken": {}}}, "output": {"type": "structure", "members": {"CustomerManagedPolicyReferences": {"type": "list", "member": {"shape": "S4"}}, "NextToken": {}}}}, "ListInstances": {"input": {"type": "structure", "members": {"MaxResults": {"type": "integer"}, "NextToken": {}}}, "output": {"type": "structure", "members": {"Instances": {"type": "list", "member": {"type": "structure", "members": {"InstanceArn": {}, "IdentityStoreId": {}}}}, "NextToken": {}}}}, "ListManagedPoliciesInPermissionSet": {"input": {"type": "structure", "required": ["InstanceArn", "PermissionSetArn"], "members": {"InstanceArn": {}, "PermissionSetArn": {}, "MaxResults": {"type": "integer"}, "NextToken": {}}}, "output": {"type": "structure", "members": {"AttachedManagedPolicies": {"type": "list", "member": {"type": "structure", "members": {"Name": {}, "Arn": {}}}}, "NextToken": {}}}}, "ListPermissionSetProvisioningStatus": {"input": {"type": "structure", "required": ["InstanceArn"], "members": {"InstanceArn": {}, "MaxResults": {"type": "integer"}, "NextToken": {}, "Filter": {"shape": "S27"}}}, "output": {"type": "structure", "members": {"PermissionSetsProvisioningStatus": {"type": "list", "member": {"type": "structure", "members": {"Status": {}, "RequestId": {}, "CreatedDate": {"type": "timestamp"}}}}, "NextToken": {}}}}, "ListPermissionSets": {"input": {"type": "structure", "required": ["InstanceArn"], "members": {"InstanceArn": {}, "NextToken": {}, "MaxResults": {"type": "integer"}}}, "output": {"type": "structure", "members": {"PermissionSets": {"shape": "S34"}, "NextToken": {}}}}, "ListPermissionSetsProvisionedToAccount": {"input": {"type": "structure", "required": ["InstanceArn", "AccountId"], "members": {"InstanceArn": {}, "AccountId": {}, "ProvisioningStatus": {}, "MaxResults": {"type": "integer"}, "NextToken": {}}}, "output": {"type": "structure", "members": {"NextToken": {}, "PermissionSets": {"shape": "S34"}}}}, "ListTagsForResource": {"input": {"type": "structure", "required": ["InstanceArn", "ResourceArn"], "members": {"InstanceArn": {}, "ResourceArn": {}, "NextToken": {}}}, "output": {"type": "structure", "members": {"Tags": {"shape": "S10"}, "NextToken": {}}}}, "ProvisionPermissionSet": {"input": {"type": "structure", "required": ["InstanceArn", "PermissionSetArn", "TargetType"], "members": {"InstanceArn": {}, "PermissionSetArn": {}, "TargetId": {}, "TargetType": {}}}, "output": {"type": "structure", "members": {"PermissionSetProvisioningStatus": {"shape": "S1s"}}}}, "PutInlinePolicyToPermissionSet": {"input": {"type": "structure", "required": ["InstanceArn", "PermissionSetArn", "InlinePolicy"], "members": {"InstanceArn": {}, "PermissionSetArn": {}, "InlinePolicy": {}}}, "output": {"type": "structure", "members": {}}}, "PutPermissionsBoundaryToPermissionSet": {"input": {"type": "structure", "required": ["InstanceArn", "PermissionSetArn", "PermissionsBoundary"], "members": {"InstanceArn": {}, "PermissionSetArn": {}, "PermissionsBoundary": {"shape": "S23"}}}, "output": {"type": "structure", "members": {}}}, "TagResource": {"input": {"type": "structure", "required": ["InstanceArn", "ResourceArn", "Tags"], "members": {"InstanceArn": {}, "ResourceArn": {}, "Tags": {"shape": "S10"}}}, "output": {"type": "structure", "members": {}}}, "UntagResource": {"input": {"type": "structure", "required": ["InstanceArn", "ResourceArn", "TagKeys"], "members": {"InstanceArn": {}, "ResourceArn": {}, "TagKeys": {"type": "list", "member": {}}}}, "output": {"type": "structure", "members": {}}}, "UpdateInstanceAccessControlAttributeConfiguration": {"input": {"type": "structure", "required": ["InstanceArn", "InstanceAccessControlAttributeConfiguration"], "members": {"InstanceArn": {}, "InstanceAccessControlAttributeConfiguration": {"shape": "Sn"}}}, "output": {"type": "structure", "members": {}}}, "UpdatePermissionSet": {"input": {"type": "structure", "required": ["InstanceArn", "PermissionSetArn"], "members": {"InstanceArn": {}, "PermissionSetArn": {}, "Description": {}, "SessionDuration": {}, "RelayState": {}}}, "output": {"type": "structure", "members": {}}}}, "shapes": {"S4": {"type": "structure", "required": ["Name"], "members": {"Name": {}, "Path": {}}}, "Sh": {"type": "structure", "members": {"Status": {}, "RequestId": {}, "FailureReason": {}, "TargetId": {}, "TargetType": {}, "PermissionSetArn": {}, "PrincipalType": {}, "PrincipalId": {}, "CreatedDate": {"type": "timestamp"}}}, "Sn": {"type": "structure", "required": ["AccessControlAttributes"], "members": {"AccessControlAttributes": {"type": "list", "member": {"type": "structure", "required": ["Key", "Value"], "members": {"Key": {}, "Value": {"type": "structure", "required": ["Source"], "members": {"Source": {"type": "list", "member": {}}}}}}}}}, "S10": {"type": "list", "member": {"type": "structure", "required": ["Key", "Value"], "members": {"Key": {}, "Value": {}}}}, "S15": {"type": "structure", "members": {"Name": {}, "PermissionSetArn": {}, "Description": {}, "CreatedDate": {"type": "timestamp"}, "SessionDuration": {}, "RelayState": {}}}, "S1s": {"type": "structure", "members": {"Status": {}, "RequestId": {}, "AccountId": {}, "PermissionSetArn": {}, "FailureReason": {}, "CreatedDate": {"type": "timestamp"}}}, "S23": {"type": "structure", "members": {"CustomerManagedPolicyReference": {"shape": "S4"}, "ManagedPolicyArn": {}}}, "S27": {"type": "structure", "members": {"Status": {}}}, "S29": {"type": "list", "member": {"type": "structure", "members": {"Status": {}, "RequestId": {}, "CreatedDate": {"type": "timestamp"}}}}, "S34": {"type": "list", "member": {}}}}