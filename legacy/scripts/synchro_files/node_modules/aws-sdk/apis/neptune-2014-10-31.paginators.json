{"pagination": {"DescribeDBClusterEndpoints": {"input_token": "<PERSON><PERSON>", "limit_key": "MaxRecords", "output_token": "<PERSON><PERSON>", "result_key": "DBClusterEndpoints"}, "DescribeDBClusterParameterGroups": {"input_token": "<PERSON><PERSON>", "limit_key": "MaxRecords", "output_token": "<PERSON><PERSON>", "result_key": "DBClusterParameterGroups"}, "DescribeDBClusterParameters": {"input_token": "<PERSON><PERSON>", "limit_key": "MaxRecords", "output_token": "<PERSON><PERSON>", "result_key": "Parameters"}, "DescribeDBClusterSnapshots": {"input_token": "<PERSON><PERSON>", "limit_key": "MaxRecords", "output_token": "<PERSON><PERSON>", "result_key": "DBClusterSnapshots"}, "DescribeDBClusters": {"input_token": "<PERSON><PERSON>", "limit_key": "MaxRecords", "output_token": "<PERSON><PERSON>", "result_key": "DBClusters"}, "DescribeDBEngineVersions": {"input_token": "<PERSON><PERSON>", "limit_key": "MaxRecords", "output_token": "<PERSON><PERSON>", "result_key": "DBEngineVersions"}, "DescribeDBInstances": {"input_token": "<PERSON><PERSON>", "limit_key": "MaxRecords", "output_token": "<PERSON><PERSON>", "result_key": "DBInstances"}, "DescribeDBParameterGroups": {"input_token": "<PERSON><PERSON>", "limit_key": "MaxRecords", "output_token": "<PERSON><PERSON>", "result_key": "DBParameterGroups"}, "DescribeDBParameters": {"input_token": "<PERSON><PERSON>", "limit_key": "MaxRecords", "output_token": "<PERSON><PERSON>", "result_key": "Parameters"}, "DescribeDBSubnetGroups": {"input_token": "<PERSON><PERSON>", "limit_key": "MaxRecords", "output_token": "<PERSON><PERSON>", "result_key": "DBSubnetGroups"}, "DescribeEngineDefaultParameters": {"input_token": "<PERSON><PERSON>", "limit_key": "MaxRecords", "output_token": "EngineDefaults.Marker", "result_key": "EngineDefaults.Parameters"}, "DescribeEventSubscriptions": {"input_token": "<PERSON><PERSON>", "limit_key": "MaxRecords", "output_token": "<PERSON><PERSON>", "result_key": "EventSubscriptionsList"}, "DescribeEvents": {"input_token": "<PERSON><PERSON>", "limit_key": "MaxRecords", "output_token": "<PERSON><PERSON>", "result_key": "Events"}, "DescribeGlobalClusters": {"input_token": "<PERSON><PERSON>", "limit_key": "MaxRecords", "output_token": "<PERSON><PERSON>", "result_key": "GlobalClusters"}, "DescribeOrderableDBInstanceOptions": {"input_token": "<PERSON><PERSON>", "limit_key": "MaxRecords", "output_token": "<PERSON><PERSON>", "result_key": "OrderableDBInstanceOptions"}, "DescribePendingMaintenanceActions": {"input_token": "<PERSON><PERSON>", "limit_key": "MaxRecords", "output_token": "<PERSON><PERSON>", "result_key": "PendingMaintenanceActions"}, "ListTagsForResource": {"result_key": "TagList"}}}