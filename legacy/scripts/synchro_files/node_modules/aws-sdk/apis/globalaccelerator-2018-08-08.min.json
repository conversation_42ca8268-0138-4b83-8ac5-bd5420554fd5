{"version": "2.0", "metadata": {"apiVersion": "2018-08-08", "endpointPrefix": "globalaccelerator", "jsonVersion": "1.1", "protocol": "json", "serviceFullName": "AWS Global Accelerator", "serviceId": "Global Accelerator", "signatureVersion": "v4", "signingName": "globalaccelerator", "targetPrefix": "GlobalAccelerator_V20180706", "uid": "globalaccelerator-2018-08-08"}, "operations": {"AddCustomRoutingEndpoints": {"input": {"type": "structure", "required": ["EndpointConfigurations", "EndpointGroupArn"], "members": {"EndpointConfigurations": {"type": "list", "member": {"type": "structure", "members": {"EndpointId": {}}}}, "EndpointGroupArn": {}}}, "output": {"type": "structure", "members": {"EndpointDescriptions": {"shape": "S6"}, "EndpointGroupArn": {}}}}, "AddEndpoints": {"input": {"type": "structure", "required": ["EndpointConfigurations", "EndpointGroupArn"], "members": {"EndpointConfigurations": {"shape": "S9"}, "EndpointGroupArn": {}}}, "output": {"type": "structure", "members": {"EndpointDescriptions": {"shape": "Se"}, "EndpointGroupArn": {}}}}, "AdvertiseByoipCidr": {"input": {"type": "structure", "required": ["<PERSON><PERSON><PERSON>"], "members": {"Cidr": {}}}, "output": {"type": "structure", "members": {"ByoipCidr": {"shape": "Sj"}}}}, "AllowCustomRoutingTraffic": {"input": {"type": "structure", "required": ["EndpointGroupArn", "EndpointId"], "members": {"EndpointGroupArn": {}, "EndpointId": {}, "DestinationAddresses": {"shape": "Sp"}, "DestinationPorts": {"shape": "<PERSON>"}, "AllowAllTrafficToEndpoint": {"type": "boolean"}}}}, "CreateAccelerator": {"input": {"type": "structure", "required": ["Name", "IdempotencyToken"], "members": {"Name": {}, "IpAddressType": {}, "IpAddresses": {"shape": "Sv"}, "Enabled": {"type": "boolean"}, "IdempotencyToken": {"idempotencyToken": true}, "Tags": {"shape": "Sx"}}}, "output": {"type": "structure", "members": {"Accelerator": {"shape": "S12"}}}}, "CreateCustomRoutingAccelerator": {"input": {"type": "structure", "required": ["Name", "IdempotencyToken"], "members": {"Name": {}, "IpAddressType": {}, "IpAddresses": {"shape": "Sv"}, "Enabled": {"type": "boolean"}, "IdempotencyToken": {"idempotencyToken": true}, "Tags": {"shape": "Sx"}}}, "output": {"type": "structure", "members": {"Accelerator": {"shape": "S1b"}}}}, "CreateCustomRoutingEndpointGroup": {"input": {"type": "structure", "required": ["Listener<PERSON>rn", "EndpointGroupRegion", "DestinationConfigurations", "IdempotencyToken"], "members": {"ListenerArn": {}, "EndpointGroupRegion": {}, "DestinationConfigurations": {"type": "list", "member": {"type": "structure", "required": ["FromPort", "ToPort", "Protocols"], "members": {"FromPort": {"type": "integer"}, "ToPort": {"type": "integer"}, "Protocols": {"shape": "S1g"}}}}, "IdempotencyToken": {"idempotencyToken": true}}}, "output": {"type": "structure", "members": {"EndpointGroup": {"shape": "S1j"}}}}, "CreateCustomRoutingListener": {"input": {"type": "structure", "required": ["AcceleratorArn", "PortRanges", "IdempotencyToken"], "members": {"AcceleratorArn": {}, "PortRanges": {"shape": "S1p"}, "IdempotencyToken": {"idempotencyToken": true}}}, "output": {"type": "structure", "members": {"Listener": {"shape": "S1s"}}}}, "CreateEndpointGroup": {"input": {"type": "structure", "required": ["Listener<PERSON>rn", "EndpointGroupRegion", "IdempotencyToken"], "members": {"ListenerArn": {}, "EndpointGroupRegion": {}, "EndpointConfigurations": {"shape": "S9"}, "TrafficDialPercentage": {"type": "float"}, "HealthCheckPort": {"type": "integer"}, "HealthCheckProtocol": {}, "HealthCheckPath": {}, "HealthCheckIntervalSeconds": {"type": "integer"}, "ThresholdCount": {"type": "integer"}, "IdempotencyToken": {"idempotencyToken": true}, "PortOverrides": {"shape": "S20"}}}, "output": {"type": "structure", "members": {"EndpointGroup": {"shape": "S23"}}}}, "CreateListener": {"input": {"type": "structure", "required": ["AcceleratorArn", "PortRanges", "Protocol", "IdempotencyToken"], "members": {"AcceleratorArn": {}, "PortRanges": {"shape": "S1p"}, "Protocol": {}, "ClientAffinity": {}, "IdempotencyToken": {"idempotencyToken": true}}}, "output": {"type": "structure", "members": {"Listener": {"shape": "S27"}}}}, "DeleteAccelerator": {"input": {"type": "structure", "required": ["AcceleratorArn"], "members": {"AcceleratorArn": {}}}}, "DeleteCustomRoutingAccelerator": {"input": {"type": "structure", "required": ["AcceleratorArn"], "members": {"AcceleratorArn": {}}}}, "DeleteCustomRoutingEndpointGroup": {"input": {"type": "structure", "required": ["EndpointGroupArn"], "members": {"EndpointGroupArn": {}}}}, "DeleteCustomRoutingListener": {"input": {"type": "structure", "required": ["Listener<PERSON>rn"], "members": {"ListenerArn": {}}}}, "DeleteEndpointGroup": {"input": {"type": "structure", "required": ["EndpointGroupArn"], "members": {"EndpointGroupArn": {}}}}, "DeleteListener": {"input": {"type": "structure", "required": ["Listener<PERSON>rn"], "members": {"ListenerArn": {}}}}, "DenyCustomRoutingTraffic": {"input": {"type": "structure", "required": ["EndpointGroupArn", "EndpointId"], "members": {"EndpointGroupArn": {}, "EndpointId": {}, "DestinationAddresses": {"shape": "Sp"}, "DestinationPorts": {"shape": "<PERSON>"}, "DenyAllTrafficToEndpoint": {"type": "boolean"}}}}, "DeprovisionByoipCidr": {"input": {"type": "structure", "required": ["<PERSON><PERSON><PERSON>"], "members": {"Cidr": {}}}, "output": {"type": "structure", "members": {"ByoipCidr": {"shape": "Sj"}}}}, "DescribeAccelerator": {"input": {"type": "structure", "required": ["AcceleratorArn"], "members": {"AcceleratorArn": {}}}, "output": {"type": "structure", "members": {"Accelerator": {"shape": "S12"}}}}, "DescribeAcceleratorAttributes": {"input": {"type": "structure", "required": ["AcceleratorArn"], "members": {"AcceleratorArn": {}}}, "output": {"type": "structure", "members": {"AcceleratorAttributes": {"shape": "S2l"}}}}, "DescribeCustomRoutingAccelerator": {"input": {"type": "structure", "required": ["AcceleratorArn"], "members": {"AcceleratorArn": {}}}, "output": {"type": "structure", "members": {"Accelerator": {"shape": "S1b"}}}}, "DescribeCustomRoutingAcceleratorAttributes": {"input": {"type": "structure", "required": ["AcceleratorArn"], "members": {"AcceleratorArn": {}}}, "output": {"type": "structure", "members": {"AcceleratorAttributes": {"shape": "S2q"}}}}, "DescribeCustomRoutingEndpointGroup": {"input": {"type": "structure", "required": ["EndpointGroupArn"], "members": {"EndpointGroupArn": {}}}, "output": {"type": "structure", "members": {"EndpointGroup": {"shape": "S1j"}}}}, "DescribeCustomRoutingListener": {"input": {"type": "structure", "required": ["Listener<PERSON>rn"], "members": {"ListenerArn": {}}}, "output": {"type": "structure", "members": {"Listener": {"shape": "S1s"}}}}, "DescribeEndpointGroup": {"input": {"type": "structure", "required": ["EndpointGroupArn"], "members": {"EndpointGroupArn": {}}}, "output": {"type": "structure", "members": {"EndpointGroup": {"shape": "S23"}}}}, "DescribeListener": {"input": {"type": "structure", "required": ["Listener<PERSON>rn"], "members": {"ListenerArn": {}}}, "output": {"type": "structure", "members": {"Listener": {"shape": "S27"}}}}, "ListAccelerators": {"input": {"type": "structure", "members": {"MaxResults": {"type": "integer"}, "NextToken": {}}}, "output": {"type": "structure", "members": {"Accelerators": {"type": "list", "member": {"shape": "S12"}}, "NextToken": {}}}}, "ListByoipCidrs": {"input": {"type": "structure", "members": {"MaxResults": {"type": "integer"}, "NextToken": {}}}, "output": {"type": "structure", "members": {"ByoipCidrs": {"type": "list", "member": {"shape": "Sj"}}, "NextToken": {}}}}, "ListCustomRoutingAccelerators": {"input": {"type": "structure", "members": {"MaxResults": {"type": "integer"}, "NextToken": {}}}, "output": {"type": "structure", "members": {"Accelerators": {"type": "list", "member": {"shape": "S1b"}}, "NextToken": {}}}}, "ListCustomRoutingEndpointGroups": {"input": {"type": "structure", "required": ["Listener<PERSON>rn"], "members": {"ListenerArn": {}, "MaxResults": {"type": "integer"}, "NextToken": {}}}, "output": {"type": "structure", "members": {"EndpointGroups": {"type": "list", "member": {"shape": "S1j"}}, "NextToken": {}}}}, "ListCustomRoutingListeners": {"input": {"type": "structure", "required": ["AcceleratorArn"], "members": {"AcceleratorArn": {}, "MaxResults": {"type": "integer"}, "NextToken": {}}}, "output": {"type": "structure", "members": {"Listeners": {"type": "list", "member": {"shape": "S1s"}}, "NextToken": {}}}}, "ListCustomRoutingPortMappings": {"input": {"type": "structure", "required": ["AcceleratorArn"], "members": {"AcceleratorArn": {}, "EndpointGroupArn": {}, "MaxResults": {"type": "integer"}, "NextToken": {}}}, "output": {"type": "structure", "members": {"PortMappings": {"type": "list", "member": {"type": "structure", "members": {"AcceleratorPort": {"type": "integer"}, "EndpointGroupArn": {}, "EndpointId": {}, "DestinationSocketAddress": {"shape": "S3k"}, "Protocols": {"shape": "S1g"}, "DestinationTrafficState": {}}}}, "NextToken": {}}}}, "ListCustomRoutingPortMappingsByDestination": {"input": {"type": "structure", "required": ["EndpointId", "DestinationAddress"], "members": {"EndpointId": {}, "DestinationAddress": {}, "MaxResults": {"type": "integer"}, "NextToken": {}}}, "output": {"type": "structure", "members": {"DestinationPortMappings": {"type": "list", "member": {"type": "structure", "members": {"AcceleratorArn": {}, "AcceleratorSocketAddresses": {"type": "list", "member": {"shape": "S3k"}}, "EndpointGroupArn": {}, "EndpointId": {}, "EndpointGroupRegion": {}, "DestinationSocketAddress": {"shape": "S3k"}, "IpAddressType": {}, "DestinationTrafficState": {}}}}, "NextToken": {}}}}, "ListEndpointGroups": {"input": {"type": "structure", "required": ["Listener<PERSON>rn"], "members": {"ListenerArn": {}, "MaxResults": {"type": "integer"}, "NextToken": {}}}, "output": {"type": "structure", "members": {"EndpointGroups": {"type": "list", "member": {"shape": "S23"}}, "NextToken": {}}}}, "ListListeners": {"input": {"type": "structure", "required": ["AcceleratorArn"], "members": {"AcceleratorArn": {}, "MaxResults": {"type": "integer"}, "NextToken": {}}}, "output": {"type": "structure", "members": {"Listeners": {"type": "list", "member": {"shape": "S27"}}, "NextToken": {}}}}, "ListTagsForResource": {"input": {"type": "structure", "required": ["ResourceArn"], "members": {"ResourceArn": {}}}, "output": {"type": "structure", "members": {"Tags": {"shape": "Sx"}}}}, "ProvisionByoipCidr": {"input": {"type": "structure", "required": ["<PERSON><PERSON><PERSON>", "CidrAuthorizationContext"], "members": {"Cidr": {}, "CidrAuthorizationContext": {"type": "structure", "required": ["Message", "Signature"], "members": {"Message": {}, "Signature": {}}}}}, "output": {"type": "structure", "members": {"ByoipCidr": {"shape": "Sj"}}}}, "RemoveCustomRoutingEndpoints": {"input": {"type": "structure", "required": ["EndpointIds", "EndpointGroupArn"], "members": {"EndpointIds": {"type": "list", "member": {}}, "EndpointGroupArn": {}}}}, "RemoveEndpoints": {"input": {"type": "structure", "required": ["EndpointIdentifiers", "EndpointGroupArn"], "members": {"EndpointIdentifiers": {"type": "list", "member": {"type": "structure", "required": ["EndpointId"], "members": {"EndpointId": {}, "ClientIPPreservationEnabled": {"type": "boolean"}}}}, "EndpointGroupArn": {}}}}, "TagResource": {"input": {"type": "structure", "required": ["ResourceArn", "Tags"], "members": {"ResourceArn": {}, "Tags": {"shape": "Sx"}}}, "output": {"type": "structure", "members": {}}}, "UntagResource": {"input": {"type": "structure", "required": ["ResourceArn", "TagKeys"], "members": {"ResourceArn": {}, "TagKeys": {"type": "list", "member": {}}}}, "output": {"type": "structure", "members": {}}}, "UpdateAccelerator": {"input": {"type": "structure", "required": ["AcceleratorArn"], "members": {"AcceleratorArn": {}, "Name": {}, "IpAddressType": {}, "Enabled": {"type": "boolean"}}}, "output": {"type": "structure", "members": {"Accelerator": {"shape": "S12"}}}}, "UpdateAcceleratorAttributes": {"input": {"type": "structure", "required": ["AcceleratorArn"], "members": {"AcceleratorArn": {}, "FlowLogsEnabled": {"type": "boolean"}, "FlowLogsS3Bucket": {}, "FlowLogsS3Prefix": {}}}, "output": {"type": "structure", "members": {"AcceleratorAttributes": {"shape": "S2l"}}}}, "UpdateCustomRoutingAccelerator": {"input": {"type": "structure", "required": ["AcceleratorArn"], "members": {"AcceleratorArn": {}, "Name": {}, "IpAddressType": {}, "Enabled": {"type": "boolean"}}}, "output": {"type": "structure", "members": {"Accelerator": {"shape": "S1b"}}}}, "UpdateCustomRoutingAcceleratorAttributes": {"input": {"type": "structure", "required": ["AcceleratorArn"], "members": {"AcceleratorArn": {}, "FlowLogsEnabled": {"type": "boolean"}, "FlowLogsS3Bucket": {}, "FlowLogsS3Prefix": {}}}, "output": {"type": "structure", "members": {"AcceleratorAttributes": {"shape": "S2q"}}}}, "UpdateCustomRoutingListener": {"input": {"type": "structure", "required": ["Listener<PERSON>rn", "PortRanges"], "members": {"ListenerArn": {}, "PortRanges": {"shape": "S1p"}}}, "output": {"type": "structure", "members": {"Listener": {"shape": "S1s"}}}}, "UpdateEndpointGroup": {"input": {"type": "structure", "required": ["EndpointGroupArn"], "members": {"EndpointGroupArn": {}, "EndpointConfigurations": {"shape": "S9"}, "TrafficDialPercentage": {"type": "float"}, "HealthCheckPort": {"type": "integer"}, "HealthCheckProtocol": {}, "HealthCheckPath": {}, "HealthCheckIntervalSeconds": {"type": "integer"}, "ThresholdCount": {"type": "integer"}, "PortOverrides": {"shape": "S20"}}}, "output": {"type": "structure", "members": {"EndpointGroup": {"shape": "S23"}}}}, "UpdateListener": {"input": {"type": "structure", "required": ["Listener<PERSON>rn"], "members": {"ListenerArn": {}, "PortRanges": {"shape": "S1p"}, "Protocol": {}, "ClientAffinity": {}}}, "output": {"type": "structure", "members": {"Listener": {"shape": "S27"}}}}, "WithdrawByoipCidr": {"input": {"type": "structure", "required": ["<PERSON><PERSON><PERSON>"], "members": {"Cidr": {}}}, "output": {"type": "structure", "members": {"ByoipCidr": {"shape": "Sj"}}}}}, "shapes": {"S6": {"type": "list", "member": {"type": "structure", "members": {"EndpointId": {}}}}, "S9": {"type": "list", "member": {"type": "structure", "members": {"EndpointId": {}, "Weight": {"type": "integer"}, "ClientIPPreservationEnabled": {"type": "boolean"}}}}, "Se": {"type": "list", "member": {"type": "structure", "members": {"EndpointId": {}, "Weight": {"type": "integer"}, "HealthState": {}, "HealthReason": {}, "ClientIPPreservationEnabled": {"type": "boolean"}}}}, "Sj": {"type": "structure", "members": {"Cidr": {}, "State": {}, "Events": {"type": "list", "member": {"type": "structure", "members": {"Message": {}, "Timestamp": {"type": "timestamp"}}}}}}, "Sp": {"type": "list", "member": {}}, "Sr": {"type": "list", "member": {"type": "integer"}}, "Sv": {"type": "list", "member": {}}, "Sx": {"type": "list", "member": {"type": "structure", "required": ["Key", "Value"], "members": {"Key": {}, "Value": {}}}}, "S12": {"type": "structure", "members": {"AcceleratorArn": {}, "Name": {}, "IpAddressType": {}, "Enabled": {"type": "boolean"}, "IpSets": {"shape": "S13"}, "DnsName": {}, "Status": {}, "CreatedTime": {"type": "timestamp"}, "LastModifiedTime": {"type": "timestamp"}, "DualStackDnsName": {}, "Events": {"type": "list", "member": {"type": "structure", "members": {"Message": {}, "Timestamp": {"type": "timestamp"}}}}}}, "S13": {"type": "list", "member": {"type": "structure", "members": {"IpFamily": {"deprecated": true, "deprecatedMessage": "IpFamily has been replaced by IpAddressFamily"}, "IpAddresses": {"shape": "Sv"}, "IpAddressFamily": {}}}}, "S1b": {"type": "structure", "members": {"AcceleratorArn": {}, "Name": {}, "IpAddressType": {}, "Enabled": {"type": "boolean"}, "IpSets": {"shape": "S13"}, "DnsName": {}, "Status": {}, "CreatedTime": {"type": "timestamp"}, "LastModifiedTime": {"type": "timestamp"}}}, "S1g": {"type": "list", "member": {}}, "S1j": {"type": "structure", "members": {"EndpointGroupArn": {}, "EndpointGroupRegion": {}, "DestinationDescriptions": {"type": "list", "member": {"type": "structure", "members": {"FromPort": {"type": "integer"}, "ToPort": {"type": "integer"}, "Protocols": {"type": "list", "member": {}}}}}, "EndpointDescriptions": {"shape": "S6"}}}, "S1p": {"type": "list", "member": {"type": "structure", "members": {"FromPort": {"type": "integer"}, "ToPort": {"type": "integer"}}}}, "S1s": {"type": "structure", "members": {"ListenerArn": {}, "PortRanges": {"shape": "S1p"}}}, "S20": {"type": "list", "member": {"type": "structure", "members": {"ListenerPort": {"type": "integer"}, "EndpointPort": {"type": "integer"}}}}, "S23": {"type": "structure", "members": {"EndpointGroupArn": {}, "EndpointGroupRegion": {}, "EndpointDescriptions": {"shape": "Se"}, "TrafficDialPercentage": {"type": "float"}, "HealthCheckPort": {"type": "integer"}, "HealthCheckProtocol": {}, "HealthCheckPath": {}, "HealthCheckIntervalSeconds": {"type": "integer"}, "ThresholdCount": {"type": "integer"}, "PortOverrides": {"shape": "S20"}}}, "S27": {"type": "structure", "members": {"ListenerArn": {}, "PortRanges": {"shape": "S1p"}, "Protocol": {}, "ClientAffinity": {}}}, "S2l": {"type": "structure", "members": {"FlowLogsEnabled": {"type": "boolean"}, "FlowLogsS3Bucket": {}, "FlowLogsS3Prefix": {}}}, "S2q": {"type": "structure", "members": {"FlowLogsEnabled": {"type": "boolean"}, "FlowLogsS3Bucket": {}, "FlowLogsS3Prefix": {}}}, "S3k": {"type": "structure", "members": {"IpAddress": {}, "Port": {"type": "integer"}}}}}