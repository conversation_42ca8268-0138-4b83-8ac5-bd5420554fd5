{"version": "1.0", "examples": {"AcceptHandshake": [{"input": {"HandshakeId": "h-examplehandshakeid111"}, "output": {"Handshake": {"Action": "INVITE", "Arn": "arn:aws:organizations::************:handshake/o-exampleorgid/invite/h-examplehandshakeid111", "ExpirationTimestamp": "20170228T1215Z", "Id": "h-examplehandshakeid111", "Parties": [{"Id": "o-exampleorgid", "Type": "ORGANIZATION"}, {"Id": "<EMAIL>", "Type": "EMAIL"}], "RequestedTimestamp": "20170214T1215Z", "Resources": [{"Resources": [{"Type": "MASTER_EMAIL", "Value": "<EMAIL>"}, {"Type": "MASTER_NAME", "Value": "Org Master Account"}, {"Type": "ORGANIZATION_FEATURE_SET", "Value": "ALL"}], "Type": "ORGANIZATION", "Value": "o-exampleorgid"}, {"Type": "ACCOUNT", "Value": "************"}], "State": "ACCEPTED"}}, "comments": {"input": {}, "output": {}}, "description": "<PERSON> is the owner of an organization, and he invites <PERSON>'s account (************) to join his organization. The following example shows <PERSON>'s account accepting the handshake and thus agreeing to the invitation.", "id": "to-accept-a-handshake-from-another-account-*************", "title": "To accept a handshake from another account"}], "AttachPolicy": [{"input": {"PolicyId": "p-examplepolicyid111", "TargetId": "ou-examplerootid111-exampleouid111"}, "comments": {"input": {}, "output": {}}, "description": "The following example shows how to attach a service control policy (SCP) to an OU:\n", "id": "to-attach-a-policy-to-an-ou", "title": "To attach a policy to an OU"}, {"input": {"PolicyId": "p-examplepolicyid111", "TargetId": "************"}, "comments": {"input": {}, "output": {}}, "description": "The following example shows how to attach a service control policy (SCP) to an account:\n", "id": "to-attach-a-policy-to-an-account", "title": "To attach a policy to an account"}], "CancelHandshake": [{"input": {"HandshakeId": "h-examplehandshakeid111"}, "output": {"Handshake": {"Action": "INVITE", "Arn": "arn:aws:organizations::************:handshake/o-exampleorgid/invite/h-examplehandshakeid111", "ExpirationTimestamp": "20170228T1215Z", "Id": "h-examplehandshakeid111", "Parties": [{"Id": "o-exampleorgid", "Type": "ORGANIZATION"}, {"Id": "<EMAIL>", "Type": "EMAIL"}], "RequestedTimestamp": "20170214T1215Z", "Resources": [{"Resources": [{"Type": "MASTER_EMAIL", "Value": "<EMAIL>"}, {"Type": "MASTER_NAME", "Value": "Master Account"}, {"Type": "ORGANIZATION_FEATURE_SET", "Value": "CONSOLIDATED_BILLING"}], "Type": "ORGANIZATION", "Value": "o-exampleorgid"}, {"Type": "ACCOUNT", "Value": "************"}, {"Type": "NOTES", "Value": "This is a request for <PERSON>'s account to join <PERSON>'s organization."}], "State": "CANCELED"}}, "comments": {"input": {}, "output": {}}, "description": "<PERSON> previously sent an invitation to <PERSON>'s account to join his organization. He changes his mind and decides to cancel the invitation before <PERSON> accepts it. The following example shows <PERSON>'s cancellation:\n", "id": "to-cancel-a-handshake-sent-to-a-member-account-*************", "title": "To cancel a handshake sent to a member account"}], "CreateAccount": [{"input": {"AccountName": "Production Account", "Email": "<EMAIL>"}, "output": {"CreateAccountStatus": {"Id": "car-examplecreateaccountrequestid111", "State": "IN_PROGRESS"}}, "comments": {"input": {}, "output": {}}, "description": "The owner of an organization creates a member account in the organization. The following example shows that when the organization owner creates the member account, the account is preconfigured with the name \"Production Account\" and an owner email <NAME_EMAIL>.  An IAM role is automatically created using the default name because the roleName parameter is not used. AWS Organizations sends <PERSON> a \"Welcome to AWS\" email:\n\n", "id": "to-create-a-new-account-that-is-automatically-part-of-the-organization-*************", "title": "To create a new account that is automatically part of the organization"}], "CreateOrganization": [{"input": {}, "output": {"Organization": {"Arn": "arn:aws:organizations::************:organization/o-exampleorgid", "AvailablePolicyTypes": [{"Status": "ENABLED", "Type": "SERVICE_CONTROL_POLICY"}], "FeatureSet": "ALL", "Id": "o-exampleorgid", "MasterAccountArn": "arn:aws:organizations::************:account/o-exampleorgid/************", "MasterAccountEmail": "<EMAIL>", "MasterAccountId": "************"}}, "comments": {"input": {}, "output": {}}, "description": "<PERSON> wants to create an organization using credentials from account ************. The following example shows that the account becomes the master account in the new organization. Because he does not specify a feature set, the new organization defaults to all features enabled and service control policies enabled on the root:\n\n", "id": "to-create-a-new-organization-with-all-features enabled", "title": "To create a new organization with all features enabled"}, {"input": {"FeatureSet": "CONSOLIDATED_BILLING"}, "output": {"Organization": {"Arn": "arn:aws:organizations::************:organization/o-exampleorgid", "AvailablePolicyTypes": [], "FeatureSet": "CONSOLIDATED_BILLING", "Id": "o-exampleorgid", "MasterAccountArn": "arn:aws:organizations::************:account/o-exampleorgid/************", "MasterAccountEmail": "<EMAIL>", "MasterAccountId": "************"}}, "comments": {"input": {}, "output": {}}, "description": "In the following example, <PERSON> creates an organization using credentials from account ************, and configures the organization to support only the consolidated billing feature set:\n\n", "id": "to-create-a-new-organization-with-consolidated-billing-features-only", "title": "To create a new organization with consolidated billing features only"}], "CreateOrganizationalUnit": [{"input": {"Name": "AccountingOU", "ParentId": "r-examplerootid111"}, "output": {"OrganizationalUnit": {"Arn": "arn:aws:organizations::************:ou/o-exampleorgid/ou-examplerootid111-exampleouid111", "Id": "ou-examplerootid111-exampleouid111", "Name": "AccountingOU"}}, "comments": {"input": {}, "output": {}}, "description": "The following example shows how to create an OU that is named AccountingOU. The new OU is directly under the root.:\n\n", "id": "to-create-a-new-organizational-unit", "title": "To create a new organization unit"}], "CreatePolicy": [{"input": {"Content": "{\\\"Version\\\":\\\"2012-10-17\\\",\\\"Statement\\\":{\\\"Effect\\\":\\\"Allow\\\",\\\"Action\\\":\\\"s3:*\\\"}}", "Description": "Enables admins of attached accounts to delegate all S3 permissions", "Name": "AllowAllS3Actions", "Type": "SERVICE_CONTROL_POLICY"}, "output": {"Policy": {"Content": "{\"Version\":\"2012-10-17\",\"Statement\":{\"Effect\":\"Allow\",\"Action\":\"s3:*\"}}", "PolicySummary": {"Arn": "arn:aws:organizations::************:policy/o-exampleorgid/service_control_policy/p-examplepolicyid111", "Description": "Allows delegation of all S3 actions", "Name": "AllowAllS3Actions", "Type": "SERVICE_CONTROL_POLICY"}}}, "comments": {"input": {}, "output": {}}, "description": "The following example shows how to create a service control policy (SCP) that is named AllowAllS3Actions. The JSON string in the content parameter specifies the content in the policy. The parameter string is escaped with backslashes to ensure that the embedded double quotes in the JSON policy are treated as literals in the parameter, which itself is surrounded by double quotes:\n\n", "id": "to-create-a-service-control-policy", "title": "To create a service control policy"}], "DeclineHandshake": [{"input": {"HandshakeId": "h-examplehandshakeid111"}, "output": {"Handshake": {"Action": "INVITE", "Arn": "arn:aws:organizations::************:handshake/o-exampleorgid/invite/h-examplehandshakeid111", "ExpirationTimestamp": "2016-12-15T19:27:58Z", "Id": "h-examplehandshakeid111", "Parties": [{"Id": "************", "Type": "ACCOUNT"}, {"Id": "o-exampleorgid", "Type": "ORGANIZATION"}], "RequestedTimestamp": "2016-11-30T19:27:58Z", "Resources": [{"Resources": [{"Type": "MASTER_EMAIL", "Value": "<EMAIL>"}, {"Type": "MASTER_NAME", "Value": "Master Account"}], "Type": "ORGANIZATION", "Value": "o-exampleorgid"}, {"Type": "ACCOUNT", "Value": "************"}, {"Type": "NOTES", "Value": "This is an invitation to <PERSON>'s account to join the Bill's organization."}], "State": "DECLINED"}}, "comments": {"input": {}, "output": {}}, "description": "The following example shows <PERSON> declining an invitation to join <PERSON>'s organization. The DeclineHandshake operation returns a handshake object, showing that the state is now DECLINED:", "id": "to-decline-a-handshake-sent-from-the-master-account-*************", "title": "To decline a handshake sent from the master account"}], "DeleteOrganizationalUnit": [{"input": {"OrganizationalUnitId": "ou-examplerootid111-exampleouid111"}, "comments": {"input": {}, "output": {}}, "description": "The following example shows how to delete an OU. The example assumes that you previously removed all accounts and other OUs from the OU:\n\n", "id": "to-delete-an-organizational-unit", "title": "To delete an organization unit"}], "DeletePolicy": [{"input": {"PolicyId": "p-examplepolicyid111"}, "comments": {"input": {}, "output": {}}, "description": "The following example shows how to delete a policy from an organization. The example assumes that you previously detached the policy from all entities:\n\n", "id": "to-delete-a-policy", "title": "To delete a policy"}], "DescribeAccount": [{"input": {"AccountId": "************"}, "output": {"Account": {"Arn": "arn:aws:organizations::************:account/o-exampleorgid/************", "Email": "<EMAIL>", "Id": "************", "Name": "Beta Account"}}, "comments": {"input": {}, "output": {}}, "description": "The following example shows a user in the master account (************) asking for details about account ************:", "id": "to-get-the-details-about-an-account-*************", "title": "To get the details about an account"}], "DescribeCreateAccountStatus": [{"input": {"CreateAccountRequestId": "car-exampleaccountcreationrequestid"}, "output": {"CreateAccountStatus": {"AccountId": "************", "Id": "car-exampleaccountcreationrequestid", "State": "SUCCEEDED"}}, "comments": {"input": {}, "output": {}}, "description": "The following example shows how to request the status about a previous request to create an account in an organization. This operation can be called only by a principal from the organization's master account. In the example, the specified \"createAccountRequestId\" comes from the response of the original call to \"CreateAccount\":", "id": "to-get-information-about-a-request-to-create-an-account-*************", "title": "To get information about a request to create an account"}], "DescribeHandshake": [{"input": {"HandshakeId": "h-examplehandshakeid111"}, "output": {"Handshake": {"Action": "INVITE", "Arn": "arn:aws:organizations::************:handshake/o-exampleorgid/invite/h-examplehandshakeid111", "ExpirationTimestamp": "2016-11-30T17:24:58.046Z", "Id": "h-examplehandshakeid111", "Parties": [{"Id": "o-exampleorgid", "Type": "ORGANIZATION"}, {"Id": "************", "Type": "ACCOUNT"}], "RequestedTimestamp": "2016-11-30T17:24:58.046Z", "Resources": [{"Resources": [{"Type": "MASTER_EMAIL", "Value": "<EMAIL>"}, {"Type": "MASTER_NAME", "Value": "Master Account"}], "Type": "ORGANIZATION", "Value": "o-exampleorgid"}, {"Type": "ACCOUNT", "Value": "************"}], "State": "OPEN"}}, "comments": {"input": {}, "output": {}}, "description": "The following example shows you how to request details about a handshake. The handshake ID comes either from the original call to \"InviteAccountToOrganization\", or from a call to \"ListHandshakesForAccount\" or \"ListHandshakesForOrganization\":", "id": "to-get-information-about-a-handshake-*************", "title": "To get information about a handshake"}], "DescribeOrganization": [{"output": {"Organization": {"Arn": "arn:aws:organizations::************:organization/o-exampleorgid", "AvailablePolicyTypes": [{"Status": "ENABLED", "Type": "SERVICE_CONTROL_POLICY"}], "FeatureSet": "ALL", "Id": "o-exampleorgid", "MasterAccountArn": "arn:aws:organizations::************:account/o-exampleorgid/************", "MasterAccountEmail": "<EMAIL>"}}, "comments": {"input": {}, "output": {}}, "description": "The following example shows how to request information about the current user's organization:/n/n", "id": "to-get-information-about-an-organization-*************", "title": "To get information about an organization"}], "DescribeOrganizationalUnit": [{"input": {"OrganizationalUnitId": "ou-examplerootid111-exampleouid111"}, "output": {"OrganizationalUnit": {"Arn": "arn:aws:organizations::************:ou/o-exampleorgid/ou-examplerootid111-exampleouid111", "Id": "ou-examplerootid111-exampleouid111", "Name": "Accounting Group"}}, "comments": {"input": {}, "output": {}}, "description": "The following example shows how to request details about an OU:/n/n", "id": "to-get-information-about-an-organizational-unit", "title": "To get information about an organizational unit"}], "DescribePolicy": [{"input": {"PolicyId": "p-examplepolicyid111"}, "output": {"Policy": {"Content": "{\\n  \\\"Version\\\": \\\"2012-10-17\\\",\\n  \\\"Statement\\\": [\\n    {\\n      \\\"Effect\\\": \\\"Allow\\\",\\n      \\\"Action\\\": \\\"*\\\",\\n      \\\"Resource\\\": \\\"*\\\"\\n    }\\n  ]\\n}", "PolicySummary": {"Arn": "arn:aws:organizations::************:policy/o-exampleorgid/service_control_policy/p-examplepolicyid111", "AwsManaged": false, "Description": "Enables admins to delegate S3 permissions", "Id": "p-examplepolicyid111", "Name": "AllowAllS3Actions", "Type": "SERVICE_CONTROL_POLICY"}}}, "comments": {"input": {}, "output": {}}, "description": "The following example shows how to request information about a policy:/n/n", "id": "to-get-information-about-a-policy", "title": "To get information about a policy"}], "DetachPolicy": [{"input": {"PolicyId": "p-examplepolicyid111", "TargetId": "ou-examplerootid111-exampleouid111"}, "comments": {"input": {}, "output": {}}, "description": "The following example shows how to detach a policy from an OU:/n/n", "id": "to-detach-a-policy-from-a-root-ou-or-account", "title": "To detach a policy from a root, OU, or account"}], "DisablePolicyType": [{"input": {"PolicyType": "SERVICE_CONTROL_POLICY", "RootId": "r-examplerootid111"}, "output": {"Root": {"Arn": "arn:aws:organizations::************:root/o-exampleorgid/r-examplerootid111", "Id": "r-examplerootid111", "Name": "Root", "PolicyTypes": []}}, "comments": {"input": {}, "output": {}}, "description": "The following example shows how to disable the service control policy (SCP) policy type in a root. The response shows that the PolicyTypes response element no longer includes SERVICE_CONTROL_POLICY:/n/n", "id": "to-disable-a-policy-type-in-a-root", "title": "To disable a policy type in a root"}], "EnableAllFeatures": [{"input": {}, "output": {"Handshake": {"Action": "ENABLE_ALL_FEATURES", "Arn": "arn:aws:organizations::************:handshake/o-exampleorgid/enable_all_features/h-examplehandshakeid111", "ExpirationTimestamp": "2017-02-28T09:35:40.05Z", "Id": "h-examplehandshakeid111", "Parties": [{"Id": "o-exampleorgid", "Type": "ORGANIZATION"}], "RequestedTimestamp": "2017-02-13T09:35:40.05Z", "Resources": [{"Type": "ORGANIZATION", "Value": "o-exampleorgid"}], "State": "REQUESTED"}}, "comments": {"input": {}, "output": {}}, "description": "This example shows the administrator asking all the invited accounts in the organization to approve enabling all features in the organization. AWS Organizations sends an email to the address that is registered with every invited member account asking the owner to approve the change by accepting the handshake that is sent. After all invited member accounts accept the handshake, the organization administrator can finalize the change to enable all features, and those with appropriate permissions can create policies and apply them to roots, OUs, and accounts:/n/n", "id": "to-enable-all-features-in-an-organization", "title": "To enable all features in an organization"}], "EnablePolicyType": [{"input": {"PolicyType": "SERVICE_CONTROL_POLICY", "RootId": "r-examplerootid111"}, "output": {"Root": {"Arn": "arn:aws:organizations::************:root/o-exampleorgid/r-examplerootid111", "Id": "r-examplerootid111", "Name": "Root", "PolicyTypes": [{"Status": "ENABLED", "Type": "SERVICE_CONTROL_POLICY"}]}}, "comments": {"input": {}, "output": {}}, "description": "The following example shows how to enable the service control policy (SCP) policy type in a root. The output shows a root object with a PolicyTypes response element showing that SCPs are now enabled:/n/n", "id": "to-enable-a-policy-type-in-a-root", "title": "To enable a policy type in a root"}], "InviteAccountToOrganization": [{"input": {"Notes": "This is a request for <PERSON>'s account to join <PERSON>'s organization", "Target": {"Id": "<EMAIL>", "Type": "EMAIL"}}, "output": {"Handshake": {"Action": "INVITE", "Arn": "arn:aws:organizations::************:handshake/o-exampleorgid/invite/h-examplehandshakeid111", "ExpirationTimestamp": "2017-02-16T09:36:05.02Z", "Id": "h-examplehandshakeid111", "Parties": [{"Id": "o-exampleorgid", "Type": "ORGANIZATION"}, {"Id": "<EMAIL>", "Type": "EMAIL"}], "RequestedTimestamp": "2017-02-01T09:36:05.02Z", "Resources": [{"Resources": [{"Type": "MASTER_EMAIL", "Value": "<EMAIL>"}, {"Type": "MASTER_NAME", "Value": "Org Master Account"}, {"Type": "ORGANIZATION_FEATURE_SET", "Value": "FULL"}], "Type": "ORGANIZATION", "Value": "o-exampleorgid"}, {"Type": "EMAIL", "Value": "<EMAIL>"}], "State": "OPEN"}}, "comments": {"input": {}, "output": {}}, "description": "The following example shows the admin of the master account <NAME_EMAIL> inviting the account <NAME_EMAIL> to join an organization.", "id": "to-invite-an-account-to-join-an-organization-*************", "title": "To invite an account to join an organization"}], "LeaveOrganization": [{"comments": {"input": {}, "output": {}}, "description": "TThe following example shows how to remove your member account from an organization:", "id": "to-leave-an-organization-as-a-member-account-*************", "title": "To leave an organization as a member account"}], "ListAccounts": [{"input": {}, "output": {"Accounts": [{"Arn": "arn:aws:organizations::************:account/o-exampleorgid/************", "Email": "<EMAIL>", "Id": "************", "JoinedMethod": "INVITED", "JoinedTimestamp": "20161215T193015Z", "Name": "Master Account", "Status": "ACTIVE"}, {"Arn": "arn:aws:organizations::************:account/o-exampleorgid/************", "Email": "<EMAIL>", "Id": "************", "JoinedMethod": "INVITED", "JoinedTimestamp": "20161215T210221Z", "Name": "Dev<PERSON><PERSON> Account", "Status": "ACTIVE"}, {"Arn": "arn:aws:organizations::************:account/o-exampleorgid/************", "Email": "<EMAIL>", "Id": "************", "JoinedMethod": "INVITED", "JoinedTimestamp": "20161215T210347Z", "Name": "Test Account", "Status": "ACTIVE"}, {"Arn": "arn:aws:organizations::************:account/o-exampleorgid/************", "Email": "<EMAIL>", "Id": "************", "JoinedMethod": "INVITED", "JoinedTimestamp": "20161215T210332Z", "Name": "Production Account", "Status": "ACTIVE"}]}, "comments": {"input": {}, "output": {}}, "description": "The following example shows you how to request a list of the accounts in an organization:", "id": "to-retrieve-a-list-of-all-of-the-accounts-in-an-organization-*************", "title": "To retrieve a list of all of the accounts in an organization"}], "ListAccountsForParent": [{"input": {"ParentId": "ou-examplerootid111-exampleouid111"}, "output": {"Accounts": [{"Arn": "arn:aws:organizations::************:account/o-exampleorgid/************", "Email": "<EMAIL>", "Id": "************", "JoinedMethod": "INVITED", "JoinedTimestamp": **********.536, "Name": "Development Account", "Status": "ACTIVE"}, {"Arn": "arn:aws:organizations::************:account/o-exampleorgid/************", "Email": "<EMAIL>", "Id": "************", "JoinedMethod": "INVITED", "JoinedTimestamp": **********.143, "Name": "Test Account", "Status": "ACTIVE"}]}, "comments": {"input": {}, "output": {}}, "description": "The following example shows how to request a list of the accounts in an OU:/n/n", "id": "to-retrieve-a-list-of-all-of-the-accounts-in-a-root-or-ou-*************", "title": "To retrieve a list of all of the accounts in a root or OU"}], "ListChildren": [{"input": {"ChildType": "ORGANIZATIONAL_UNIT", "ParentId": "ou-examplerootid111-exampleouid111"}, "output": {"Children": [{"Id": "ou-examplerootid111-exampleouid111", "Type": "ORGANIZATIONAL_UNIT"}, {"Id": "ou-examplerootid111-exampleouid222", "Type": "ORGANIZATIONAL_UNIT"}]}, "comments": {"input": {}, "output": {}}, "description": "The following example shows how to request a list of the child OUs in a parent root or OU:/n/n", "id": "to-retrieve-a-list-of-all-of-the-child-accounts-and-OUs-in-a-parent-container", "title": "To retrieve a list of all of the child accounts and OUs in a parent root or OU"}], "ListCreateAccountStatus": [{"input": {"States": ["SUCCEEDED"]}, "output": {"CreateAccountStatuses": [{"AccountId": "************", "AccountName": "Developer Test Account", "CompletedTimestamp": "2017-01-15T13:45:23.6Z", "Id": "car-exampleaccountcreationrequestid1", "RequestedTimestamp": "2017-01-15T13:45:23.01Z", "State": "SUCCEEDED"}]}, "comments": {"input": {}, "output": {}}, "description": "The following example shows a user requesting a list of only the completed account creation requests made for the current organization:", "id": "to-get-a-list-of-completed-account-creation-requests-made-in-the-organization", "title": "To get a list of completed account creation requests made in the organization"}, {"input": {"States": ["IN_PROGRESS"]}, "output": {"CreateAccountStatuses": [{"AccountName": "Production Account", "Id": "car-exampleaccountcreationrequestid2", "RequestedTimestamp": "2017-01-15T13:45:23.01Z", "State": "IN_PROGRESS"}]}, "comments": {"input": {}, "output": {}}, "description": "The following example shows a user requesting a list of only the in-progress account creation requests made for the current organization:", "id": "to-get-a-list-of-all-account-creation-requests-made-in-the-organization-*************", "title": "To get a list of all account creation requests made in the organization"}], "ListHandshakesForAccount": [{"output": {"Handshakes": [{"Action": "INVITE", "Arn": "arn:aws:organizations::************:handshake/o-exampleorgid/invite/h-examplehandshakeid111", "ExpirationTimestamp": "2017-01-28T14:35:23.3Z", "Id": "h-examplehandshakeid111", "Parties": [{"Id": "o-exampleorgid", "Type": "ORGANIZATION"}, {"Id": "<EMAIL>", "Type": "EMAIL"}], "RequestedTimestamp": "2017-01-13T14:35:23.3Z", "Resources": [{"Resources": [{"Type": "MASTER_EMAIL", "Value": "<EMAIL>"}, {"Type": "MASTER_NAME", "Value": "Org Master Account"}, {"Type": "ORGANIZATION_FEATURE_SET", "Value": "FULL"}], "Type": "ORGANIZATION", "Value": "o-exampleorgid"}, {"Type": "EMAIL", "Value": "<EMAIL>"}], "State": "OPEN"}]}, "comments": {"input": {}, "output": {}}, "description": "The following example shows you how to get a list of handshakes that are associated with the account of the credentials used to call the operation:", "id": "to-retrieve-a-list-of-the-handshakes-sent-to-an-account-*************", "title": "To retrieve a list of the handshakes sent to an account"}], "ListHandshakesForOrganization": [{"output": {"Handshakes": [{"Action": "INVITE", "Arn": "arn:aws:organizations::************:handshake/o-exampleorgid/invite/h-examplehandshakeid111", "ExpirationTimestamp": "2017-01-28T14:35:23.3Z", "Id": "h-examplehandshakeid111", "Parties": [{"Id": "o-exampleorgid", "Type": "ORGANIZATION"}, {"Id": "<EMAIL>", "Type": "EMAIL"}], "RequestedTimestamp": "2017-01-13T14:35:23.3Z", "Resources": [{"Resources": [{"Type": "MASTER_EMAIL", "Value": "<EMAIL>"}, {"Type": "MASTER_NAME", "Value": "Org Master Account"}, {"Type": "ORGANIZATION_FEATURE_SET", "Value": "FULL"}], "Type": "ORGANIZATION", "Value": "o-exampleorgid"}, {"Type": "EMAIL", "Value": "<EMAIL>"}], "State": "OPEN"}, {"Action": "INVITE", "Arn": "arn:aws:organizations::************:handshake/o-exampleorgid/invite/h-examplehandshakeid111", "ExpirationTimestamp": "2017-01-28T14:35:23.3Z", "Id": "h-examplehandshakeid222", "Parties": [{"Id": "o-exampleorgid", "Type": "ORGANIZATION"}, {"Id": "<EMAIL>", "Type": "EMAIL"}], "RequestedTimestamp": "2017-01-13T14:35:23.3Z", "Resources": [{"Resources": [{"Type": "MASTER_EMAIL", "Value": "<EMAIL>"}, {"Type": "MASTER_NAME", "Value": "Master Account"}], "Type": "ORGANIZATION", "Value": "o-exampleorgid"}, {"Type": "EMAIL", "Value": "<EMAIL>"}, {"Type": "NOTES", "Value": "This is an invitation to <PERSON><PERSON>'s account to join <PERSON>'s organization."}], "State": "ACCEPTED"}]}, "comments": {"input": {}, "output": {}}, "description": "The following example shows you how to get a list of handshakes associated with the current organization:", "id": "to-retrieve-a-list-of-the-handshakes-associated-with-an-organization-*************", "title": "To retrieve a list of the handshakes associated with an organization"}], "ListOrganizationalUnitsForParent": [{"input": {"ParentId": "r-examplerootid111"}, "output": {"OrganizationalUnits": [{"Arn": "arn:aws:organizations::************:ou/o-exampleorgid/ou-examlerootid111-exampleouid111", "Id": "ou-examplerootid111-exampleouid111", "Name": "Development"}, {"Arn": "arn:aws:organizations::************:ou/o-exampleorgid/ou-examlerootid111-exampleouid222", "Id": "ou-examplerootid111-exampleouid222", "Name": "Production"}]}, "comments": {"input": {}, "output": {}}, "description": "The following example shows how to get a list of OUs in a specified root:/n/n", "id": "to-retrieve-a-list-of-all-of-the-OUs-in-a-parent-container", "title": "To retrieve a list of all of the child OUs in a parent root or OU"}], "ListParents": [{"input": {"ChildId": "************"}, "output": {"Parents": [{"Id": "ou-examplerootid111-exampleouid111", "Type": "ORGANIZATIONAL_UNIT"}]}, "comments": {"input": {}, "output": {}}, "description": "The following example shows how to list the root or OUs that contain account ************:/n/n", "id": "to-retrieve-a-list-of-all-of-the-parents-of-a-child-ou-or-account", "title": "To retrieve a list of all of the parents of a child OU or account"}], "ListPolicies": [{"input": {"Filter": "SERVICE_CONTROL_POLICY"}, "output": {"Policies": [{"Arn": "arn:aws:organizations::************:policy/o-exampleorgid/service_control_policy/p-examplepolicyid111", "AwsManaged": false, "Description": "Enables account admins to delegate permissions for any S3 actions to users and roles in their accounts.", "Id": "p-examplepolicyid111", "Name": "AllowAllS3Actions", "Type": "SERVICE_CONTROL_POLICY"}, {"Arn": "arn:aws:organizations::************:policy/o-exampleorgid/service_control_policy/p-examplepolicyid222", "AwsManaged": false, "Description": "Enables account admins to delegate permissions for any EC2 actions to users and roles in their accounts.", "Id": "p-examplepolicyid222", "Name": "AllowAllEC2Actions", "Type": "SERVICE_CONTROL_POLICY"}, {"Arn": "arn:aws:organizations::aws:policy/service_control_policy/p-FullAWSAccess", "AwsManaged": true, "Description": "Allows access to every operation", "Id": "p-FullAWSAccess", "Name": "FullAWSAccess", "Type": "SERVICE_CONTROL_POLICY"}]}, "comments": {"input": {}, "output": {}}, "description": "The following example shows how to get a list of service control policies (SCPs):/n/n", "id": "to-retrieve-a-list-of--policies-in-the-organization", "title": "To retrieve a list policies in the organization"}], "ListPoliciesForTarget": [{"input": {"Filter": "SERVICE_CONTROL_POLICY", "TargetId": "************"}, "output": {"Policies": [{"Arn": "arn:aws:organizations::************:policy/o-exampleorgid/service_control_policy/p-examplepolicyid222", "AwsManaged": false, "Description": "Enables account admins to delegate permissions for any EC2 actions to users and roles in their accounts.", "Id": "p-examplepolicyid222", "Name": "AllowAllEC2Actions", "Type": "SERVICE_CONTROL_POLICY"}]}, "comments": {"input": {}, "output": {}}, "description": "The following example shows how to get a list of all service control policies (SCPs) of the type specified by the Filter parameter, that are directly attached to an account. The returned list does not include policies that apply to the account because of inheritance from its location in an OU hierarchy:/n/n", "id": "to-retrieve-a-list-of-policies-attached-to-a-root-ou-or-account", "title": "To retrieve a list policies attached to a root, OU, or account"}], "ListRoots": [{"input": {}, "output": {"Roots": [{"Arn": "arn:aws:organizations::************:root/o-exampleorgid/r-examplerootid111", "Id": "r-examplerootid111", "Name": "Root", "PolicyTypes": [{"Status": "ENABLED", "Type": "SERVICE_CONTROL_POLICY"}]}]}, "comments": {"input": {}, "output": {}}, "description": "The following example shows how to get the list of the roots in the current organization:/n/n", "id": "to-retrieve-a-list-of-roots-in-the-organization", "title": "To retrieve a list of roots in the organization"}], "ListTargetsForPolicy": [{"input": {"PolicyId": "p-FullAWSAccess"}, "output": {"Targets": [{"Arn": "arn:aws:organizations::************:root/o-exampleorgid/r-examplerootid111", "Name": "Root", "TargetId": "r-examplerootid111", "Type": "ROOT"}, {"Arn": "arn:aws:organizations::************:account/o-exampleorgid/************;", "Name": "Developer Test Account", "TargetId": "************", "Type": "ACCOUNT"}, {"Arn": "arn:aws:organizations::************:ou/o-exampleorgid/ou-examplerootid111-exampleouid111", "Name": "Accounting", "TargetId": "ou-examplerootid111-exampleouid111", "Type": "ORGANIZATIONAL_UNIT"}]}, "comments": {"input": {}, "output": {}}, "description": "The following example shows how to get the list of roots, OUs, and accounts to which the specified policy is attached:/n/n", "id": "to-retrieve-a-list-of-roots-ous-and-accounts-to-which-a-policy-is-attached", "title": "To retrieve a list of roots, OUs, and accounts to which a policy is attached"}], "MoveAccount": [{"input": {"AccountId": "************", "DestinationParentId": "ou-examplerootid111-exampleouid111", "SourceParentId": "r-examplerootid111"}, "comments": {"input": {}, "output": {}}, "description": "The following example shows how to move a member account from the root to an OU:/n/n", "id": "to-move-an-ou-or-account-to-another-ou-or-the-root", "title": "To move an OU or account to another OU or the root"}], "RemoveAccountFromOrganization": [{"input": {"AccountId": "************"}, "comments": {"input": {}, "output": {}}, "description": "The following example shows you how to remove an account from an organization:", "id": "to-remove-an-account-from-an-organization-as-the-master-account", "title": "To remove an account from an organization as the master account"}], "UpdateOrganizationalUnit": [{"input": {"Name": "AccountingOU", "OrganizationalUnitId": "ou-examplerootid111-exampleouid111"}, "output": {"OrganizationalUnit": {"Arn": "arn:aws:organizations::************:ou/o-exampleorgid/ou-examplerootid111-exampleouid111", "Id": "ou-examplerootid111-exampleouid111", "Name": "AccountingOU"}}, "comments": {"input": {}, "output": {}}, "description": "The following example shows how to rename an OU. The output confirms the new name:/n/n", "id": "to-rename-an-organizational-unit", "title": "To rename an organizational unit"}], "UpdatePolicy": [{"input": {"Description": "This description replaces the original.", "Name": "Renamed-Policy", "PolicyId": "p-examplepolicyid111"}, "output": {"Policy": {"Content": "{ \"Version\": \"2012-10-17\", \"Statement\": { \"Effect\": \"Allow\", \"Action\": \"ec2:*\", \"Resource\": \"*\" } }", "PolicySummary": {"Arn": "arn:aws:organizations::************:policy/o-exampleorgid/service_control_policy/p-examplepolicyid111", "AwsManaged": false, "Description": "This description replaces the original.", "Id": "p-examplepolicyid111", "Name": "Renamed-Policy", "Type": "SERVICE_CONTROL_POLICY"}}}, "comments": {"input": {}, "output": {}}, "description": "The following example shows how to rename a policy and give it a new description and new content. The output confirms the new name and description text:/n/n", "id": "to-update-the-details-of-a-policy", "title": "To update the details of a policy"}, {"input": {"Content": "{ \\\"Version\\\": \\\"2012-10-17\\\", \\\"Statement\\\": {\\\"Effect\\\": \\\"Allow\\\", \\\"Action\\\": \\\"s3:*\\\", \\\"Resource\\\": \\\"*\\\" } }", "PolicyId": "p-examplepolicyid111"}, "output": {"Policy": {"Content": "{ \\\"Version\\\": \\\"2012-10-17\\\", \\\"Statement\\\": { \\\"Effect\\\": \\\"Allow\\\", \\\"Action\\\": \\\"s3:*\\\", \\\"Resource\\\": \\\"*\\\" } }", "PolicySummary": {"Arn": "arn:aws:organizations::************:policy/o-exampleorgid/service_control_policy/p-examplepolicyid111", "AwsManaged": false, "Description": "This description replaces the original.", "Id": "p-examplepolicyid111", "Name": "Renamed-Policy", "Type": "SERVICE_CONTROL_POLICY"}}}, "comments": {"input": {}, "output": {}}, "description": "The following example shows how to replace the JSON text of the SCP from the preceding example with a new JSON policy text string that allows S3 actions instead of EC2 actions:/n/n", "id": "to-update-the-content-of-a-policy", "title": "To update the content of a policy"}]}}