{"version": "2.0", "metadata": {"apiVersion": "2018-08-01", "endpointPrefix": "license-manager", "jsonVersion": "1.1", "protocol": "json", "serviceFullName": "AWS License Manager", "serviceId": "License Manager", "signatureVersion": "v4", "targetPrefix": "AWSLicenseManager", "uid": "license-manager-2018-08-01"}, "operations": {"AcceptGrant": {"input": {"type": "structure", "required": ["<PERSON><PERSON><PERSON>"], "members": {"GrantArn": {}}}, "output": {"type": "structure", "members": {"GrantArn": {}, "Status": {}, "Version": {}}}}, "CheckInLicense": {"input": {"type": "structure", "required": ["LicenseConsumptionToken"], "members": {"LicenseConsumptionToken": {}, "Beneficiary": {}}}, "output": {"type": "structure", "members": {}}}, "CheckoutBorrowLicense": {"input": {"type": "structure", "required": ["LicenseArn", "Entitlements", "DigitalSignatureMethod", "ClientToken"], "members": {"LicenseArn": {}, "Entitlements": {"shape": "S9"}, "DigitalSignatureMethod": {}, "NodeId": {}, "CheckoutMetadata": {"shape": "Sd"}, "ClientToken": {}}}, "output": {"type": "structure", "members": {"LicenseArn": {}, "LicenseConsumptionToken": {}, "EntitlementsAllowed": {"shape": "S9"}, "NodeId": {}, "SignedToken": {}, "IssuedAt": {}, "Expiration": {}, "CheckoutMetadata": {"shape": "Sd"}}}}, "CheckoutLicense": {"input": {"type": "structure", "required": ["ProductSKU", "CheckoutType", "KeyFingerprint", "Entitlements", "ClientToken"], "members": {"ProductSKU": {}, "CheckoutType": {}, "KeyFingerprint": {}, "Entitlements": {"shape": "S9"}, "ClientToken": {}, "Beneficiary": {}, "NodeId": {}}}, "output": {"type": "structure", "members": {"CheckoutType": {}, "LicenseConsumptionToken": {}, "EntitlementsAllowed": {"shape": "S9"}, "SignedToken": {}, "NodeId": {}, "IssuedAt": {}, "Expiration": {}, "LicenseArn": {}}}}, "CreateGrant": {"input": {"type": "structure", "required": ["ClientToken", "<PERSON><PERSON><PERSON>", "LicenseArn", "Principals", "HomeRegion", "AllowedOperations"], "members": {"ClientToken": {}, "GrantName": {}, "LicenseArn": {}, "Principals": {"type": "list", "member": {}}, "HomeRegion": {}, "AllowedOperations": {"shape": "So"}}}, "output": {"type": "structure", "members": {"GrantArn": {}, "Status": {}, "Version": {}}}}, "CreateGrantVersion": {"input": {"type": "structure", "required": ["ClientToken", "<PERSON><PERSON><PERSON>"], "members": {"ClientToken": {}, "GrantArn": {}, "GrantName": {}, "AllowedOperations": {"shape": "So"}, "Status": {}, "StatusReason": {}, "SourceVersion": {}}}, "output": {"type": "structure", "members": {"GrantArn": {}, "Status": {}, "Version": {}}}}, "CreateLicense": {"input": {"type": "structure", "required": ["LicenseName", "ProductName", "ProductSKU", "Issuer", "HomeRegion", "Validity", "Entitlements", "Beneficiary", "ConsumptionConfiguration", "ClientToken"], "members": {"LicenseName": {}, "ProductName": {}, "ProductSKU": {}, "Issuer": {"shape": "Sv"}, "HomeRegion": {}, "Validity": {"shape": "Sw"}, "Entitlements": {"shape": "Sx"}, "Beneficiary": {}, "ConsumptionConfiguration": {"shape": "S12"}, "LicenseMetadata": {"shape": "Sd"}, "ClientToken": {}}}, "output": {"type": "structure", "members": {"LicenseArn": {}, "Status": {}, "Version": {}}}}, "CreateLicenseConfiguration": {"input": {"type": "structure", "required": ["Name", "LicenseCountingType"], "members": {"Name": {}, "Description": {}, "LicenseCountingType": {}, "LicenseCount": {"type": "long"}, "LicenseCountHardLimit": {"type": "boolean"}, "LicenseRules": {"shape": "S1c"}, "Tags": {"shape": "S1d"}, "DisassociateWhenNotFound": {"type": "boolean"}, "ProductInformationList": {"shape": "S1f"}}}, "output": {"type": "structure", "members": {"LicenseConfigurationArn": {}}}}, "CreateLicenseConversionTaskForResource": {"input": {"type": "structure", "required": ["ResourceArn", "SourceLicenseContext", "DestinationLicenseContext"], "members": {"ResourceArn": {}, "SourceLicenseContext": {"shape": "S1l"}, "DestinationLicenseContext": {"shape": "S1l"}}}, "output": {"type": "structure", "members": {"LicenseConversionTaskId": {}}}}, "CreateLicenseManagerReportGenerator": {"input": {"type": "structure", "required": ["ReportGeneratorName", "Type", "ReportContext", "ReportFrequency", "ClientToken"], "members": {"ReportGeneratorName": {}, "Type": {"shape": "S1r"}, "ReportContext": {"shape": "S1t"}, "ReportFrequency": {"shape": "S1v"}, "ClientToken": {}, "Description": {}, "Tags": {"shape": "S1d"}}}, "output": {"type": "structure", "members": {"LicenseManagerReportGeneratorArn": {}}}}, "CreateLicenseVersion": {"input": {"type": "structure", "required": ["LicenseArn", "LicenseName", "ProductName", "Issuer", "HomeRegion", "Validity", "Entitlements", "ConsumptionConfiguration", "Status", "ClientToken"], "members": {"LicenseArn": {}, "LicenseName": {}, "ProductName": {}, "Issuer": {"shape": "Sv"}, "HomeRegion": {}, "Validity": {"shape": "Sw"}, "LicenseMetadata": {"shape": "Sd"}, "Entitlements": {"shape": "Sx"}, "ConsumptionConfiguration": {"shape": "S12"}, "Status": {}, "ClientToken": {}, "SourceVersion": {}}}, "output": {"type": "structure", "members": {"LicenseArn": {}, "Version": {}, "Status": {}}}}, "CreateToken": {"input": {"type": "structure", "required": ["LicenseArn", "ClientToken"], "members": {"LicenseArn": {}, "RoleArns": {"shape": "S1u"}, "ExpirationInDays": {"type": "integer"}, "TokenProperties": {"shape": "S23"}, "ClientToken": {}}}, "output": {"type": "structure", "members": {"TokenId": {}, "TokenType": {}, "Token": {}}}}, "DeleteGrant": {"input": {"type": "structure", "required": ["<PERSON><PERSON><PERSON>", "Version"], "members": {"GrantArn": {}, "StatusReason": {}, "Version": {}}}, "output": {"type": "structure", "members": {"GrantArn": {}, "Status": {}, "Version": {}}}}, "DeleteLicense": {"input": {"type": "structure", "required": ["LicenseArn", "SourceVersion"], "members": {"LicenseArn": {}, "SourceVersion": {}}}, "output": {"type": "structure", "members": {"Status": {}, "DeletionDate": {}}}}, "DeleteLicenseConfiguration": {"input": {"type": "structure", "required": ["LicenseConfigurationArn"], "members": {"LicenseConfigurationArn": {}}}, "output": {"type": "structure", "members": {}}}, "DeleteLicenseManagerReportGenerator": {"input": {"type": "structure", "required": ["LicenseManagerReportGeneratorArn"], "members": {"LicenseManagerReportGeneratorArn": {}}}, "output": {"type": "structure", "members": {}}}, "DeleteToken": {"input": {"type": "structure", "required": ["TokenId"], "members": {"TokenId": {}}}, "output": {"type": "structure", "members": {}}}, "ExtendLicenseConsumption": {"input": {"type": "structure", "required": ["LicenseConsumptionToken"], "members": {"LicenseConsumptionToken": {}, "DryRun": {"type": "boolean"}}}, "output": {"type": "structure", "members": {"LicenseConsumptionToken": {}, "Expiration": {}}}}, "GetAccessToken": {"input": {"type": "structure", "required": ["Token"], "members": {"Token": {}, "TokenProperties": {"shape": "S23"}}}, "output": {"type": "structure", "members": {"AccessToken": {}}}}, "GetGrant": {"input": {"type": "structure", "required": ["<PERSON><PERSON><PERSON>"], "members": {"GrantArn": {}, "Version": {}}}, "output": {"type": "structure", "members": {"Grant": {"shape": "S2p"}}}}, "GetLicense": {"input": {"type": "structure", "required": ["LicenseArn"], "members": {"LicenseArn": {}, "Version": {}}}, "output": {"type": "structure", "members": {"License": {"shape": "S2s"}}}}, "GetLicenseConfiguration": {"input": {"type": "structure", "required": ["LicenseConfigurationArn"], "members": {"LicenseConfigurationArn": {}}}, "output": {"type": "structure", "members": {"LicenseConfigurationId": {}, "LicenseConfigurationArn": {}, "Name": {}, "Description": {}, "LicenseCountingType": {}, "LicenseRules": {"shape": "S1c"}, "LicenseCount": {"type": "long"}, "LicenseCountHardLimit": {"type": "boolean"}, "ConsumedLicenses": {"type": "long"}, "Status": {}, "OwnerAccountId": {}, "ConsumedLicenseSummaryList": {"shape": "S2w"}, "ManagedResourceSummaryList": {"shape": "S2z"}, "Tags": {"shape": "S1d"}, "ProductInformationList": {"shape": "S1f"}, "AutomatedDiscoveryInformation": {"shape": "S31"}, "DisassociateWhenNotFound": {"type": "boolean"}}}}, "GetLicenseConversionTask": {"input": {"type": "structure", "required": ["LicenseConversionTaskId"], "members": {"LicenseConversionTaskId": {}}}, "output": {"type": "structure", "members": {"LicenseConversionTaskId": {}, "ResourceArn": {}, "SourceLicenseContext": {"shape": "S1l"}, "DestinationLicenseContext": {"shape": "S1l"}, "StatusMessage": {}, "Status": {}, "StartTime": {"type": "timestamp"}, "LicenseConversionTime": {"type": "timestamp"}, "EndTime": {"type": "timestamp"}}}}, "GetLicenseManagerReportGenerator": {"input": {"type": "structure", "required": ["LicenseManagerReportGeneratorArn"], "members": {"LicenseManagerReportGeneratorArn": {}}}, "output": {"type": "structure", "members": {"ReportGenerator": {"shape": "S38"}}}}, "GetLicenseUsage": {"input": {"type": "structure", "required": ["LicenseArn"], "members": {"LicenseArn": {}}}, "output": {"type": "structure", "members": {"LicenseUsage": {"type": "structure", "members": {"EntitlementUsages": {"type": "list", "member": {"type": "structure", "required": ["Name", "ConsumedValue", "Unit"], "members": {"Name": {}, "ConsumedValue": {}, "MaxCount": {}, "Unit": {}}}}}}}}}, "GetServiceSettings": {"input": {"type": "structure", "members": {}}, "output": {"type": "structure", "members": {"S3BucketArn": {}, "SnsTopicArn": {}, "OrganizationConfiguration": {"shape": "S3h"}, "EnableCrossAccountsDiscovery": {"type": "boolean"}, "LicenseManagerResourceShareArn": {}}}}, "ListAssociationsForLicenseConfiguration": {"input": {"type": "structure", "required": ["LicenseConfigurationArn"], "members": {"LicenseConfigurationArn": {}, "MaxResults": {"type": "integer"}, "NextToken": {}}}, "output": {"type": "structure", "members": {"LicenseConfigurationAssociations": {"type": "list", "member": {"type": "structure", "members": {"ResourceArn": {}, "ResourceType": {}, "ResourceOwnerId": {}, "AssociationTime": {"type": "timestamp"}, "AmiAssociationScope": {}}}}, "NextToken": {}}}}, "ListDistributedGrants": {"input": {"type": "structure", "members": {"GrantArns": {"shape": "S1u"}, "Filters": {"shape": "S3n"}, "NextToken": {}, "MaxResults": {"type": "integer"}}}, "output": {"type": "structure", "members": {"Grants": {"shape": "S3u"}, "NextToken": {}}}}, "ListFailuresForLicenseConfigurationOperations": {"input": {"type": "structure", "required": ["LicenseConfigurationArn"], "members": {"LicenseConfigurationArn": {}, "MaxResults": {"type": "integer"}, "NextToken": {}}}, "output": {"type": "structure", "members": {"LicenseOperationFailureList": {"type": "list", "member": {"type": "structure", "members": {"ResourceArn": {}, "ResourceType": {}, "ErrorMessage": {}, "FailureTime": {"type": "timestamp"}, "OperationName": {}, "ResourceOwnerId": {}, "OperationRequestedBy": {}, "MetadataList": {"shape": "Sd"}}}}, "NextToken": {}}}}, "ListLicenseConfigurations": {"input": {"type": "structure", "members": {"LicenseConfigurationArns": {"shape": "S1c"}, "MaxResults": {"type": "integer"}, "NextToken": {}, "Filters": {"shape": "S40"}}}, "output": {"type": "structure", "members": {"LicenseConfigurations": {"type": "list", "member": {"type": "structure", "members": {"LicenseConfigurationId": {}, "LicenseConfigurationArn": {}, "Name": {}, "Description": {}, "LicenseCountingType": {}, "LicenseRules": {"shape": "S1c"}, "LicenseCount": {"type": "long"}, "LicenseCountHardLimit": {"type": "boolean"}, "DisassociateWhenNotFound": {"type": "boolean"}, "ConsumedLicenses": {"type": "long"}, "Status": {}, "OwnerAccountId": {}, "ConsumedLicenseSummaryList": {"shape": "S2w"}, "ManagedResourceSummaryList": {"shape": "S2z"}, "ProductInformationList": {"shape": "S1f"}, "AutomatedDiscoveryInformation": {"shape": "S31"}}}}, "NextToken": {}}}}, "ListLicenseConversionTasks": {"input": {"type": "structure", "members": {"NextToken": {}, "MaxResults": {"type": "integer"}, "Filters": {"shape": "S40"}}}, "output": {"type": "structure", "members": {"LicenseConversionTasks": {"type": "list", "member": {"type": "structure", "members": {"LicenseConversionTaskId": {}, "ResourceArn": {}, "SourceLicenseContext": {"shape": "S1l"}, "DestinationLicenseContext": {"shape": "S1l"}, "Status": {}, "StatusMessage": {}, "StartTime": {"type": "timestamp"}, "LicenseConversionTime": {"type": "timestamp"}, "EndTime": {"type": "timestamp"}}}}, "NextToken": {}}}}, "ListLicenseManagerReportGenerators": {"input": {"type": "structure", "members": {"Filters": {"shape": "S3n"}, "NextToken": {}, "MaxResults": {"type": "integer"}}}, "output": {"type": "structure", "members": {"ReportGenerators": {"type": "list", "member": {"shape": "S38"}}, "NextToken": {}}}}, "ListLicenseSpecificationsForResource": {"input": {"type": "structure", "required": ["ResourceArn"], "members": {"ResourceArn": {}, "MaxResults": {"type": "integer"}, "NextToken": {}}}, "output": {"type": "structure", "members": {"LicenseSpecifications": {"shape": "S4d"}, "NextToken": {}}}}, "ListLicenseVersions": {"input": {"type": "structure", "required": ["LicenseArn"], "members": {"LicenseArn": {}, "NextToken": {}, "MaxResults": {"type": "integer"}}}, "output": {"type": "structure", "members": {"Licenses": {"shape": "S4h"}, "NextToken": {}}}}, "ListLicenses": {"input": {"type": "structure", "members": {"LicenseArns": {"shape": "S1u"}, "Filters": {"shape": "S3n"}, "NextToken": {}, "MaxResults": {"type": "integer"}}}, "output": {"type": "structure", "members": {"Licenses": {"shape": "S4h"}, "NextToken": {}}}}, "ListReceivedGrants": {"input": {"type": "structure", "members": {"GrantArns": {"shape": "S1u"}, "Filters": {"shape": "S3n"}, "NextToken": {}, "MaxResults": {"type": "integer"}}}, "output": {"type": "structure", "members": {"Grants": {"shape": "S3u"}, "NextToken": {}}}}, "ListReceivedGrantsForOrganization": {"input": {"type": "structure", "required": ["LicenseArn"], "members": {"LicenseArn": {}, "Filters": {"shape": "S3n"}, "NextToken": {}, "MaxResults": {"type": "integer"}}}, "output": {"type": "structure", "members": {"Grants": {"shape": "S3u"}, "NextToken": {}}}}, "ListReceivedLicenses": {"input": {"type": "structure", "members": {"LicenseArns": {"shape": "S1u"}, "Filters": {"shape": "S3n"}, "NextToken": {}, "MaxResults": {"type": "integer"}}}, "output": {"type": "structure", "members": {"Licenses": {"shape": "S4q"}, "NextToken": {}}}}, "ListReceivedLicensesForOrganization": {"input": {"type": "structure", "members": {"Filters": {"shape": "S3n"}, "NextToken": {}, "MaxResults": {"type": "integer"}}}, "output": {"type": "structure", "members": {"Licenses": {"shape": "S4q"}, "NextToken": {}}}}, "ListResourceInventory": {"input": {"type": "structure", "members": {"MaxResults": {"type": "integer"}, "NextToken": {}, "Filters": {"type": "list", "member": {"type": "structure", "required": ["Name", "Condition"], "members": {"Name": {}, "Condition": {}, "Value": {}}}}}}, "output": {"type": "structure", "members": {"ResourceInventoryList": {"type": "list", "member": {"type": "structure", "members": {"ResourceId": {}, "ResourceType": {}, "ResourceArn": {}, "Platform": {}, "PlatformVersion": {}, "ResourceOwningAccountId": {}}}}, "NextToken": {}}}}, "ListTagsForResource": {"input": {"type": "structure", "required": ["ResourceArn"], "members": {"ResourceArn": {}}}, "output": {"type": "structure", "members": {"Tags": {"shape": "S1d"}}}}, "ListTokens": {"input": {"type": "structure", "members": {"TokenIds": {"shape": "S1c"}, "Filters": {"shape": "S3n"}, "NextToken": {}, "MaxResults": {"type": "integer"}}}, "output": {"type": "structure", "members": {"Tokens": {"type": "list", "member": {"type": "structure", "members": {"TokenId": {}, "TokenType": {}, "LicenseArn": {}, "ExpirationTime": {}, "TokenProperties": {"shape": "S23"}, "RoleArns": {"shape": "S1u"}, "Status": {}}}}, "NextToken": {}}}}, "ListUsageForLicenseConfiguration": {"input": {"type": "structure", "required": ["LicenseConfigurationArn"], "members": {"LicenseConfigurationArn": {}, "MaxResults": {"type": "integer"}, "NextToken": {}, "Filters": {"shape": "S40"}}}, "output": {"type": "structure", "members": {"LicenseConfigurationUsageList": {"type": "list", "member": {"type": "structure", "members": {"ResourceArn": {}, "ResourceType": {}, "ResourceStatus": {}, "ResourceOwnerId": {}, "AssociationTime": {"type": "timestamp"}, "ConsumedLicenses": {"type": "long"}}}}, "NextToken": {}}}}, "RejectGrant": {"input": {"type": "structure", "required": ["<PERSON><PERSON><PERSON>"], "members": {"GrantArn": {}}}, "output": {"type": "structure", "members": {"GrantArn": {}, "Status": {}, "Version": {}}}}, "TagResource": {"input": {"type": "structure", "required": ["ResourceArn", "Tags"], "members": {"ResourceArn": {}, "Tags": {"shape": "S1d"}}}, "output": {"type": "structure", "members": {}}}, "UntagResource": {"input": {"type": "structure", "required": ["ResourceArn", "TagKeys"], "members": {"ResourceArn": {}, "TagKeys": {"type": "list", "member": {}}}}, "output": {"type": "structure", "members": {}}}, "UpdateLicenseConfiguration": {"input": {"type": "structure", "required": ["LicenseConfigurationArn"], "members": {"LicenseConfigurationArn": {}, "LicenseConfigurationStatus": {}, "LicenseRules": {"shape": "S1c"}, "LicenseCount": {"type": "long"}, "LicenseCountHardLimit": {"type": "boolean"}, "Name": {}, "Description": {}, "ProductInformationList": {"shape": "S1f"}, "DisassociateWhenNotFound": {"type": "boolean"}}}, "output": {"type": "structure", "members": {}}}, "UpdateLicenseManagerReportGenerator": {"input": {"type": "structure", "required": ["LicenseManagerReportGeneratorArn", "ReportGeneratorName", "Type", "ReportContext", "ReportFrequency", "ClientToken"], "members": {"LicenseManagerReportGeneratorArn": {}, "ReportGeneratorName": {}, "Type": {"shape": "S1r"}, "ReportContext": {"shape": "S1t"}, "ReportFrequency": {"shape": "S1v"}, "ClientToken": {}, "Description": {}}}, "output": {"type": "structure", "members": {}}}, "UpdateLicenseSpecificationsForResource": {"input": {"type": "structure", "required": ["ResourceArn"], "members": {"ResourceArn": {}, "AddLicenseSpecifications": {"shape": "S4d"}, "RemoveLicenseSpecifications": {"shape": "S4d"}}}, "output": {"type": "structure", "members": {}}}, "UpdateServiceSettings": {"input": {"type": "structure", "members": {"S3BucketArn": {}, "SnsTopicArn": {}, "OrganizationConfiguration": {"shape": "S3h"}, "EnableCrossAccountsDiscovery": {"type": "boolean"}}}, "output": {"type": "structure", "members": {}}}}, "shapes": {"S9": {"type": "list", "member": {"type": "structure", "required": ["Name", "Unit"], "members": {"Name": {}, "Value": {}, "Unit": {}}}}, "Sd": {"type": "list", "member": {"type": "structure", "members": {"Name": {}, "Value": {}}}}, "So": {"type": "list", "member": {}}, "Sv": {"type": "structure", "required": ["Name"], "members": {"Name": {}, "SignKey": {}}}, "Sw": {"type": "structure", "required": ["<PERSON><PERSON>"], "members": {"Begin": {}, "End": {}}}, "Sx": {"type": "list", "member": {"type": "structure", "required": ["Name", "Unit"], "members": {"Name": {}, "Value": {}, "MaxCount": {"type": "long"}, "Overage": {"type": "boolean"}, "Unit": {}, "AllowCheckIn": {"type": "boolean"}}}}, "S12": {"type": "structure", "members": {"RenewType": {}, "ProvisionalConfiguration": {"type": "structure", "required": ["MaxTimeToLiveInMinutes"], "members": {"MaxTimeToLiveInMinutes": {"type": "integer"}}}, "BorrowConfiguration": {"type": "structure", "required": ["AllowEarlyCheckIn", "MaxTimeToLiveInMinutes"], "members": {"AllowEarlyCheckIn": {"type": "boolean"}, "MaxTimeToLiveInMinutes": {"type": "integer"}}}}}, "S1c": {"type": "list", "member": {}}, "S1d": {"type": "list", "member": {"type": "structure", "members": {"Key": {}, "Value": {}}}}, "S1f": {"type": "list", "member": {"type": "structure", "required": ["ResourceType", "ProductInformationFilterList"], "members": {"ResourceType": {}, "ProductInformationFilterList": {"type": "list", "member": {"type": "structure", "required": ["ProductInformationFilterName", "ProductInformationFilterComparator"], "members": {"ProductInformationFilterName": {}, "ProductInformationFilterValue": {"shape": "S1c"}, "ProductInformationFilterComparator": {}}}}}}}, "S1l": {"type": "structure", "members": {"UsageOperation": {}}}, "S1r": {"type": "list", "member": {}}, "S1t": {"type": "structure", "required": ["licenseConfigurationArns"], "members": {"licenseConfigurationArns": {"shape": "S1u"}}}, "S1u": {"type": "list", "member": {}}, "S1v": {"type": "structure", "members": {"value": {"type": "integer"}, "period": {}}}, "S23": {"type": "list", "member": {}}, "S2p": {"type": "structure", "required": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "ParentArn", "LicenseArn", "GranteePrincipalArn", "HomeRegion", "GrantStatus", "Version", "GrantedOperations"], "members": {"GrantArn": {}, "GrantName": {}, "ParentArn": {}, "LicenseArn": {}, "GranteePrincipalArn": {}, "HomeRegion": {}, "GrantStatus": {}, "StatusReason": {}, "Version": {}, "GrantedOperations": {"shape": "So"}}}, "S2s": {"type": "structure", "members": {"LicenseArn": {}, "LicenseName": {}, "ProductName": {}, "ProductSKU": {}, "Issuer": {"shape": "S2t"}, "HomeRegion": {}, "Status": {}, "Validity": {"shape": "Sw"}, "Beneficiary": {}, "Entitlements": {"shape": "Sx"}, "ConsumptionConfiguration": {"shape": "S12"}, "LicenseMetadata": {"shape": "Sd"}, "CreateTime": {}, "Version": {}}}, "S2t": {"type": "structure", "members": {"Name": {}, "SignKey": {}, "KeyFingerprint": {}}}, "S2w": {"type": "list", "member": {"type": "structure", "members": {"ResourceType": {}, "ConsumedLicenses": {"type": "long"}}}}, "S2z": {"type": "list", "member": {"type": "structure", "members": {"ResourceType": {}, "AssociationCount": {"type": "long"}}}}, "S31": {"type": "structure", "members": {"LastRunTime": {"type": "timestamp"}}}, "S38": {"type": "structure", "members": {"ReportGeneratorName": {}, "ReportType": {"shape": "S1r"}, "ReportContext": {"shape": "S1t"}, "ReportFrequency": {"shape": "S1v"}, "LicenseManagerReportGeneratorArn": {}, "LastRunStatus": {}, "LastRunFailureReason": {}, "LastReportGenerationTime": {}, "ReportCreatorAccount": {}, "Description": {}, "S3Location": {"type": "structure", "members": {"bucket": {}, "keyPrefix": {}}}, "CreateTime": {}, "Tags": {"shape": "S1d"}}}, "S3h": {"type": "structure", "required": ["EnableIntegration"], "members": {"EnableIntegration": {"type": "boolean"}}}, "S3n": {"type": "list", "member": {"shape": "S3o"}}, "S3o": {"type": "structure", "members": {"Name": {}, "Values": {"type": "list", "member": {}}}}, "S3u": {"type": "list", "member": {"shape": "S2p"}}, "S40": {"type": "list", "member": {"shape": "S3o"}}, "S4d": {"type": "list", "member": {"type": "structure", "required": ["LicenseConfigurationArn"], "members": {"LicenseConfigurationArn": {}, "AmiAssociationScope": {}}}}, "S4h": {"type": "list", "member": {"shape": "S2s"}}, "S4q": {"type": "list", "member": {"type": "structure", "members": {"LicenseArn": {}, "LicenseName": {}, "ProductName": {}, "ProductSKU": {}, "Issuer": {"shape": "S2t"}, "HomeRegion": {}, "Status": {}, "Validity": {"shape": "Sw"}, "Beneficiary": {}, "Entitlements": {"shape": "Sx"}, "ConsumptionConfiguration": {"shape": "S12"}, "LicenseMetadata": {"shape": "Sd"}, "CreateTime": {}, "Version": {}, "ReceivedMetadata": {"type": "structure", "members": {"ReceivedStatus": {}, "ReceivedStatusReason": {}, "AllowedOperations": {"shape": "So"}}}}}}}}