{"version": "2.0", "metadata": {"apiVersion": "2016-06-27", "endpointPrefix": "rekognition", "jsonVersion": "1.1", "protocol": "json", "serviceFullName": "Amazon Rekognition", "serviceId": "Rekognition", "signatureVersion": "v4", "targetPrefix": "RekognitionService", "uid": "rekognition-2016-06-27"}, "operations": {"CompareFaces": {"input": {"type": "structure", "required": ["SourceImage", "TargetImage"], "members": {"SourceImage": {"shape": "S2"}, "TargetImage": {"shape": "S2"}, "SimilarityThreshold": {"type": "float"}, "QualityFilter": {}}}, "output": {"type": "structure", "members": {"SourceImageFace": {"type": "structure", "members": {"BoundingBox": {"shape": "Sc"}, "Confidence": {"type": "float"}}}, "FaceMatches": {"type": "list", "member": {"type": "structure", "members": {"Similarity": {"type": "float"}, "Face": {"shape": "Sg"}}}}, "UnmatchedFaces": {"type": "list", "member": {"shape": "Sg"}}, "SourceImageOrientationCorrection": {}, "TargetImageOrientationCorrection": {}}}}, "CopyProjectVersion": {"input": {"type": "structure", "required": ["SourceProjectArn", "SourceProjectVersionArn", "DestinationProjectArn", "VersionName", "OutputConfig"], "members": {"SourceProjectArn": {}, "SourceProjectVersionArn": {}, "DestinationProjectArn": {}, "VersionName": {}, "OutputConfig": {"shape": "Sy"}, "Tags": {"shape": "S10"}, "KmsKeyId": {}}}, "output": {"type": "structure", "members": {"ProjectVersionArn": {}}}}, "CreateCollection": {"input": {"type": "structure", "required": ["CollectionId"], "members": {"CollectionId": {}, "Tags": {"shape": "S10"}}}, "output": {"type": "structure", "members": {"StatusCode": {"type": "integer"}, "CollectionArn": {}, "FaceModelVersion": {}}}}, "CreateDataset": {"input": {"type": "structure", "required": ["DatasetType", "ProjectArn"], "members": {"DatasetSource": {"type": "structure", "members": {"GroundTruthManifest": {"shape": "S1c"}, "DatasetArn": {}}}, "DatasetType": {}, "ProjectArn": {}}}, "output": {"type": "structure", "members": {"DatasetArn": {}}}}, "CreateProject": {"input": {"type": "structure", "required": ["ProjectName"], "members": {"ProjectName": {}}}, "output": {"type": "structure", "members": {"ProjectArn": {}}}}, "CreateProjectVersion": {"input": {"type": "structure", "required": ["ProjectArn", "VersionName", "OutputConfig"], "members": {"ProjectArn": {}, "VersionName": {}, "OutputConfig": {"shape": "Sy"}, "TrainingData": {"shape": "S1k"}, "TestingData": {"shape": "S1n"}, "Tags": {"shape": "S10"}, "KmsKeyId": {}}}, "output": {"type": "structure", "members": {"ProjectVersionArn": {}}}}, "CreateStreamProcessor": {"input": {"type": "structure", "required": ["Input", "Output", "Name", "Settings", "RoleArn"], "members": {"Input": {"shape": "S1q"}, "Output": {"shape": "S1t"}, "Name": {}, "Settings": {"shape": "S1y"}, "RoleArn": {}, "Tags": {"shape": "S10"}, "NotificationChannel": {"shape": "S24"}, "KmsKeyId": {}, "RegionsOfInterest": {"shape": "S26"}, "DataSharingPreference": {"shape": "S2a"}}}, "output": {"type": "structure", "members": {"StreamProcessorArn": {}}}}, "DeleteCollection": {"input": {"type": "structure", "required": ["CollectionId"], "members": {"CollectionId": {}}}, "output": {"type": "structure", "members": {"StatusCode": {"type": "integer"}}}}, "DeleteDataset": {"input": {"type": "structure", "required": ["DatasetArn"], "members": {"DatasetArn": {}}}, "output": {"type": "structure", "members": {}}}, "DeleteFaces": {"input": {"type": "structure", "required": ["CollectionId", "FaceIds"], "members": {"CollectionId": {}, "FaceIds": {"shape": "S2i"}}}, "output": {"type": "structure", "members": {"DeletedFaces": {"shape": "S2i"}}}}, "DeleteProject": {"input": {"type": "structure", "required": ["ProjectArn"], "members": {"ProjectArn": {}}}, "output": {"type": "structure", "members": {"Status": {}}}}, "DeleteProjectPolicy": {"input": {"type": "structure", "required": ["ProjectArn", "PolicyName"], "members": {"ProjectArn": {}, "PolicyName": {}, "PolicyRevisionId": {}}}, "output": {"type": "structure", "members": {}}}, "DeleteProjectVersion": {"input": {"type": "structure", "required": ["ProjectVersionArn"], "members": {"ProjectVersionArn": {}}}, "output": {"type": "structure", "members": {"Status": {}}}}, "DeleteStreamProcessor": {"input": {"type": "structure", "required": ["Name"], "members": {"Name": {}}}, "output": {"type": "structure", "members": {}}}, "DescribeCollection": {"input": {"type": "structure", "required": ["CollectionId"], "members": {"CollectionId": {}}}, "output": {"type": "structure", "members": {"FaceCount": {"type": "long"}, "FaceModelVersion": {}, "CollectionARN": {}, "CreationTimestamp": {"type": "timestamp"}}}}, "DescribeDataset": {"input": {"type": "structure", "required": ["DatasetArn"], "members": {"DatasetArn": {}}}, "output": {"type": "structure", "members": {"DatasetDescription": {"type": "structure", "members": {"CreationTimestamp": {"type": "timestamp"}, "LastUpdatedTimestamp": {"type": "timestamp"}, "Status": {}, "StatusMessage": {}, "StatusMessageCode": {}, "DatasetStats": {"type": "structure", "members": {"LabeledEntries": {"type": "integer"}, "TotalEntries": {"type": "integer"}, "TotalLabels": {"type": "integer"}, "ErrorEntries": {"type": "integer"}}}}}}}}, "DescribeProjectVersions": {"input": {"type": "structure", "required": ["ProjectArn"], "members": {"ProjectArn": {}, "VersionNames": {"type": "list", "member": {}}, "NextToken": {}, "MaxResults": {"type": "integer"}}}, "output": {"type": "structure", "members": {"ProjectVersionDescriptions": {"type": "list", "member": {"type": "structure", "members": {"ProjectVersionArn": {}, "CreationTimestamp": {"type": "timestamp"}, "MinInferenceUnits": {"type": "integer"}, "Status": {}, "StatusMessage": {}, "BillableTrainingTimeInSeconds": {"type": "long"}, "TrainingEndTimestamp": {"type": "timestamp"}, "OutputConfig": {"shape": "Sy"}, "TrainingDataResult": {"type": "structure", "members": {"Input": {"shape": "S1k"}, "Output": {"shape": "S1k"}, "Validation": {"shape": "S3h"}}}, "TestingDataResult": {"type": "structure", "members": {"Input": {"shape": "S1n"}, "Output": {"shape": "S1n"}, "Validation": {"shape": "S3h"}}}, "EvaluationResult": {"type": "structure", "members": {"F1Score": {"type": "float"}, "Summary": {"type": "structure", "members": {"S3Object": {"shape": "S4"}}}}}, "ManifestSummary": {"shape": "S1c"}, "KmsKeyId": {}, "MaxInferenceUnits": {"type": "integer"}, "SourceProjectVersionArn": {}}}}, "NextToken": {}}}}, "DescribeProjects": {"input": {"type": "structure", "members": {"NextToken": {}, "MaxResults": {"type": "integer"}, "ProjectNames": {"type": "list", "member": {}}}}, "output": {"type": "structure", "members": {"ProjectDescriptions": {"type": "list", "member": {"type": "structure", "members": {"ProjectArn": {}, "CreationTimestamp": {"type": "timestamp"}, "Status": {}, "Datasets": {"type": "list", "member": {"type": "structure", "members": {"CreationTimestamp": {"type": "timestamp"}, "DatasetType": {}, "DatasetArn": {}, "Status": {}, "StatusMessage": {}, "StatusMessageCode": {}}}}}}}, "NextToken": {}}}}, "DescribeStreamProcessor": {"input": {"type": "structure", "required": ["Name"], "members": {"Name": {}}}, "output": {"type": "structure", "members": {"Name": {}, "StreamProcessorArn": {}, "Status": {}, "StatusMessage": {}, "CreationTimestamp": {"type": "timestamp"}, "LastUpdateTimestamp": {"type": "timestamp"}, "Input": {"shape": "S1q"}, "Output": {"shape": "S1t"}, "RoleArn": {}, "Settings": {"shape": "S1y"}, "NotificationChannel": {"shape": "S24"}, "KmsKeyId": {}, "RegionsOfInterest": {"shape": "S26"}, "DataSharingPreference": {"shape": "S2a"}}}}, "DetectCustomLabels": {"input": {"type": "structure", "required": ["ProjectVersionArn", "Image"], "members": {"ProjectVersionArn": {}, "Image": {"shape": "S2"}, "MaxResults": {"type": "integer"}, "MinConfidence": {"type": "float"}}}, "output": {"type": "structure", "members": {"CustomLabels": {"type": "list", "member": {"type": "structure", "members": {"Name": {}, "Confidence": {"type": "float"}, "Geometry": {"shape": "S40"}}}}}}}, "DetectFaces": {"input": {"type": "structure", "required": ["Image"], "members": {"Image": {"shape": "S2"}, "Attributes": {"shape": "S42"}}}, "output": {"type": "structure", "members": {"FaceDetails": {"type": "list", "member": {"shape": "S46"}}, "OrientationCorrection": {}}}}, "DetectLabels": {"input": {"type": "structure", "required": ["Image"], "members": {"Image": {"shape": "S2"}, "MaxLabels": {"type": "integer"}, "MinConfidence": {"type": "float"}, "Features": {"type": "list", "member": {}}, "Settings": {"type": "structure", "members": {"GeneralLabels": {"shape": "S4k"}, "ImageProperties": {"type": "structure", "members": {"MaxDominantColors": {"type": "integer"}}}}}}}, "output": {"type": "structure", "members": {"Labels": {"type": "list", "member": {"shape": "S4r"}}, "OrientationCorrection": {}, "LabelModelVersion": {}, "ImageProperties": {"type": "structure", "members": {"Quality": {"shape": "S53"}, "DominantColors": {"shape": "S4u"}, "Foreground": {"type": "structure", "members": {"Quality": {"shape": "S53"}, "DominantColors": {"shape": "S4u"}}}, "Background": {"type": "structure", "members": {"Quality": {"shape": "S53"}, "DominantColors": {"shape": "S4u"}}}}}}}}, "DetectModerationLabels": {"input": {"type": "structure", "required": ["Image"], "members": {"Image": {"shape": "S2"}, "MinConfidence": {"type": "float"}, "HumanLoopConfig": {"type": "structure", "required": ["HumanLoopName", "FlowDefinitionArn"], "members": {"HumanLoopName": {}, "FlowDefinitionArn": {}, "DataAttributes": {"type": "structure", "members": {"ContentClassifiers": {"type": "list", "member": {}}}}}}}}, "output": {"type": "structure", "members": {"ModerationLabels": {"type": "list", "member": {"shape": "S5f"}}, "ModerationModelVersion": {}, "HumanLoopActivationOutput": {"type": "structure", "members": {"HumanLoopArn": {}, "HumanLoopActivationReasons": {"type": "list", "member": {}}, "HumanLoopActivationConditionsEvaluationResults": {"jsonvalue": true}}}}}}, "DetectProtectiveEquipment": {"input": {"type": "structure", "required": ["Image"], "members": {"Image": {"shape": "S2"}, "SummarizationAttributes": {"type": "structure", "required": ["MinConfidence", "RequiredEquipmentTypes"], "members": {"MinConfidence": {"type": "float"}, "RequiredEquipmentTypes": {"type": "list", "member": {}}}}}}, "output": {"type": "structure", "members": {"ProtectiveEquipmentModelVersion": {}, "Persons": {"type": "list", "member": {"type": "structure", "members": {"BodyParts": {"type": "list", "member": {"type": "structure", "members": {"Name": {}, "Confidence": {"type": "float"}, "EquipmentDetections": {"type": "list", "member": {"type": "structure", "members": {"BoundingBox": {"shape": "Sc"}, "Confidence": {"type": "float"}, "Type": {}, "CoversBodyPart": {"type": "structure", "members": {"Confidence": {"type": "float"}, "Value": {"type": "boolean"}}}}}}}}}, "BoundingBox": {"shape": "Sc"}, "Confidence": {"type": "float"}, "Id": {"type": "integer"}}}}, "Summary": {"type": "structure", "members": {"PersonsWithRequiredEquipment": {"shape": "S5z"}, "PersonsWithoutRequiredEquipment": {"shape": "S5z"}, "PersonsIndeterminate": {"shape": "S5z"}}}}}}, "DetectText": {"input": {"type": "structure", "required": ["Image"], "members": {"Image": {"shape": "S2"}, "Filters": {"type": "structure", "members": {"WordFilter": {"shape": "S62"}, "RegionsOfInterest": {"shape": "S26"}}}}}, "output": {"type": "structure", "members": {"TextDetections": {"type": "list", "member": {"shape": "S67"}}, "TextModelVersion": {}}}}, "DistributeDatasetEntries": {"input": {"type": "structure", "required": ["Datasets"], "members": {"Datasets": {"type": "list", "member": {"type": "structure", "required": ["<PERSON><PERSON>"], "members": {"Arn": {}}}}}}, "output": {"type": "structure", "members": {}}}, "GetCelebrityInfo": {"input": {"type": "structure", "required": ["Id"], "members": {"Id": {}}}, "output": {"type": "structure", "members": {"Urls": {"shape": "S6g"}, "Name": {}, "KnownGender": {"shape": "S6i"}}}}, "GetCelebrityRecognition": {"input": {"type": "structure", "required": ["JobId"], "members": {"JobId": {}, "MaxResults": {"type": "integer"}, "NextToken": {}, "SortBy": {}}}, "output": {"type": "structure", "members": {"JobStatus": {}, "StatusMessage": {}, "VideoMetadata": {"shape": "S6r"}, "NextToken": {}, "Celebrities": {"type": "list", "member": {"type": "structure", "members": {"Timestamp": {"type": "long"}, "Celebrity": {"type": "structure", "members": {"Urls": {"shape": "S6g"}, "Name": {}, "Id": {}, "Confidence": {"type": "float"}, "BoundingBox": {"shape": "Sc"}, "Face": {"shape": "S46"}, "KnownGender": {"shape": "S6i"}}}}}}}}}, "GetContentModeration": {"input": {"type": "structure", "required": ["JobId"], "members": {"JobId": {}, "MaxResults": {"type": "integer"}, "NextToken": {}, "SortBy": {}}}, "output": {"type": "structure", "members": {"JobStatus": {}, "StatusMessage": {}, "VideoMetadata": {"shape": "S6r"}, "ModerationLabels": {"type": "list", "member": {"type": "structure", "members": {"Timestamp": {"type": "long"}, "ModerationLabel": {"shape": "S5f"}}}}, "NextToken": {}, "ModerationModelVersion": {}}}}, "GetFaceDetection": {"input": {"type": "structure", "required": ["JobId"], "members": {"JobId": {}, "MaxResults": {"type": "integer"}, "NextToken": {}}}, "output": {"type": "structure", "members": {"JobStatus": {}, "StatusMessage": {}, "VideoMetadata": {"shape": "S6r"}, "NextToken": {}, "Faces": {"type": "list", "member": {"type": "structure", "members": {"Timestamp": {"type": "long"}, "Face": {"shape": "S46"}}}}}}}, "GetFaceSearch": {"input": {"type": "structure", "required": ["JobId"], "members": {"JobId": {}, "MaxResults": {"type": "integer"}, "NextToken": {}, "SortBy": {}}}, "output": {"type": "structure", "members": {"JobStatus": {}, "StatusMessage": {}, "NextToken": {}, "VideoMetadata": {"shape": "S6r"}, "Persons": {"type": "list", "member": {"type": "structure", "members": {"Timestamp": {"type": "long"}, "Person": {"shape": "S7b"}, "FaceMatches": {"shape": "S7d"}}}}}}}, "GetLabelDetection": {"input": {"type": "structure", "required": ["JobId"], "members": {"JobId": {}, "MaxResults": {"type": "integer"}, "NextToken": {}, "SortBy": {}, "AggregateBy": {}}}, "output": {"type": "structure", "members": {"JobStatus": {}, "StatusMessage": {}, "VideoMetadata": {"shape": "S6r"}, "NextToken": {}, "Labels": {"type": "list", "member": {"type": "structure", "members": {"Timestamp": {"type": "long"}, "Label": {"shape": "S4r"}, "StartTimestampMillis": {"type": "long"}, "EndTimestampMillis": {"type": "long"}, "DurationMillis": {"type": "long"}}}}, "LabelModelVersion": {}}}}, "GetPersonTracking": {"input": {"type": "structure", "required": ["JobId"], "members": {"JobId": {}, "MaxResults": {"type": "integer"}, "NextToken": {}, "SortBy": {}}}, "output": {"type": "structure", "members": {"JobStatus": {}, "StatusMessage": {}, "VideoMetadata": {"shape": "S6r"}, "NextToken": {}, "Persons": {"type": "list", "member": {"type": "structure", "members": {"Timestamp": {"type": "long"}, "Person": {"shape": "S7b"}}}}}}}, "GetSegmentDetection": {"input": {"type": "structure", "required": ["JobId"], "members": {"JobId": {}, "MaxResults": {"type": "integer"}, "NextToken": {}}}, "output": {"type": "structure", "members": {"JobStatus": {}, "StatusMessage": {}, "VideoMetadata": {"type": "list", "member": {"shape": "S6r"}}, "AudioMetadata": {"type": "list", "member": {"type": "structure", "members": {"Codec": {}, "DurationMillis": {"type": "long"}, "SampleRate": {"type": "long"}, "NumberOfChannels": {"type": "long"}}}}, "NextToken": {}, "Segments": {"type": "list", "member": {"type": "structure", "members": {"Type": {}, "StartTimestampMillis": {"type": "long"}, "EndTimestampMillis": {"type": "long"}, "DurationMillis": {"type": "long"}, "StartTimecodeSMPTE": {}, "EndTimecodeSMPTE": {}, "DurationSMPTE": {}, "TechnicalCueSegment": {"type": "structure", "members": {"Type": {}, "Confidence": {"type": "float"}}}, "ShotSegment": {"type": "structure", "members": {"Index": {"type": "long"}, "Confidence": {"type": "float"}}}, "StartFrameNumber": {"type": "long"}, "EndFrameNumber": {"type": "long"}, "DurationFrames": {"type": "long"}}}}, "SelectedSegmentTypes": {"type": "list", "member": {"type": "structure", "members": {"Type": {}, "ModelVersion": {}}}}}}}, "GetTextDetection": {"input": {"type": "structure", "required": ["JobId"], "members": {"JobId": {}, "MaxResults": {"type": "integer"}, "NextToken": {}}}, "output": {"type": "structure", "members": {"JobStatus": {}, "StatusMessage": {}, "VideoMetadata": {"shape": "S6r"}, "TextDetections": {"type": "list", "member": {"type": "structure", "members": {"Timestamp": {"type": "long"}, "TextDetection": {"shape": "S67"}}}}, "NextToken": {}, "TextModelVersion": {}}}}, "IndexFaces": {"input": {"type": "structure", "required": ["CollectionId", "Image"], "members": {"CollectionId": {}, "Image": {"shape": "S2"}, "ExternalImageId": {}, "DetectionAttributes": {"shape": "S42"}, "MaxFaces": {"type": "integer"}, "QualityFilter": {}}}, "output": {"type": "structure", "members": {"FaceRecords": {"type": "list", "member": {"type": "structure", "members": {"Face": {"shape": "S7f"}, "FaceDetail": {"shape": "S46"}}}}, "OrientationCorrection": {}, "FaceModelVersion": {}, "UnindexedFaces": {"type": "list", "member": {"type": "structure", "members": {"Reasons": {"type": "list", "member": {}}, "FaceDetail": {"shape": "S46"}}}}}}}, "ListCollections": {"input": {"type": "structure", "members": {"NextToken": {}, "MaxResults": {"type": "integer"}}}, "output": {"type": "structure", "members": {"CollectionIds": {"type": "list", "member": {}}, "NextToken": {}, "FaceModelVersions": {"type": "list", "member": {}}}}}, "ListDatasetEntries": {"input": {"type": "structure", "required": ["DatasetArn"], "members": {"DatasetArn": {}, "ContainsLabels": {"type": "list", "member": {}}, "Labeled": {"type": "boolean"}, "SourceRefContains": {}, "HasErrors": {"type": "boolean"}, "NextToken": {}, "MaxResults": {"type": "integer"}}}, "output": {"type": "structure", "members": {"DatasetEntries": {"type": "list", "member": {}}, "NextToken": {}}}}, "ListDatasetLabels": {"input": {"type": "structure", "required": ["DatasetArn"], "members": {"DatasetArn": {}, "NextToken": {}, "MaxResults": {"type": "integer"}}}, "output": {"type": "structure", "members": {"DatasetLabelDescriptions": {"type": "list", "member": {"type": "structure", "members": {"LabelName": {}, "LabelStats": {"type": "structure", "members": {"EntryCount": {"type": "integer"}, "BoundingBoxCount": {"type": "integer"}}}}}}, "NextToken": {}}}}, "ListFaces": {"input": {"type": "structure", "required": ["CollectionId"], "members": {"CollectionId": {}, "NextToken": {}, "MaxResults": {"type": "integer"}}}, "output": {"type": "structure", "members": {"Faces": {"type": "list", "member": {"shape": "S7f"}}, "NextToken": {}, "FaceModelVersion": {}}}}, "ListProjectPolicies": {"input": {"type": "structure", "required": ["ProjectArn"], "members": {"ProjectArn": {}, "NextToken": {}, "MaxResults": {"type": "integer"}}}, "output": {"type": "structure", "members": {"ProjectPolicies": {"type": "list", "member": {"type": "structure", "members": {"ProjectArn": {}, "PolicyName": {}, "PolicyRevisionId": {}, "PolicyDocument": {}, "CreationTimestamp": {"type": "timestamp"}, "LastUpdatedTimestamp": {"type": "timestamp"}}}}, "NextToken": {}}}}, "ListStreamProcessors": {"input": {"type": "structure", "members": {"NextToken": {}, "MaxResults": {"type": "integer"}}}, "output": {"type": "structure", "members": {"NextToken": {}, "StreamProcessors": {"type": "list", "member": {"type": "structure", "members": {"Name": {}, "Status": {}}}}}}}, "ListTagsForResource": {"input": {"type": "structure", "required": ["ResourceArn"], "members": {"ResourceArn": {}}}, "output": {"type": "structure", "members": {"Tags": {"shape": "S10"}}}}, "PutProjectPolicy": {"input": {"type": "structure", "required": ["ProjectArn", "PolicyName", "PolicyDocument"], "members": {"ProjectArn": {}, "PolicyName": {}, "PolicyRevisionId": {}, "PolicyDocument": {}}}, "output": {"type": "structure", "members": {"PolicyRevisionId": {}}}}, "RecognizeCelebrities": {"input": {"type": "structure", "required": ["Image"], "members": {"Image": {"shape": "S2"}}}, "output": {"type": "structure", "members": {"CelebrityFaces": {"type": "list", "member": {"type": "structure", "members": {"Urls": {"shape": "S6g"}, "Name": {}, "Id": {}, "Face": {"shape": "Sg"}, "MatchConfidence": {"type": "float"}, "KnownGender": {"shape": "S6i"}}}}, "UnrecognizedFaces": {"type": "list", "member": {"shape": "Sg"}}, "OrientationCorrection": {}}}}, "SearchFaces": {"input": {"type": "structure", "required": ["CollectionId", "FaceId"], "members": {"CollectionId": {}, "FaceId": {}, "MaxFaces": {"type": "integer"}, "FaceMatchThreshold": {"type": "float"}}}, "output": {"type": "structure", "members": {"SearchedFaceId": {}, "FaceMatches": {"shape": "S7d"}, "FaceModelVersion": {}}}}, "SearchFacesByImage": {"input": {"type": "structure", "required": ["CollectionId", "Image"], "members": {"CollectionId": {}, "Image": {"shape": "S2"}, "MaxFaces": {"type": "integer"}, "FaceMatchThreshold": {"type": "float"}, "QualityFilter": {}}}, "output": {"type": "structure", "members": {"SearchedFaceBoundingBox": {"shape": "Sc"}, "SearchedFaceConfidence": {"type": "float"}, "FaceMatches": {"shape": "S7d"}, "FaceModelVersion": {}}}}, "StartCelebrityRecognition": {"input": {"type": "structure", "required": ["Video"], "members": {"Video": {"shape": "Sa0"}, "ClientRequestToken": {}, "NotificationChannel": {"shape": "Sa2"}, "JobTag": {}}}, "output": {"type": "structure", "members": {"JobId": {}}}, "idempotent": true}, "StartContentModeration": {"input": {"type": "structure", "required": ["Video"], "members": {"Video": {"shape": "Sa0"}, "MinConfidence": {"type": "float"}, "ClientRequestToken": {}, "NotificationChannel": {"shape": "Sa2"}, "JobTag": {}}}, "output": {"type": "structure", "members": {"JobId": {}}}, "idempotent": true}, "StartFaceDetection": {"input": {"type": "structure", "required": ["Video"], "members": {"Video": {"shape": "Sa0"}, "ClientRequestToken": {}, "NotificationChannel": {"shape": "Sa2"}, "FaceAttributes": {}, "JobTag": {}}}, "output": {"type": "structure", "members": {"JobId": {}}}, "idempotent": true}, "StartFaceSearch": {"input": {"type": "structure", "required": ["Video", "CollectionId"], "members": {"Video": {"shape": "Sa0"}, "ClientRequestToken": {}, "FaceMatchThreshold": {"type": "float"}, "CollectionId": {}, "NotificationChannel": {"shape": "Sa2"}, "JobTag": {}}}, "output": {"type": "structure", "members": {"JobId": {}}}, "idempotent": true}, "StartLabelDetection": {"input": {"type": "structure", "required": ["Video"], "members": {"Video": {"shape": "Sa0"}, "ClientRequestToken": {}, "MinConfidence": {"type": "float"}, "NotificationChannel": {"shape": "Sa2"}, "JobTag": {}, "Features": {"type": "list", "member": {}}, "Settings": {"type": "structure", "members": {"GeneralLabels": {"shape": "S4k"}}}}}, "output": {"type": "structure", "members": {"JobId": {}}}, "idempotent": true}, "StartPersonTracking": {"input": {"type": "structure", "required": ["Video"], "members": {"Video": {"shape": "Sa0"}, "ClientRequestToken": {}, "NotificationChannel": {"shape": "Sa2"}, "JobTag": {}}}, "output": {"type": "structure", "members": {"JobId": {}}}, "idempotent": true}, "StartProjectVersion": {"input": {"type": "structure", "required": ["ProjectVersionArn", "MinInferenceUnits"], "members": {"ProjectVersionArn": {}, "MinInferenceUnits": {"type": "integer"}, "MaxInferenceUnits": {"type": "integer"}}}, "output": {"type": "structure", "members": {"Status": {}}}}, "StartSegmentDetection": {"input": {"type": "structure", "required": ["Video", "SegmentTypes"], "members": {"Video": {"shape": "Sa0"}, "ClientRequestToken": {}, "NotificationChannel": {"shape": "Sa2"}, "JobTag": {}, "Filters": {"type": "structure", "members": {"TechnicalCueFilter": {"type": "structure", "members": {"MinSegmentConfidence": {"type": "float"}, "BlackFrame": {"type": "structure", "members": {"MaxPixelThreshold": {"type": "float"}, "MinCoveragePercentage": {"type": "float"}}}}}, "ShotFilter": {"type": "structure", "members": {"MinSegmentConfidence": {"type": "float"}}}}}, "SegmentTypes": {"type": "list", "member": {}}}}, "output": {"type": "structure", "members": {"JobId": {}}}, "idempotent": true}, "StartStreamProcessor": {"input": {"type": "structure", "required": ["Name"], "members": {"Name": {}, "StartSelector": {"type": "structure", "members": {"KVSStreamStartSelector": {"type": "structure", "members": {"ProducerTimestamp": {"type": "long"}, "FragmentNumber": {}}}}}, "StopSelector": {"type": "structure", "members": {"MaxDurationInSeconds": {"type": "long"}}}}}, "output": {"type": "structure", "members": {"SessionId": {}}}}, "StartTextDetection": {"input": {"type": "structure", "required": ["Video"], "members": {"Video": {"shape": "Sa0"}, "ClientRequestToken": {}, "NotificationChannel": {"shape": "Sa2"}, "JobTag": {}, "Filters": {"type": "structure", "members": {"WordFilter": {"shape": "S62"}, "RegionsOfInterest": {"shape": "S26"}}}}}, "output": {"type": "structure", "members": {"JobId": {}}}, "idempotent": true}, "StopProjectVersion": {"input": {"type": "structure", "required": ["ProjectVersionArn"], "members": {"ProjectVersionArn": {}}}, "output": {"type": "structure", "members": {"Status": {}}}}, "StopStreamProcessor": {"input": {"type": "structure", "required": ["Name"], "members": {"Name": {}}}, "output": {"type": "structure", "members": {}}}, "TagResource": {"input": {"type": "structure", "required": ["ResourceArn", "Tags"], "members": {"ResourceArn": {}, "Tags": {"shape": "S10"}}}, "output": {"type": "structure", "members": {}}}, "UntagResource": {"input": {"type": "structure", "required": ["ResourceArn", "TagKeys"], "members": {"ResourceArn": {}, "TagKeys": {"type": "list", "member": {}}}}, "output": {"type": "structure", "members": {}}}, "UpdateDatasetEntries": {"input": {"type": "structure", "required": ["DatasetArn", "Changes"], "members": {"DatasetArn": {}, "Changes": {"type": "structure", "required": ["GroundTruth"], "members": {"GroundTruth": {"type": "blob"}}}}}, "output": {"type": "structure", "members": {}}}, "UpdateStreamProcessor": {"input": {"type": "structure", "required": ["Name"], "members": {"Name": {}, "SettingsForUpdate": {"type": "structure", "members": {"ConnectedHomeForUpdate": {"type": "structure", "members": {"Labels": {"shape": "S21"}, "MinConfidence": {"type": "float"}}}}}, "RegionsOfInterestForUpdate": {"shape": "S26"}, "DataSharingPreferenceForUpdate": {"shape": "S2a"}, "ParametersToDelete": {"type": "list", "member": {}}}}, "output": {"type": "structure", "members": {}}}}, "shapes": {"S2": {"type": "structure", "members": {"Bytes": {"type": "blob"}, "S3Object": {"shape": "S4"}}}, "S4": {"type": "structure", "members": {"Bucket": {}, "Name": {}, "Version": {}}}, "Sc": {"type": "structure", "members": {"Width": {"type": "float"}, "Height": {"type": "float"}, "Left": {"type": "float"}, "Top": {"type": "float"}}}, "Sg": {"type": "structure", "members": {"BoundingBox": {"shape": "Sc"}, "Confidence": {"type": "float"}, "Landmarks": {"shape": "Sh"}, "Pose": {"shape": "Sk"}, "Quality": {"shape": "Sm"}, "Emotions": {"shape": "Sn"}, "Smile": {"shape": "Sq"}}}, "Sh": {"type": "list", "member": {"type": "structure", "members": {"Type": {}, "X": {"type": "float"}, "Y": {"type": "float"}}}}, "Sk": {"type": "structure", "members": {"Roll": {"type": "float"}, "Yaw": {"type": "float"}, "Pitch": {"type": "float"}}}, "Sm": {"type": "structure", "members": {"Brightness": {"type": "float"}, "Sharpness": {"type": "float"}}}, "Sn": {"type": "list", "member": {"type": "structure", "members": {"Type": {}, "Confidence": {"type": "float"}}}}, "Sq": {"type": "structure", "members": {"Value": {"type": "boolean"}, "Confidence": {"type": "float"}}}, "Sy": {"type": "structure", "members": {"S3Bucket": {}, "S3KeyPrefix": {}}}, "S10": {"type": "map", "key": {}, "value": {}}, "S1c": {"type": "structure", "members": {"S3Object": {"shape": "S4"}}}, "S1k": {"type": "structure", "members": {"Assets": {"shape": "S1l"}}}, "S1l": {"type": "list", "member": {"type": "structure", "members": {"GroundTruthManifest": {"shape": "S1c"}}}}, "S1n": {"type": "structure", "members": {"Assets": {"shape": "S1l"}, "AutoCreate": {"type": "boolean"}}}, "S1q": {"type": "structure", "members": {"KinesisVideoStream": {"type": "structure", "members": {"Arn": {}}}}}, "S1t": {"type": "structure", "members": {"KinesisDataStream": {"type": "structure", "members": {"Arn": {}}}, "S3Destination": {"type": "structure", "members": {"Bucket": {}, "KeyPrefix": {}}}}}, "S1y": {"type": "structure", "members": {"FaceSearch": {"type": "structure", "members": {"CollectionId": {}, "FaceMatchThreshold": {"type": "float"}}}, "ConnectedHome": {"type": "structure", "required": ["Labels"], "members": {"Labels": {"shape": "S21"}, "MinConfidence": {"type": "float"}}}}}, "S21": {"type": "list", "member": {}}, "S24": {"type": "structure", "required": ["SNSTopicArn"], "members": {"SNSTopicArn": {}}}, "S26": {"type": "list", "member": {"type": "structure", "members": {"BoundingBox": {"shape": "Sc"}, "Polygon": {"shape": "S28"}}}}, "S28": {"type": "list", "member": {"type": "structure", "members": {"X": {"type": "float"}, "Y": {"type": "float"}}}}, "S2a": {"type": "structure", "required": ["OptIn"], "members": {"OptIn": {"type": "boolean"}}}, "S2i": {"type": "list", "member": {}}, "S3h": {"type": "structure", "members": {"Assets": {"shape": "S1l"}}}, "S40": {"type": "structure", "members": {"BoundingBox": {"shape": "Sc"}, "Polygon": {"shape": "S28"}}}, "S42": {"type": "list", "member": {}}, "S46": {"type": "structure", "members": {"BoundingBox": {"shape": "Sc"}, "AgeRange": {"type": "structure", "members": {"Low": {"type": "integer"}, "High": {"type": "integer"}}}, "Smile": {"shape": "Sq"}, "Eyeglasses": {"type": "structure", "members": {"Value": {"type": "boolean"}, "Confidence": {"type": "float"}}}, "Sunglasses": {"type": "structure", "members": {"Value": {"type": "boolean"}, "Confidence": {"type": "float"}}}, "Gender": {"type": "structure", "members": {"Value": {}, "Confidence": {"type": "float"}}}, "Beard": {"type": "structure", "members": {"Value": {"type": "boolean"}, "Confidence": {"type": "float"}}}, "Mustache": {"type": "structure", "members": {"Value": {"type": "boolean"}, "Confidence": {"type": "float"}}}, "EyesOpen": {"type": "structure", "members": {"Value": {"type": "boolean"}, "Confidence": {"type": "float"}}}, "MouthOpen": {"type": "structure", "members": {"Value": {"type": "boolean"}, "Confidence": {"type": "float"}}}, "Emotions": {"shape": "Sn"}, "Landmarks": {"shape": "Sh"}, "Pose": {"shape": "Sk"}, "Quality": {"shape": "Sm"}, "Confidence": {"type": "float"}}}, "S4k": {"type": "structure", "members": {"LabelInclusionFilters": {"shape": "S4l"}, "LabelExclusionFilters": {"shape": "S4l"}, "LabelCategoryInclusionFilters": {"shape": "S4l"}, "LabelCategoryExclusionFilters": {"shape": "S4l"}}}, "S4l": {"type": "list", "member": {}}, "S4r": {"type": "structure", "members": {"Name": {}, "Confidence": {"type": "float"}, "Instances": {"type": "list", "member": {"type": "structure", "members": {"BoundingBox": {"shape": "Sc"}, "Confidence": {"type": "float"}, "DominantColors": {"shape": "S4u"}}}}, "Parents": {"type": "list", "member": {"type": "structure", "members": {"Name": {}}}}, "Aliases": {"type": "list", "member": {"type": "structure", "members": {"Name": {}}}}, "Categories": {"type": "list", "member": {"type": "structure", "members": {"Name": {}}}}}}, "S4u": {"type": "list", "member": {"type": "structure", "members": {"Red": {"type": "integer"}, "Blue": {"type": "integer"}, "Green": {"type": "integer"}, "HexCode": {}, "CSSColor": {}, "SimplifiedColor": {}, "PixelPercent": {"type": "float"}}}}, "S53": {"type": "structure", "members": {"Brightness": {"type": "float"}, "Sharpness": {"type": "float"}, "Contrast": {"type": "float"}}}, "S5f": {"type": "structure", "members": {"Confidence": {"type": "float"}, "Name": {}, "ParentName": {}}}, "S5z": {"type": "list", "member": {"type": "integer"}}, "S62": {"type": "structure", "members": {"MinConfidence": {"type": "float"}, "MinBoundingBoxHeight": {"type": "float"}, "MinBoundingBoxWidth": {"type": "float"}}}, "S67": {"type": "structure", "members": {"DetectedText": {}, "Type": {}, "Id": {"type": "integer"}, "ParentId": {"type": "integer"}, "Confidence": {"type": "float"}, "Geometry": {"shape": "S40"}}}, "S6g": {"type": "list", "member": {}}, "S6i": {"type": "structure", "members": {"Type": {}}}, "S6r": {"type": "structure", "members": {"Codec": {}, "DurationMillis": {"type": "long"}, "Format": {}, "FrameRate": {"type": "float"}, "FrameHeight": {"type": "long"}, "FrameWidth": {"type": "long"}, "ColorRange": {}}}, "S7b": {"type": "structure", "members": {"Index": {"type": "long"}, "BoundingBox": {"shape": "Sc"}, "Face": {"shape": "S46"}}}, "S7d": {"type": "list", "member": {"type": "structure", "members": {"Similarity": {"type": "float"}, "Face": {"shape": "S7f"}}}}, "S7f": {"type": "structure", "members": {"FaceId": {}, "BoundingBox": {"shape": "Sc"}, "ImageId": {}, "ExternalImageId": {}, "Confidence": {"type": "float"}, "IndexFacesModelVersion": {}}}, "Sa0": {"type": "structure", "members": {"S3Object": {"shape": "S4"}}}, "Sa2": {"type": "structure", "required": ["SNSTopicArn", "RoleArn"], "members": {"SNSTopicArn": {}, "RoleArn": {}}}}}