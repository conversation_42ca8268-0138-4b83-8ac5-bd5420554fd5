{"version": "2.0", "metadata": {"apiVersion": "2019-02-03", "endpointPrefix": "kendra", "jsonVersion": "1.1", "protocol": "json", "serviceAbbreviation": "kendra", "serviceFullName": "AWSKendraFrontendService", "serviceId": "kendra", "signatureVersion": "v4", "signingName": "kendra", "targetPrefix": "AWSKendraFrontendService", "uid": "kendra-2019-02-03"}, "operations": {"AssociateEntitiesToExperience": {"input": {"type": "structure", "required": ["Id", "IndexId", "EntityList"], "members": {"Id": {}, "IndexId": {}, "EntityList": {"type": "list", "member": {"shape": "S5"}}}}, "output": {"type": "structure", "members": {"FailedEntityList": {"type": "list", "member": {"shape": "Sa"}}}}}, "AssociatePersonasToEntities": {"input": {"type": "structure", "required": ["Id", "IndexId", "Personas"], "members": {"Id": {}, "IndexId": {}, "Personas": {"type": "list", "member": {"type": "structure", "required": ["EntityId", "<PERSON>a"], "members": {"EntityId": {}, "Persona": {}}}}}}, "output": {"type": "structure", "members": {"FailedEntityList": {"shape": "Sh"}}}}, "BatchDeleteDocument": {"input": {"type": "structure", "required": ["IndexId", "DocumentIdList"], "members": {"IndexId": {}, "DocumentIdList": {"type": "list", "member": {}}, "DataSourceSyncJobMetricTarget": {"type": "structure", "required": ["DataSourceId"], "members": {"DataSourceId": {}, "DataSourceSyncJobId": {}}}}}, "output": {"type": "structure", "members": {"FailedDocuments": {"type": "list", "member": {"type": "structure", "members": {"Id": {}, "ErrorCode": {}, "ErrorMessage": {}}}}}}}, "BatchGetDocumentStatus": {"input": {"type": "structure", "required": ["IndexId", "DocumentInfoList"], "members": {"IndexId": {}, "DocumentInfoList": {"type": "list", "member": {"type": "structure", "required": ["DocumentId"], "members": {"DocumentId": {}, "Attributes": {"shape": "Sv"}}}}}}, "output": {"type": "structure", "members": {"Errors": {"type": "list", "member": {"type": "structure", "members": {"DocumentId": {}, "ErrorCode": {}, "ErrorMessage": {}}}}, "DocumentStatusList": {"type": "list", "member": {"type": "structure", "members": {"DocumentId": {}, "DocumentStatus": {}, "FailureCode": {}, "FailureReason": {}}}}}}}, "BatchPutDocument": {"input": {"type": "structure", "required": ["IndexId", "Documents"], "members": {"IndexId": {}, "RoleArn": {}, "Documents": {"type": "list", "member": {"type": "structure", "required": ["Id"], "members": {"Id": {}, "Title": {}, "Blob": {"type": "blob"}, "S3Path": {"shape": "S1g"}, "Attributes": {"shape": "Sv"}, "AccessControlList": {"shape": "S1j"}, "HierarchicalAccessControlList": {"shape": "S1o"}, "ContentType": {}, "AccessControlConfigurationId": {}}}}, "CustomDocumentEnrichmentConfiguration": {"shape": "S1s"}}}, "output": {"type": "structure", "members": {"FailedDocuments": {"type": "list", "member": {"type": "structure", "members": {"Id": {}, "ErrorCode": {}, "ErrorMessage": {}}}}}}}, "ClearQuerySuggestions": {"input": {"type": "structure", "required": ["IndexId"], "members": {"IndexId": {}}}}, "CreateAccessControlConfiguration": {"input": {"type": "structure", "required": ["IndexId", "Name"], "members": {"IndexId": {}, "Name": {}, "Description": {}, "AccessControlList": {"shape": "S1j"}, "HierarchicalAccessControlList": {"shape": "S1o"}, "ClientToken": {"idempotencyToken": true}}}, "output": {"type": "structure", "required": ["Id"], "members": {"Id": {}}}}, "CreateDataSource": {"input": {"type": "structure", "required": ["Name", "IndexId", "Type"], "members": {"Name": {}, "IndexId": {}, "Type": {}, "Configuration": {"shape": "S2d"}, "VpcConfiguration": {"shape": "S2o"}, "Description": {}, "Schedule": {}, "RoleArn": {}, "Tags": {"shape": "S6l"}, "ClientToken": {"idempotencyToken": true}, "LanguageCode": {}, "CustomDocumentEnrichmentConfiguration": {"shape": "S1s"}}}, "output": {"type": "structure", "required": ["Id"], "members": {"Id": {}}}}, "CreateExperience": {"input": {"type": "structure", "required": ["Name", "IndexId"], "members": {"Name": {}, "IndexId": {}, "RoleArn": {}, "Configuration": {"shape": "S6t"}, "Description": {}, "ClientToken": {"idempotencyToken": true}}}, "output": {"type": "structure", "required": ["Id"], "members": {"Id": {}}}}, "CreateFaq": {"input": {"type": "structure", "required": ["IndexId", "Name", "S3Path", "RoleArn"], "members": {"IndexId": {}, "Name": {}, "Description": {}, "S3Path": {"shape": "S1g"}, "RoleArn": {}, "Tags": {"shape": "S6l"}, "FileFormat": {}, "ClientToken": {"idempotencyToken": true}, "LanguageCode": {}}}, "output": {"type": "structure", "members": {"Id": {}}}}, "CreateIndex": {"input": {"type": "structure", "required": ["Name", "RoleArn"], "members": {"Name": {}, "Edition": {}, "RoleArn": {}, "ServerSideEncryptionConfiguration": {"shape": "S78"}, "Description": {}, "ClientToken": {"idempotencyToken": true}, "Tags": {"shape": "S6l"}, "UserTokenConfigurations": {"shape": "S7a"}, "UserContextPolicy": {}, "UserGroupResolutionConfiguration": {"shape": "S7k"}}}, "output": {"type": "structure", "members": {"Id": {}}}}, "CreateQuerySuggestionsBlockList": {"input": {"type": "structure", "required": ["IndexId", "Name", "SourceS3Path", "RoleArn"], "members": {"IndexId": {}, "Name": {}, "Description": {}, "SourceS3Path": {"shape": "S1g"}, "ClientToken": {"idempotencyToken": true}, "RoleArn": {}, "Tags": {"shape": "S6l"}}}, "output": {"type": "structure", "members": {"Id": {}}}}, "CreateThesaurus": {"input": {"type": "structure", "required": ["IndexId", "Name", "RoleArn", "SourceS3Path"], "members": {"IndexId": {}, "Name": {}, "Description": {}, "RoleArn": {}, "Tags": {"shape": "S6l"}, "SourceS3Path": {"shape": "S1g"}, "ClientToken": {"idempotencyToken": true}}}, "output": {"type": "structure", "members": {"Id": {}}}}, "DeleteAccessControlConfiguration": {"input": {"type": "structure", "required": ["IndexId", "Id"], "members": {"IndexId": {}, "Id": {}}}, "output": {"type": "structure", "members": {}}}, "DeleteDataSource": {"input": {"type": "structure", "required": ["Id", "IndexId"], "members": {"Id": {}, "IndexId": {}}}}, "DeleteExperience": {"input": {"type": "structure", "required": ["Id", "IndexId"], "members": {"Id": {}, "IndexId": {}}}, "output": {"type": "structure", "members": {}}}, "DeleteFaq": {"input": {"type": "structure", "required": ["Id", "IndexId"], "members": {"Id": {}, "IndexId": {}}}}, "DeleteIndex": {"input": {"type": "structure", "required": ["Id"], "members": {"Id": {}}}}, "DeletePrincipalMapping": {"input": {"type": "structure", "required": ["IndexId", "GroupId"], "members": {"IndexId": {}, "DataSourceId": {}, "GroupId": {}, "OrderingId": {"type": "long"}}}}, "DeleteQuerySuggestionsBlockList": {"input": {"type": "structure", "required": ["IndexId", "Id"], "members": {"IndexId": {}, "Id": {}}}}, "DeleteThesaurus": {"input": {"type": "structure", "required": ["Id", "IndexId"], "members": {"Id": {}, "IndexId": {}}}}, "DescribeAccessControlConfiguration": {"input": {"type": "structure", "required": ["IndexId", "Id"], "members": {"IndexId": {}, "Id": {}}}, "output": {"type": "structure", "required": ["Name"], "members": {"Name": {}, "Description": {}, "ErrorMessage": {}, "AccessControlList": {"shape": "S1j"}, "HierarchicalAccessControlList": {"shape": "S1o"}}}}, "DescribeDataSource": {"input": {"type": "structure", "required": ["Id", "IndexId"], "members": {"Id": {}, "IndexId": {}}}, "output": {"type": "structure", "members": {"Id": {}, "IndexId": {}, "Name": {}, "Type": {}, "Configuration": {"shape": "S2d"}, "VpcConfiguration": {"shape": "S2o"}, "CreatedAt": {"type": "timestamp"}, "UpdatedAt": {"type": "timestamp"}, "Description": {}, "Status": {}, "Schedule": {}, "RoleArn": {}, "ErrorMessage": {}, "LanguageCode": {}, "CustomDocumentEnrichmentConfiguration": {"shape": "S1s"}}}}, "DescribeExperience": {"input": {"type": "structure", "required": ["Id", "IndexId"], "members": {"Id": {}, "IndexId": {}}}, "output": {"type": "structure", "members": {"Id": {}, "IndexId": {}, "Name": {}, "Endpoints": {"shape": "S8e"}, "Configuration": {"shape": "S6t"}, "CreatedAt": {"type": "timestamp"}, "UpdatedAt": {"type": "timestamp"}, "Description": {}, "Status": {}, "RoleArn": {}, "ErrorMessage": {}}}}, "DescribeFaq": {"input": {"type": "structure", "required": ["Id", "IndexId"], "members": {"Id": {}, "IndexId": {}}}, "output": {"type": "structure", "members": {"Id": {}, "IndexId": {}, "Name": {}, "Description": {}, "CreatedAt": {"type": "timestamp"}, "UpdatedAt": {"type": "timestamp"}, "S3Path": {"shape": "S1g"}, "Status": {}, "RoleArn": {}, "ErrorMessage": {}, "FileFormat": {}, "LanguageCode": {}}}}, "DescribeIndex": {"input": {"type": "structure", "required": ["Id"], "members": {"Id": {}}}, "output": {"type": "structure", "members": {"Name": {}, "Id": {}, "Edition": {}, "RoleArn": {}, "ServerSideEncryptionConfiguration": {"shape": "S78"}, "Status": {}, "Description": {}, "CreatedAt": {"type": "timestamp"}, "UpdatedAt": {"type": "timestamp"}, "DocumentMetadataConfigurations": {"shape": "S8p"}, "IndexStatistics": {"type": "structure", "required": ["FaqStatistics", "TextDocumentStatistics"], "members": {"FaqStatistics": {"type": "structure", "required": ["IndexedQuestionAnswersCount"], "members": {"IndexedQuestionAnswersCount": {"type": "integer"}}}, "TextDocumentStatistics": {"type": "structure", "required": ["IndexedTextDocumentsCount", "IndexedTextBytes"], "members": {"IndexedTextDocumentsCount": {"type": "integer"}, "IndexedTextBytes": {"type": "long"}}}}}, "ErrorMessage": {}, "CapacityUnits": {"shape": "S97"}, "UserTokenConfigurations": {"shape": "S7a"}, "UserContextPolicy": {}, "UserGroupResolutionConfiguration": {"shape": "S7k"}}}}, "DescribePrincipalMapping": {"input": {"type": "structure", "required": ["IndexId", "GroupId"], "members": {"IndexId": {}, "DataSourceId": {}, "GroupId": {}}}, "output": {"type": "structure", "members": {"IndexId": {}, "DataSourceId": {}, "GroupId": {}, "GroupOrderingIdSummaries": {"type": "list", "member": {"type": "structure", "members": {"Status": {}, "LastUpdatedAt": {"type": "timestamp"}, "ReceivedAt": {"type": "timestamp"}, "OrderingId": {"type": "long"}, "FailureReason": {}}}}}}}, "DescribeQuerySuggestionsBlockList": {"input": {"type": "structure", "required": ["IndexId", "Id"], "members": {"IndexId": {}, "Id": {}}}, "output": {"type": "structure", "members": {"IndexId": {}, "Id": {}, "Name": {}, "Description": {}, "Status": {}, "ErrorMessage": {}, "CreatedAt": {"type": "timestamp"}, "UpdatedAt": {"type": "timestamp"}, "SourceS3Path": {"shape": "S1g"}, "ItemCount": {"type": "integer"}, "FileSizeBytes": {"type": "long"}, "RoleArn": {}}}}, "DescribeQuerySuggestionsConfig": {"input": {"type": "structure", "required": ["IndexId"], "members": {"IndexId": {}}}, "output": {"type": "structure", "members": {"Mode": {}, "Status": {}, "QueryLogLookBackWindowInDays": {"type": "integer"}, "IncludeQueriesWithoutUserInformation": {"type": "boolean"}, "MinimumNumberOfQueryingUsers": {"type": "integer"}, "MinimumQueryCount": {"type": "integer"}, "LastSuggestionsBuildTime": {"type": "timestamp"}, "LastClearTime": {"type": "timestamp"}, "TotalSuggestionsCount": {"type": "integer"}}}}, "DescribeThesaurus": {"input": {"type": "structure", "required": ["Id", "IndexId"], "members": {"Id": {}, "IndexId": {}}}, "output": {"type": "structure", "members": {"Id": {}, "IndexId": {}, "Name": {}, "Description": {}, "Status": {}, "ErrorMessage": {}, "CreatedAt": {"type": "timestamp"}, "UpdatedAt": {"type": "timestamp"}, "RoleArn": {}, "SourceS3Path": {"shape": "S1g"}, "FileSizeBytes": {"type": "long"}, "TermCount": {"type": "long"}, "SynonymRuleCount": {"type": "long"}}}}, "DisassociateEntitiesFromExperience": {"input": {"type": "structure", "required": ["Id", "IndexId", "EntityList"], "members": {"Id": {}, "IndexId": {}, "EntityList": {"type": "list", "member": {"shape": "S5"}}}}, "output": {"type": "structure", "members": {"FailedEntityList": {"shape": "Sh"}}}}, "DisassociatePersonasFromEntities": {"input": {"type": "structure", "required": ["Id", "IndexId", "EntityIds"], "members": {"Id": {}, "IndexId": {}, "EntityIds": {"type": "list", "member": {}}}}, "output": {"type": "structure", "members": {"FailedEntityList": {"shape": "Sh"}}}}, "GetQuerySuggestions": {"input": {"type": "structure", "required": ["IndexId", "QueryText"], "members": {"IndexId": {}, "QueryText": {}, "MaxSuggestionsCount": {"type": "integer"}}}, "output": {"type": "structure", "members": {"QuerySuggestionsId": {}, "Suggestions": {"type": "list", "member": {"type": "structure", "members": {"Id": {}, "Value": {"type": "structure", "members": {"Text": {"type": "structure", "members": {"Text": {}, "Highlights": {"type": "list", "member": {"type": "structure", "members": {"BeginOffset": {"type": "integer"}, "EndOffset": {"type": "integer"}}}}}}}}}}}}}}, "GetSnapshots": {"input": {"type": "structure", "required": ["IndexId", "Interval", "MetricType"], "members": {"IndexId": {}, "Interval": {}, "MetricType": {}, "NextToken": {}, "MaxResults": {"type": "integer"}}}, "output": {"type": "structure", "members": {"SnapShotTimeFilter": {"shape": "Sag"}, "SnapshotsDataHeader": {"type": "list", "member": {}}, "SnapshotsData": {"type": "list", "member": {"type": "list", "member": {}}}, "NextToken": {}}}}, "ListAccessControlConfigurations": {"input": {"type": "structure", "required": ["IndexId"], "members": {"IndexId": {}, "NextToken": {}, "MaxResults": {"type": "integer"}}}, "output": {"type": "structure", "required": ["AccessControlConfigurations"], "members": {"NextToken": {}, "AccessControlConfigurations": {"type": "list", "member": {"type": "structure", "required": ["Id"], "members": {"Id": {}}}}}}}, "ListDataSourceSyncJobs": {"input": {"type": "structure", "required": ["Id", "IndexId"], "members": {"Id": {}, "IndexId": {}, "NextToken": {}, "MaxResults": {"type": "integer"}, "StartTimeFilter": {"shape": "Sag"}, "StatusFilter": {}}}, "output": {"type": "structure", "members": {"History": {"type": "list", "member": {"type": "structure", "members": {"ExecutionId": {}, "StartTime": {"type": "timestamp"}, "EndTime": {"type": "timestamp"}, "Status": {}, "ErrorMessage": {}, "ErrorCode": {}, "DataSourceErrorCode": {}, "Metrics": {"type": "structure", "members": {"DocumentsAdded": {}, "DocumentsModified": {}, "DocumentsDeleted": {}, "DocumentsFailed": {}, "DocumentsScanned": {}}}}}}, "NextToken": {}}}}, "ListDataSources": {"input": {"type": "structure", "required": ["IndexId"], "members": {"IndexId": {}, "NextToken": {}, "MaxResults": {"type": "integer"}}}, "output": {"type": "structure", "members": {"SummaryItems": {"type": "list", "member": {"type": "structure", "members": {"Name": {}, "Id": {}, "Type": {}, "CreatedAt": {"type": "timestamp"}, "UpdatedAt": {"type": "timestamp"}, "Status": {}, "LanguageCode": {}}}}, "NextToken": {}}}}, "ListEntityPersonas": {"input": {"type": "structure", "required": ["Id", "IndexId"], "members": {"Id": {}, "IndexId": {}, "NextToken": {}, "MaxResults": {"type": "integer"}}}, "output": {"type": "structure", "members": {"SummaryItems": {"type": "list", "member": {"type": "structure", "members": {"EntityId": {}, "Persona": {}, "CreatedAt": {"type": "timestamp"}, "UpdatedAt": {"type": "timestamp"}}}}, "NextToken": {}}}}, "ListExperienceEntities": {"input": {"type": "structure", "required": ["Id", "IndexId"], "members": {"Id": {}, "IndexId": {}, "NextToken": {}}}, "output": {"type": "structure", "members": {"SummaryItems": {"type": "list", "member": {"type": "structure", "members": {"EntityId": {}, "EntityType": {}, "DisplayData": {"type": "structure", "members": {"UserName": {"shape": "Sbc"}, "GroupName": {"shape": "Sbc"}, "IdentifiedUserName": {"shape": "Sbc"}, "FirstName": {"shape": "Sbc"}, "LastName": {"shape": "Sbc"}}}}}}, "NextToken": {}}}}, "ListExperiences": {"input": {"type": "structure", "required": ["IndexId"], "members": {"IndexId": {}, "NextToken": {}, "MaxResults": {"type": "integer"}}}, "output": {"type": "structure", "members": {"SummaryItems": {"type": "list", "member": {"type": "structure", "members": {"Name": {}, "Id": {}, "CreatedAt": {"type": "timestamp"}, "Status": {}, "Endpoints": {"shape": "S8e"}}}}, "NextToken": {}}}}, "ListFaqs": {"input": {"type": "structure", "required": ["IndexId"], "members": {"IndexId": {}, "NextToken": {}, "MaxResults": {"type": "integer"}}}, "output": {"type": "structure", "members": {"NextToken": {}, "FaqSummaryItems": {"type": "list", "member": {"type": "structure", "members": {"Id": {}, "Name": {}, "Status": {}, "CreatedAt": {"type": "timestamp"}, "UpdatedAt": {"type": "timestamp"}, "FileFormat": {}, "LanguageCode": {}}}}}}}, "ListGroupsOlderThanOrderingId": {"input": {"type": "structure", "required": ["IndexId", "OrderingId"], "members": {"IndexId": {}, "DataSourceId": {}, "OrderingId": {"type": "long"}, "NextToken": {}, "MaxResults": {"type": "integer"}}}, "output": {"type": "structure", "members": {"GroupsSummaries": {"type": "list", "member": {"type": "structure", "members": {"GroupId": {}, "OrderingId": {"type": "long"}}}}, "NextToken": {}}}}, "ListIndices": {"input": {"type": "structure", "members": {"NextToken": {}, "MaxResults": {"type": "integer"}}}, "output": {"type": "structure", "members": {"IndexConfigurationSummaryItems": {"type": "list", "member": {"type": "structure", "required": ["CreatedAt", "UpdatedAt", "Status"], "members": {"Name": {}, "Id": {}, "Edition": {}, "CreatedAt": {"type": "timestamp"}, "UpdatedAt": {"type": "timestamp"}, "Status": {}}}}, "NextToken": {}}}}, "ListQuerySuggestionsBlockLists": {"input": {"type": "structure", "required": ["IndexId"], "members": {"IndexId": {}, "NextToken": {}, "MaxResults": {"type": "integer"}}}, "output": {"type": "structure", "members": {"BlockListSummaryItems": {"type": "list", "member": {"type": "structure", "members": {"Id": {}, "Name": {}, "Status": {}, "CreatedAt": {"type": "timestamp"}, "UpdatedAt": {"type": "timestamp"}, "ItemCount": {"type": "integer"}}}}, "NextToken": {}}}}, "ListTagsForResource": {"input": {"type": "structure", "required": ["ResourceARN"], "members": {"ResourceARN": {}}}, "output": {"type": "structure", "members": {"Tags": {"shape": "S6l"}}}}, "ListThesauri": {"input": {"type": "structure", "required": ["IndexId"], "members": {"IndexId": {}, "NextToken": {}, "MaxResults": {"type": "integer"}}}, "output": {"type": "structure", "members": {"NextToken": {}, "ThesaurusSummaryItems": {"type": "list", "member": {"type": "structure", "members": {"Id": {}, "Name": {}, "Status": {}, "CreatedAt": {"type": "timestamp"}, "UpdatedAt": {"type": "timestamp"}}}}}}}, "PutPrincipalMapping": {"input": {"type": "structure", "required": ["IndexId", "GroupId", "GroupMembers"], "members": {"IndexId": {}, "DataSourceId": {}, "GroupId": {}, "GroupMembers": {"type": "structure", "members": {"MemberGroups": {"type": "list", "member": {"type": "structure", "required": ["GroupId"], "members": {"GroupId": {}, "DataSourceId": {}}}}, "MemberUsers": {"type": "list", "member": {"type": "structure", "required": ["UserId"], "members": {"UserId": {}}}}, "S3PathforGroupMembers": {"shape": "S1g"}}}, "OrderingId": {"type": "long"}, "RoleArn": {}}}}, "Query": {"input": {"type": "structure", "required": ["IndexId"], "members": {"IndexId": {}, "QueryText": {}, "AttributeFilter": {"shape": "Scj"}, "Facets": {"shape": "Scl"}, "RequestedDocumentAttributes": {"type": "list", "member": {}}, "QueryResultTypeFilter": {}, "DocumentRelevanceOverrideConfigurations": {"type": "list", "member": {"type": "structure", "required": ["Name", "Relevance"], "members": {"Name": {}, "Relevance": {"shape": "S8t"}}}}, "PageNumber": {"type": "integer"}, "PageSize": {"type": "integer"}, "SortingConfiguration": {"type": "structure", "required": ["DocumentAttributeKey", "SortOrder"], "members": {"DocumentAttributeKey": {}, "SortOrder": {}}}, "UserContext": {"type": "structure", "members": {"Token": {}, "UserId": {}, "Groups": {"type": "list", "member": {}}, "DataSourceGroups": {"type": "list", "member": {"type": "structure", "required": ["GroupId", "DataSourceId"], "members": {"GroupId": {}, "DataSourceId": {}}}}}}, "VisitorId": {}, "SpellCorrectionConfiguration": {"type": "structure", "required": ["IncludeQuerySpellCheckSuggestions"], "members": {"IncludeQuerySpellCheckSuggestions": {"type": "boolean"}}}}}, "output": {"type": "structure", "members": {"QueryId": {}, "ResultItems": {"type": "list", "member": {"type": "structure", "members": {"Id": {}, "Type": {}, "Format": {}, "AdditionalAttributes": {"type": "list", "member": {"type": "structure", "required": ["Key", "ValueType", "Value"], "members": {"Key": {}, "ValueType": {}, "Value": {"type": "structure", "members": {"TextWithHighlightsValue": {"shape": "Sda"}}}}}}, "DocumentId": {}, "DocumentTitle": {"shape": "Sda"}, "DocumentExcerpt": {"shape": "Sda"}, "DocumentURI": {}, "DocumentAttributes": {"shape": "Sv"}, "ScoreAttributes": {"type": "structure", "members": {"ScoreConfidence": {}}}, "FeedbackToken": {}, "TableExcerpt": {"type": "structure", "members": {"Rows": {"type": "list", "member": {"type": "structure", "members": {"Cells": {"type": "list", "member": {"type": "structure", "members": {"Value": {}, "TopAnswer": {"type": "boolean"}, "Highlighted": {"type": "boolean"}, "Header": {"type": "boolean"}}}}}}}, "TotalNumberOfRows": {"type": "integer"}}}}}}, "FacetResults": {"shape": "Sdm"}, "TotalNumberOfResults": {"type": "integer"}, "Warnings": {"type": "list", "member": {"type": "structure", "members": {"Message": {}, "Code": {}}}}, "SpellCorrectedQueries": {"type": "list", "member": {"type": "structure", "members": {"SuggestedQueryText": {}, "Corrections": {"type": "list", "member": {"type": "structure", "members": {"BeginOffset": {"type": "integer"}, "EndOffset": {"type": "integer"}, "Term": {}, "CorrectedTerm": {}}}}}}}}}}, "StartDataSourceSyncJob": {"input": {"type": "structure", "required": ["Id", "IndexId"], "members": {"Id": {}, "IndexId": {}}}, "output": {"type": "structure", "members": {"ExecutionId": {}}}}, "StopDataSourceSyncJob": {"input": {"type": "structure", "required": ["Id", "IndexId"], "members": {"Id": {}, "IndexId": {}}}}, "SubmitFeedback": {"input": {"type": "structure", "required": ["IndexId", "QueryId"], "members": {"IndexId": {}, "QueryId": {}, "ClickFeedbackItems": {"type": "list", "member": {"type": "structure", "required": ["ResultId", "ClickTime"], "members": {"ResultId": {}, "ClickTime": {"type": "timestamp"}}}}, "RelevanceFeedbackItems": {"type": "list", "member": {"type": "structure", "required": ["ResultId", "RelevanceValue"], "members": {"ResultId": {}, "RelevanceValue": {}}}}}}}, "TagResource": {"input": {"type": "structure", "required": ["ResourceARN", "Tags"], "members": {"ResourceARN": {}, "Tags": {"shape": "S6l"}}}, "output": {"type": "structure", "members": {}}}, "UntagResource": {"input": {"type": "structure", "required": ["ResourceARN", "TagKeys"], "members": {"ResourceARN": {}, "TagKeys": {"type": "list", "member": {}}}}, "output": {"type": "structure", "members": {}}}, "UpdateAccessControlConfiguration": {"input": {"type": "structure", "required": ["IndexId", "Id"], "members": {"IndexId": {}, "Id": {}, "Name": {}, "Description": {}, "AccessControlList": {"shape": "S1j"}, "HierarchicalAccessControlList": {"shape": "S1o"}}}, "output": {"type": "structure", "members": {}}}, "UpdateDataSource": {"input": {"type": "structure", "required": ["Id", "IndexId"], "members": {"Id": {}, "Name": {}, "IndexId": {}, "Configuration": {"shape": "S2d"}, "VpcConfiguration": {"shape": "S2o"}, "Description": {}, "Schedule": {}, "RoleArn": {}, "LanguageCode": {}, "CustomDocumentEnrichmentConfiguration": {"shape": "S1s"}}}}, "UpdateExperience": {"input": {"type": "structure", "required": ["Id", "IndexId"], "members": {"Id": {}, "Name": {}, "IndexId": {}, "RoleArn": {}, "Configuration": {"shape": "S6t"}, "Description": {}}}}, "UpdateIndex": {"input": {"type": "structure", "required": ["Id"], "members": {"Id": {}, "Name": {}, "RoleArn": {}, "Description": {}, "DocumentMetadataConfigurationUpdates": {"shape": "S8p"}, "CapacityUnits": {"shape": "S97"}, "UserTokenConfigurations": {"shape": "S7a"}, "UserContextPolicy": {}, "UserGroupResolutionConfiguration": {"shape": "S7k"}}}}, "UpdateQuerySuggestionsBlockList": {"input": {"type": "structure", "required": ["IndexId", "Id"], "members": {"IndexId": {}, "Id": {}, "Name": {}, "Description": {}, "SourceS3Path": {"shape": "S1g"}, "RoleArn": {}}}}, "UpdateQuerySuggestionsConfig": {"input": {"type": "structure", "required": ["IndexId"], "members": {"IndexId": {}, "Mode": {}, "QueryLogLookBackWindowInDays": {"type": "integer"}, "IncludeQueriesWithoutUserInformation": {"type": "boolean"}, "MinimumNumberOfQueryingUsers": {"type": "integer"}, "MinimumQueryCount": {"type": "integer"}}}}, "UpdateThesaurus": {"input": {"type": "structure", "required": ["Id", "IndexId"], "members": {"Id": {}, "Name": {}, "IndexId": {}, "Description": {}, "RoleArn": {}, "SourceS3Path": {"shape": "S1g"}}}}}, "shapes": {"S5": {"type": "structure", "required": ["EntityId", "EntityType"], "members": {"EntityId": {}, "EntityType": {}}}, "Sa": {"type": "structure", "members": {"EntityId": {}, "ErrorMessage": {}}}, "Sh": {"type": "list", "member": {"shape": "Sa"}}, "Sv": {"type": "list", "member": {"shape": "Sw"}}, "Sw": {"type": "structure", "required": ["Key", "Value"], "members": {"Key": {}, "Value": {"shape": "Sy"}}}, "Sy": {"type": "structure", "members": {"StringValue": {}, "StringListValue": {"type": "list", "member": {}}, "LongValue": {"type": "long"}, "DateValue": {"type": "timestamp"}}}, "S1g": {"type": "structure", "required": ["Bucket", "Key"], "members": {"Bucket": {}, "Key": {}}}, "S1j": {"type": "list", "member": {"type": "structure", "required": ["Name", "Type", "Access"], "members": {"Name": {}, "Type": {}, "Access": {}, "DataSourceId": {}}}}, "S1o": {"type": "list", "member": {"type": "structure", "required": ["PrincipalList"], "members": {"PrincipalList": {"shape": "S1j"}}}}, "S1s": {"type": "structure", "members": {"InlineConfigurations": {"type": "list", "member": {"type": "structure", "members": {"Condition": {"shape": "S1v"}, "Target": {"type": "structure", "members": {"TargetDocumentAttributeKey": {}, "TargetDocumentAttributeValueDeletion": {"type": "boolean"}, "TargetDocumentAttributeValue": {"shape": "Sy"}}}, "DocumentContentDeletion": {"type": "boolean"}}}}, "PreExtractionHookConfiguration": {"shape": "S1z"}, "PostExtractionHookConfiguration": {"shape": "S1z"}, "RoleArn": {}}}, "S1v": {"type": "structure", "required": ["ConditionDocumentAttributeKey", "Operator"], "members": {"ConditionDocumentAttributeKey": {}, "Operator": {}, "ConditionOnValue": {"shape": "Sy"}}}, "S1z": {"type": "structure", "required": ["LambdaArn", "S3Bucket"], "members": {"InvocationCondition": {"shape": "S1v"}, "LambdaArn": {}, "S3Bucket": {}}}, "S2d": {"type": "structure", "members": {"S3Configuration": {"type": "structure", "required": ["BucketName"], "members": {"BucketName": {}, "InclusionPrefixes": {"shape": "S2f"}, "InclusionPatterns": {"shape": "S2f"}, "ExclusionPatterns": {"shape": "S2f"}, "DocumentsMetadataConfiguration": {"type": "structure", "members": {"S3Prefix": {}}}, "AccessControlListConfiguration": {"type": "structure", "members": {"KeyPath": {}}}}}, "SharePointConfiguration": {"type": "structure", "required": ["SharePointVersion", "Urls", "SecretArn"], "members": {"SharePointVersion": {}, "Urls": {"type": "list", "member": {}}, "SecretArn": {}, "CrawlAttachments": {"type": "boolean"}, "UseChangeLog": {"type": "boolean"}, "InclusionPatterns": {"shape": "S2f"}, "ExclusionPatterns": {"shape": "S2f"}, "VpcConfiguration": {"shape": "S2o"}, "FieldMappings": {"shape": "S2t"}, "DocumentTitleFieldName": {}, "DisableLocalGroups": {"type": "boolean"}, "SslCertificateS3Path": {"shape": "S1g"}, "AuthenticationType": {}, "ProxyConfiguration": {"shape": "S2z"}}}, "DatabaseConfiguration": {"type": "structure", "required": ["DatabaseEngineType", "ConnectionConfiguration", "ColumnConfiguration"], "members": {"DatabaseEngineType": {}, "ConnectionConfiguration": {"type": "structure", "required": ["DatabaseHost", "DatabasePort", "DatabaseName", "TableName", "SecretArn"], "members": {"DatabaseHost": {}, "DatabasePort": {"type": "integer"}, "DatabaseName": {}, "TableName": {}, "SecretArn": {}}}, "VpcConfiguration": {"shape": "S2o"}, "ColumnConfiguration": {"type": "structure", "required": ["DocumentIdColumnName", "DocumentDataColumnName", "ChangeDetectingColumns"], "members": {"DocumentIdColumnName": {}, "DocumentDataColumnName": {}, "DocumentTitleColumnName": {}, "FieldMappings": {"shape": "S2t"}, "ChangeDetectingColumns": {"type": "list", "member": {}}}}, "AclConfiguration": {"type": "structure", "required": ["AllowedGroupsColumnName"], "members": {"AllowedGroupsColumnName": {}}}, "SqlConfiguration": {"type": "structure", "members": {"QueryIdentifiersEnclosingOption": {}}}}}, "SalesforceConfiguration": {"type": "structure", "required": ["ServerUrl", "SecretArn"], "members": {"ServerUrl": {}, "SecretArn": {}, "StandardObjectConfigurations": {"type": "list", "member": {"type": "structure", "required": ["Name", "DocumentDataFieldName"], "members": {"Name": {}, "DocumentDataFieldName": {}, "DocumentTitleFieldName": {}, "FieldMappings": {"shape": "S2t"}}}}, "KnowledgeArticleConfiguration": {"type": "structure", "required": ["IncludedStates"], "members": {"IncludedStates": {"type": "list", "member": {}}, "StandardKnowledgeArticleTypeConfiguration": {"type": "structure", "required": ["DocumentDataFieldName"], "members": {"DocumentDataFieldName": {}, "DocumentTitleFieldName": {}, "FieldMappings": {"shape": "S2t"}}}, "CustomKnowledgeArticleTypeConfigurations": {"type": "list", "member": {"type": "structure", "required": ["Name", "DocumentDataFieldName"], "members": {"Name": {}, "DocumentDataFieldName": {}, "DocumentTitleFieldName": {}, "FieldMappings": {"shape": "S2t"}}}}}}, "ChatterFeedConfiguration": {"type": "structure", "required": ["DocumentDataFieldName"], "members": {"DocumentDataFieldName": {}, "DocumentTitleFieldName": {}, "FieldMappings": {"shape": "S2t"}, "IncludeFilterTypes": {"type": "list", "member": {}}}}, "CrawlAttachments": {"type": "boolean"}, "StandardObjectAttachmentConfiguration": {"type": "structure", "members": {"DocumentTitleFieldName": {}, "FieldMappings": {"shape": "S2t"}}}, "IncludeAttachmentFilePatterns": {"shape": "S2f"}, "ExcludeAttachmentFilePatterns": {"shape": "S2f"}}}, "OneDriveConfiguration": {"type": "structure", "required": ["TenantDomain", "SecretArn", "OneDriveUsers"], "members": {"TenantDomain": {}, "SecretArn": {}, "OneDriveUsers": {"type": "structure", "members": {"OneDriveUserList": {"type": "list", "member": {}}, "OneDriveUserS3Path": {"shape": "S1g"}}}, "InclusionPatterns": {"shape": "S2f"}, "ExclusionPatterns": {"shape": "S2f"}, "FieldMappings": {"shape": "S2t"}, "DisableLocalGroups": {"type": "boolean"}}}, "ServiceNowConfiguration": {"type": "structure", "required": ["HostUrl", "SecretArn", "ServiceNowBuildVersion"], "members": {"HostUrl": {}, "SecretArn": {}, "ServiceNowBuildVersion": {}, "KnowledgeArticleConfiguration": {"type": "structure", "required": ["DocumentDataFieldName"], "members": {"CrawlAttachments": {"type": "boolean"}, "IncludeAttachmentFilePatterns": {"shape": "S2f"}, "ExcludeAttachmentFilePatterns": {"shape": "S2f"}, "DocumentDataFieldName": {}, "DocumentTitleFieldName": {}, "FieldMappings": {"shape": "S2t"}, "FilterQuery": {}}}, "ServiceCatalogConfiguration": {"type": "structure", "required": ["DocumentDataFieldName"], "members": {"CrawlAttachments": {"type": "boolean"}, "IncludeAttachmentFilePatterns": {"shape": "S2f"}, "ExcludeAttachmentFilePatterns": {"shape": "S2f"}, "DocumentDataFieldName": {}, "DocumentTitleFieldName": {}, "FieldMappings": {"shape": "S2t"}}}, "AuthenticationType": {}}}, "ConfluenceConfiguration": {"type": "structure", "required": ["ServerUrl", "SecretArn", "Version"], "members": {"ServerUrl": {}, "SecretArn": {}, "Version": {}, "SpaceConfiguration": {"type": "structure", "members": {"CrawlPersonalSpaces": {"type": "boolean"}, "CrawlArchivedSpaces": {"type": "boolean"}, "IncludeSpaces": {"shape": "S49"}, "ExcludeSpaces": {"shape": "S49"}, "SpaceFieldMappings": {"type": "list", "member": {"type": "structure", "members": {"DataSourceFieldName": {}, "DateFieldFormat": {}, "IndexFieldName": {}}}}}}, "PageConfiguration": {"type": "structure", "members": {"PageFieldMappings": {"type": "list", "member": {"type": "structure", "members": {"DataSourceFieldName": {}, "DateFieldFormat": {}, "IndexFieldName": {}}}}}}, "BlogConfiguration": {"type": "structure", "members": {"BlogFieldMappings": {"type": "list", "member": {"type": "structure", "members": {"DataSourceFieldName": {}, "DateFieldFormat": {}, "IndexFieldName": {}}}}}}, "AttachmentConfiguration": {"type": "structure", "members": {"CrawlAttachments": {"type": "boolean"}, "AttachmentFieldMappings": {"type": "list", "member": {"type": "structure", "members": {"DataSourceFieldName": {}, "DateFieldFormat": {}, "IndexFieldName": {}}}}}}, "VpcConfiguration": {"shape": "S2o"}, "InclusionPatterns": {"shape": "S2f"}, "ExclusionPatterns": {"shape": "S2f"}, "ProxyConfiguration": {"shape": "S2z"}, "AuthenticationType": {}}}, "GoogleDriveConfiguration": {"type": "structure", "required": ["SecretArn"], "members": {"SecretArn": {}, "InclusionPatterns": {"shape": "S2f"}, "ExclusionPatterns": {"shape": "S2f"}, "FieldMappings": {"shape": "S2t"}, "ExcludeMimeTypes": {"type": "list", "member": {}}, "ExcludeUserAccounts": {"type": "list", "member": {}}, "ExcludeSharedDrives": {"type": "list", "member": {}}}}, "WebCrawlerConfiguration": {"type": "structure", "required": ["Urls"], "members": {"Urls": {"type": "structure", "members": {"SeedUrlConfiguration": {"type": "structure", "required": ["SeedUrls"], "members": {"SeedUrls": {"type": "list", "member": {}}, "WebCrawlerMode": {}}}, "SiteMapsConfiguration": {"type": "structure", "required": ["SiteMaps"], "members": {"SiteMaps": {"type": "list", "member": {}}}}}}, "CrawlDepth": {"type": "integer"}, "MaxLinksPerPage": {"type": "integer"}, "MaxContentSizePerPageInMegaBytes": {"type": "float"}, "MaxUrlsPerMinuteCrawlRate": {"type": "integer"}, "UrlInclusionPatterns": {"shape": "S2f"}, "UrlExclusionPatterns": {"shape": "S2f"}, "ProxyConfiguration": {"shape": "S2z"}, "AuthenticationConfiguration": {"type": "structure", "members": {"BasicAuthentication": {"type": "list", "member": {"type": "structure", "required": ["Host", "Port", "Credentials"], "members": {"Host": {}, "Port": {"type": "integer"}, "Credentials": {}}}}}}}}, "WorkDocsConfiguration": {"type": "structure", "required": ["OrganizationId"], "members": {"OrganizationId": {}, "CrawlComments": {"type": "boolean"}, "UseChangeLog": {"type": "boolean"}, "InclusionPatterns": {"shape": "S2f"}, "ExclusionPatterns": {"shape": "S2f"}, "FieldMappings": {"shape": "S2t"}}}, "FsxConfiguration": {"type": "structure", "required": ["FileSystemId", "FileSystemType", "VpcConfiguration"], "members": {"FileSystemId": {}, "FileSystemType": {}, "VpcConfiguration": {"shape": "S2o"}, "SecretArn": {}, "InclusionPatterns": {"shape": "S2f"}, "ExclusionPatterns": {"shape": "S2f"}, "FieldMappings": {"shape": "S2t"}}}, "SlackConfiguration": {"type": "structure", "required": ["TeamId", "SecretArn", "SlackEntityList", "SinceCrawlDate"], "members": {"TeamId": {}, "SecretArn": {}, "VpcConfiguration": {"shape": "S2o"}, "SlackEntityList": {"type": "list", "member": {}}, "UseChangeLog": {"type": "boolean"}, "CrawlBotMessage": {"type": "boolean"}, "ExcludeArchived": {"type": "boolean"}, "SinceCrawlDate": {}, "LookBackPeriod": {"type": "integer"}, "PrivateChannelFilter": {"type": "list", "member": {}}, "PublicChannelFilter": {"type": "list", "member": {}}, "InclusionPatterns": {"shape": "S2f"}, "ExclusionPatterns": {"shape": "S2f"}, "FieldMappings": {"shape": "S2t"}}}, "BoxConfiguration": {"type": "structure", "required": ["EnterpriseId", "SecretArn"], "members": {"EnterpriseId": {}, "SecretArn": {}, "UseChangeLog": {"type": "boolean"}, "CrawlComments": {"type": "boolean"}, "CrawlTasks": {"type": "boolean"}, "CrawlWebLinks": {"type": "boolean"}, "FileFieldMappings": {"shape": "S2t"}, "TaskFieldMappings": {"shape": "S2t"}, "CommentFieldMappings": {"shape": "S2t"}, "WebLinkFieldMappings": {"shape": "S2t"}, "InclusionPatterns": {"shape": "S2f"}, "ExclusionPatterns": {"shape": "S2f"}, "VpcConfiguration": {"shape": "S2o"}}}, "QuipConfiguration": {"type": "structure", "required": ["Domain", "SecretArn"], "members": {"Domain": {}, "SecretArn": {}, "CrawlFileComments": {"type": "boolean"}, "CrawlChatRooms": {"type": "boolean"}, "CrawlAttachments": {"type": "boolean"}, "FolderIds": {"type": "list", "member": {}}, "ThreadFieldMappings": {"shape": "S2t"}, "MessageFieldMappings": {"shape": "S2t"}, "AttachmentFieldMappings": {"shape": "S2t"}, "InclusionPatterns": {"shape": "S2f"}, "ExclusionPatterns": {"shape": "S2f"}, "VpcConfiguration": {"shape": "S2o"}}}, "JiraConfiguration": {"type": "structure", "required": ["JiraAccountUrl", "SecretArn"], "members": {"JiraAccountUrl": {}, "SecretArn": {}, "UseChangeLog": {"type": "boolean"}, "Project": {"type": "list", "member": {}}, "IssueType": {"type": "list", "member": {}}, "Status": {"type": "list", "member": {}}, "IssueSubEntityFilter": {"type": "list", "member": {}}, "AttachmentFieldMappings": {"shape": "S2t"}, "CommentFieldMappings": {"shape": "S2t"}, "IssueFieldMappings": {"shape": "S2t"}, "ProjectFieldMappings": {"shape": "S2t"}, "WorkLogFieldMappings": {"shape": "S2t"}, "InclusionPatterns": {"shape": "S2f"}, "ExclusionPatterns": {"shape": "S2f"}, "VpcConfiguration": {"shape": "S2o"}}}, "GitHubConfiguration": {"type": "structure", "required": ["SecretArn"], "members": {"SaaSConfiguration": {"type": "structure", "required": ["OrganizationName", "HostUrl"], "members": {"OrganizationName": {}, "HostUrl": {}}}, "OnPremiseConfiguration": {"type": "structure", "required": ["HostUrl", "OrganizationName", "SslCertificateS3Path"], "members": {"HostUrl": {}, "OrganizationName": {}, "SslCertificateS3Path": {"shape": "S1g"}}}, "Type": {}, "SecretArn": {}, "UseChangeLog": {"type": "boolean"}, "GitHubDocumentCrawlProperties": {"type": "structure", "members": {"CrawlRepositoryDocuments": {"type": "boolean"}, "CrawlIssue": {"type": "boolean"}, "CrawlIssueComment": {"type": "boolean"}, "CrawlIssueCommentAttachment": {"type": "boolean"}, "CrawlPullRequest": {"type": "boolean"}, "CrawlPullRequestComment": {"type": "boolean"}, "CrawlPullRequestCommentAttachment": {"type": "boolean"}}}, "RepositoryFilter": {"type": "list", "member": {}}, "InclusionFolderNamePatterns": {"shape": "S6c"}, "InclusionFileTypePatterns": {"shape": "S6c"}, "InclusionFileNamePatterns": {"shape": "S6c"}, "ExclusionFolderNamePatterns": {"shape": "S6c"}, "ExclusionFileTypePatterns": {"shape": "S6c"}, "ExclusionFileNamePatterns": {"shape": "S6c"}, "VpcConfiguration": {"shape": "S2o"}, "GitHubRepositoryConfigurationFieldMappings": {"shape": "S2t"}, "GitHubCommitConfigurationFieldMappings": {"shape": "S2t"}, "GitHubIssueDocumentConfigurationFieldMappings": {"shape": "S2t"}, "GitHubIssueCommentConfigurationFieldMappings": {"shape": "S2t"}, "GitHubIssueAttachmentConfigurationFieldMappings": {"shape": "S2t"}, "GitHubPullRequestCommentConfigurationFieldMappings": {"shape": "S2t"}, "GitHubPullRequestDocumentConfigurationFieldMappings": {"shape": "S2t"}, "GitHubPullRequestDocumentAttachmentConfigurationFieldMappings": {"shape": "S2t"}}}, "AlfrescoConfiguration": {"type": "structure", "required": ["SiteUrl", "SiteId", "SecretArn", "SslCertificateS3Path"], "members": {"SiteUrl": {}, "SiteId": {}, "SecretArn": {}, "SslCertificateS3Path": {"shape": "S1g"}, "CrawlSystemFolders": {"type": "boolean"}, "CrawlComments": {"type": "boolean"}, "EntityFilter": {"type": "list", "member": {}}, "DocumentLibraryFieldMappings": {"shape": "S2t"}, "BlogFieldMappings": {"shape": "S2t"}, "WikiFieldMappings": {"shape": "S2t"}, "InclusionPatterns": {"shape": "S2f"}, "ExclusionPatterns": {"shape": "S2f"}, "VpcConfiguration": {"shape": "S2o"}}}, "TemplateConfiguration": {"type": "structure", "members": {"Template": {"type": "structure", "members": {}, "document": true}}}}}, "S2f": {"type": "list", "member": {}}, "S2o": {"type": "structure", "required": ["SubnetIds", "SecurityGroupIds"], "members": {"SubnetIds": {"type": "list", "member": {}}, "SecurityGroupIds": {"type": "list", "member": {}}}}, "S2t": {"type": "list", "member": {"type": "structure", "required": ["DataSourceFieldName", "IndexFieldName"], "members": {"DataSourceFieldName": {}, "DateFieldFormat": {}, "IndexFieldName": {}}}}, "S2z": {"type": "structure", "required": ["Host", "Port"], "members": {"Host": {}, "Port": {"type": "integer"}, "Credentials": {}}}, "S49": {"type": "list", "member": {}}, "S6c": {"type": "list", "member": {}}, "S6l": {"type": "list", "member": {"type": "structure", "required": ["Key", "Value"], "members": {"Key": {}, "Value": {}}}}, "S6t": {"type": "structure", "members": {"ContentSourceConfiguration": {"type": "structure", "members": {"DataSourceIds": {"type": "list", "member": {}}, "FaqIds": {"type": "list", "member": {}}, "DirectPutContent": {"type": "boolean"}}}, "UserIdentityConfiguration": {"type": "structure", "members": {"IdentityAttributeName": {}}}}}, "S78": {"type": "structure", "members": {"KmsKeyId": {"type": "string", "sensitive": true}}}, "S7a": {"type": "list", "member": {"type": "structure", "members": {"JwtTokenTypeConfiguration": {"type": "structure", "required": ["KeyLocation"], "members": {"KeyLocation": {}, "URL": {}, "SecretManagerArn": {}, "UserNameAttributeField": {}, "GroupAttributeField": {}, "Issuer": {}, "ClaimRegex": {}}}, "JsonTokenTypeConfiguration": {"type": "structure", "required": ["UserNameAttributeField", "GroupAttributeField"], "members": {"UserNameAttributeField": {}, "GroupAttributeField": {}}}}}}, "S7k": {"type": "structure", "required": ["UserGroupResolutionMode"], "members": {"UserGroupResolutionMode": {}}}, "S8e": {"type": "list", "member": {"type": "structure", "members": {"EndpointType": {}, "Endpoint": {}}}}, "S8p": {"type": "list", "member": {"type": "structure", "required": ["Name", "Type"], "members": {"Name": {}, "Type": {}, "Relevance": {"shape": "S8t"}, "Search": {"type": "structure", "members": {"Facetable": {"type": "boolean"}, "Searchable": {"type": "boolean"}, "Displayable": {"type": "boolean"}, "Sortable": {"type": "boolean"}}}}}}, "S8t": {"type": "structure", "members": {"Freshness": {"type": "boolean"}, "Importance": {"type": "integer"}, "Duration": {}, "RankOrder": {}, "ValueImportanceMap": {"type": "map", "key": {}, "value": {"type": "integer"}}}}, "S97": {"type": "structure", "required": ["StorageCapacityUnits", "QueryCapacityUnits"], "members": {"StorageCapacityUnits": {"type": "integer"}, "QueryCapacityUnits": {"type": "integer"}}}, "Sag": {"type": "structure", "members": {"StartTime": {"type": "timestamp"}, "EndTime": {"type": "timestamp"}}}, "Sbc": {"type": "string", "sensitive": true}, "Scj": {"type": "structure", "members": {"AndAllFilters": {"shape": "Sck"}, "OrAllFilters": {"shape": "Sck"}, "NotFilter": {"shape": "Scj"}, "EqualsTo": {"shape": "Sw"}, "ContainsAll": {"shape": "Sw"}, "ContainsAny": {"shape": "Sw"}, "GreaterThan": {"shape": "Sw"}, "GreaterThanOrEquals": {"shape": "Sw"}, "LessThan": {"shape": "Sw"}, "LessThanOrEquals": {"shape": "Sw"}}}, "Sck": {"type": "list", "member": {"shape": "Scj"}}, "Scl": {"type": "list", "member": {"type": "structure", "members": {"DocumentAttributeKey": {}, "Facets": {"shape": "Scl"}, "MaxResults": {"type": "integer"}}}}, "Sda": {"type": "structure", "members": {"Text": {}, "Highlights": {"type": "list", "member": {"type": "structure", "required": ["BeginOffset", "EndOffset"], "members": {"BeginOffset": {"type": "integer"}, "EndOffset": {"type": "integer"}, "TopAnswer": {"type": "boolean"}, "Type": {}}}}}}, "Sdm": {"type": "list", "member": {"type": "structure", "members": {"DocumentAttributeKey": {}, "DocumentAttributeValueType": {}, "DocumentAttributeValueCountPairs": {"type": "list", "member": {"type": "structure", "members": {"DocumentAttributeValue": {"shape": "Sy"}, "Count": {"type": "integer"}, "FacetResults": {"shape": "Sdm"}}}}}}}}}