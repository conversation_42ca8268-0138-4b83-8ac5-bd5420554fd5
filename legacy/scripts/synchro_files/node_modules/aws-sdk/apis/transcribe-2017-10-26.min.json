{"version": "2.0", "metadata": {"apiVersion": "2017-10-26", "endpointPrefix": "transcribe", "jsonVersion": "1.1", "protocol": "json", "serviceFullName": "Amazon Transcribe Service", "serviceId": "Transcribe", "signatureVersion": "v4", "signingName": "transcribe", "targetPrefix": "Transcribe", "uid": "transcribe-2017-10-26"}, "operations": {"CreateCallAnalyticsCategory": {"input": {"type": "structure", "required": ["CategoryName", "Rules"], "members": {"CategoryName": {}, "Rules": {"shape": "S3"}, "InputType": {}}}, "output": {"type": "structure", "members": {"CategoryProperties": {"shape": "Sm"}}}}, "CreateLanguageModel": {"input": {"type": "structure", "required": ["LanguageCode", "BaseModelName", "ModelName", "InputDataConfig"], "members": {"LanguageCode": {}, "BaseModelName": {}, "ModelName": {}, "InputDataConfig": {"shape": "Ss"}, "Tags": {"shape": "Sv"}}}, "output": {"type": "structure", "members": {"LanguageCode": {}, "BaseModelName": {}, "ModelName": {}, "InputDataConfig": {"shape": "Ss"}, "ModelStatus": {}}}}, "CreateMedicalVocabulary": {"input": {"type": "structure", "required": ["VocabularyName", "LanguageCode", "VocabularyFileUri"], "members": {"VocabularyName": {}, "LanguageCode": {}, "VocabularyFileUri": {}, "Tags": {"shape": "Sv"}}}, "output": {"type": "structure", "members": {"VocabularyName": {}, "LanguageCode": {}, "VocabularyState": {}, "LastModifiedTime": {"type": "timestamp"}, "FailureReason": {}}}}, "CreateVocabulary": {"input": {"type": "structure", "required": ["VocabularyName", "LanguageCode"], "members": {"VocabularyName": {}, "LanguageCode": {}, "Phrases": {"shape": "S18"}, "VocabularyFileUri": {}, "Tags": {"shape": "Sv"}}}, "output": {"type": "structure", "members": {"VocabularyName": {}, "LanguageCode": {}, "VocabularyState": {}, "LastModifiedTime": {"type": "timestamp"}, "FailureReason": {}}}}, "CreateVocabularyFilter": {"input": {"type": "structure", "required": ["VocabularyFilterName", "LanguageCode"], "members": {"VocabularyFilterName": {}, "LanguageCode": {}, "Words": {"shape": "S1d"}, "VocabularyFilterFileUri": {}, "Tags": {"shape": "Sv"}}}, "output": {"type": "structure", "members": {"VocabularyFilterName": {}, "LanguageCode": {}, "LastModifiedTime": {"type": "timestamp"}}}}, "DeleteCallAnalyticsCategory": {"input": {"type": "structure", "required": ["CategoryName"], "members": {"CategoryName": {}}}, "output": {"type": "structure", "members": {}}}, "DeleteCallAnalyticsJob": {"input": {"type": "structure", "required": ["CallAnalyticsJobName"], "members": {"CallAnalyticsJobName": {}}}, "output": {"type": "structure", "members": {}}}, "DeleteLanguageModel": {"input": {"type": "structure", "required": ["ModelName"], "members": {"ModelName": {}}}}, "DeleteMedicalTranscriptionJob": {"input": {"type": "structure", "required": ["MedicalTranscriptionJobName"], "members": {"MedicalTranscriptionJobName": {}}}}, "DeleteMedicalVocabulary": {"input": {"type": "structure", "required": ["VocabularyName"], "members": {"VocabularyName": {}}}}, "DeleteTranscriptionJob": {"input": {"type": "structure", "required": ["TranscriptionJobName"], "members": {"TranscriptionJobName": {}}}}, "DeleteVocabulary": {"input": {"type": "structure", "required": ["VocabularyName"], "members": {"VocabularyName": {}}}}, "DeleteVocabularyFilter": {"input": {"type": "structure", "required": ["VocabularyFilterName"], "members": {"VocabularyFilterName": {}}}}, "DescribeLanguageModel": {"input": {"type": "structure", "required": ["ModelName"], "members": {"ModelName": {}}}, "output": {"type": "structure", "members": {"LanguageModel": {"shape": "S1u"}}}}, "GetCallAnalyticsCategory": {"input": {"type": "structure", "required": ["CategoryName"], "members": {"CategoryName": {}}}, "output": {"type": "structure", "members": {"CategoryProperties": {"shape": "Sm"}}}}, "GetCallAnalyticsJob": {"input": {"type": "structure", "required": ["CallAnalyticsJobName"], "members": {"CallAnalyticsJobName": {}}}, "output": {"type": "structure", "members": {"CallAnalyticsJob": {"shape": "S1z"}}}}, "GetMedicalTranscriptionJob": {"input": {"type": "structure", "required": ["MedicalTranscriptionJobName"], "members": {"MedicalTranscriptionJobName": {}}}, "output": {"type": "structure", "members": {"MedicalTranscriptionJob": {"shape": "S2l"}}}}, "GetMedicalVocabulary": {"input": {"type": "structure", "required": ["VocabularyName"], "members": {"VocabularyName": {}}}, "output": {"type": "structure", "members": {"VocabularyName": {}, "LanguageCode": {}, "VocabularyState": {}, "LastModifiedTime": {"type": "timestamp"}, "FailureReason": {}, "DownloadUri": {}}}}, "GetTranscriptionJob": {"input": {"type": "structure", "required": ["TranscriptionJobName"], "members": {"TranscriptionJobName": {}}}, "output": {"type": "structure", "members": {"TranscriptionJob": {"shape": "S2z"}}}}, "GetVocabulary": {"input": {"type": "structure", "required": ["VocabularyName"], "members": {"VocabularyName": {}}}, "output": {"type": "structure", "members": {"VocabularyName": {}, "LanguageCode": {}, "VocabularyState": {}, "LastModifiedTime": {"type": "timestamp"}, "FailureReason": {}, "DownloadUri": {}}}}, "GetVocabularyFilter": {"input": {"type": "structure", "required": ["VocabularyFilterName"], "members": {"VocabularyFilterName": {}}}, "output": {"type": "structure", "members": {"VocabularyFilterName": {}, "LanguageCode": {}, "LastModifiedTime": {"type": "timestamp"}, "DownloadUri": {}}}}, "ListCallAnalyticsCategories": {"input": {"type": "structure", "members": {"NextToken": {}, "MaxResults": {"type": "integer"}}}, "output": {"type": "structure", "members": {"NextToken": {}, "Categories": {"type": "list", "member": {"shape": "Sm"}}}}}, "ListCallAnalyticsJobs": {"input": {"type": "structure", "members": {"Status": {}, "JobNameContains": {}, "NextToken": {}, "MaxResults": {"type": "integer"}}}, "output": {"type": "structure", "members": {"Status": {}, "NextToken": {}, "CallAnalyticsJobSummaries": {"type": "list", "member": {"type": "structure", "members": {"CallAnalyticsJobName": {}, "CreationTime": {"type": "timestamp"}, "StartTime": {"type": "timestamp"}, "CompletionTime": {"type": "timestamp"}, "LanguageCode": {}, "CallAnalyticsJobStatus": {}, "FailureReason": {}}}}}}}, "ListLanguageModels": {"input": {"type": "structure", "members": {"StatusEquals": {}, "NameContains": {}, "NextToken": {}, "MaxResults": {"type": "integer"}}}, "output": {"type": "structure", "members": {"NextToken": {}, "Models": {"type": "list", "member": {"shape": "S1u"}}}}}, "ListMedicalTranscriptionJobs": {"input": {"type": "structure", "members": {"Status": {}, "JobNameContains": {}, "NextToken": {}, "MaxResults": {"type": "integer"}}}, "output": {"type": "structure", "members": {"Status": {}, "NextToken": {}, "MedicalTranscriptionJobSummaries": {"type": "list", "member": {"type": "structure", "members": {"MedicalTranscriptionJobName": {}, "CreationTime": {"type": "timestamp"}, "StartTime": {"type": "timestamp"}, "CompletionTime": {"type": "timestamp"}, "LanguageCode": {}, "TranscriptionJobStatus": {}, "FailureReason": {}, "OutputLocationType": {}, "Specialty": {}, "ContentIdentificationType": {}, "Type": {}}}}}}}, "ListMedicalVocabularies": {"input": {"type": "structure", "members": {"NextToken": {}, "MaxResults": {"type": "integer"}, "StateEquals": {}, "NameContains": {}}}, "output": {"type": "structure", "members": {"Status": {}, "NextToken": {}, "Vocabularies": {"shape": "S3y"}}}}, "ListTagsForResource": {"input": {"type": "structure", "required": ["ResourceArn"], "members": {"ResourceArn": {}}}, "output": {"type": "structure", "members": {"ResourceArn": {}, "Tags": {"shape": "Sv"}}}}, "ListTranscriptionJobs": {"input": {"type": "structure", "members": {"Status": {}, "JobNameContains": {}, "NextToken": {}, "MaxResults": {"type": "integer"}}}, "output": {"type": "structure", "members": {"Status": {}, "NextToken": {}, "TranscriptionJobSummaries": {"type": "list", "member": {"type": "structure", "members": {"TranscriptionJobName": {}, "CreationTime": {"type": "timestamp"}, "StartTime": {"type": "timestamp"}, "CompletionTime": {"type": "timestamp"}, "LanguageCode": {}, "TranscriptionJobStatus": {}, "FailureReason": {}, "OutputLocationType": {}, "ContentRedaction": {"shape": "S28"}, "ModelSettings": {"shape": "S31"}, "IdentifyLanguage": {"type": "boolean"}, "IdentifyMultipleLanguages": {"type": "boolean"}, "IdentifiedLanguageScore": {"type": "float"}, "LanguageCodes": {"shape": "S33"}}}}}}}, "ListVocabularies": {"input": {"type": "structure", "members": {"NextToken": {}, "MaxResults": {"type": "integer"}, "StateEquals": {}, "NameContains": {}}}, "output": {"type": "structure", "members": {"Status": {}, "NextToken": {}, "Vocabularies": {"shape": "S3y"}}}}, "ListVocabularyFilters": {"input": {"type": "structure", "members": {"NextToken": {}, "MaxResults": {"type": "integer"}, "NameContains": {}}}, "output": {"type": "structure", "members": {"NextToken": {}, "VocabularyFilters": {"type": "list", "member": {"type": "structure", "members": {"VocabularyFilterName": {}, "LanguageCode": {}, "LastModifiedTime": {"type": "timestamp"}}}}}}}, "StartCallAnalyticsJob": {"input": {"type": "structure", "required": ["CallAnalyticsJobName", "Media"], "members": {"CallAnalyticsJobName": {}, "Media": {"shape": "S23"}, "OutputLocation": {}, "OutputEncryptionKMSKeyId": {}, "DataAccessRoleArn": {}, "Settings": {"shape": "S26"}, "ChannelDefinitions": {"shape": "S2g"}}}, "output": {"type": "structure", "members": {"CallAnalyticsJob": {"shape": "S1z"}}}}, "StartMedicalTranscriptionJob": {"input": {"type": "structure", "required": ["MedicalTranscriptionJobName", "LanguageCode", "Media", "OutputBucketName", "Specialty", "Type"], "members": {"MedicalTranscriptionJobName": {}, "LanguageCode": {}, "MediaSampleRateHertz": {"type": "integer"}, "MediaFormat": {}, "Media": {"shape": "S23"}, "OutputBucketName": {}, "OutputKey": {}, "OutputEncryptionKMSKeyId": {}, "KMSEncryptionContext": {"shape": "S4j"}, "Settings": {"shape": "S2p"}, "ContentIdentificationType": {}, "Specialty": {}, "Type": {}, "Tags": {"shape": "Sv"}}}, "output": {"type": "structure", "members": {"MedicalTranscriptionJob": {"shape": "S2l"}}}}, "StartTranscriptionJob": {"input": {"type": "structure", "required": ["TranscriptionJobName", "Media"], "members": {"TranscriptionJobName": {}, "LanguageCode": {}, "MediaSampleRateHertz": {"type": "integer"}, "MediaFormat": {}, "Media": {"shape": "S23"}, "OutputBucketName": {}, "OutputKey": {}, "OutputEncryptionKMSKeyId": {}, "KMSEncryptionContext": {"shape": "S4j"}, "Settings": {"shape": "S30"}, "ModelSettings": {"shape": "S31"}, "JobExecutionSettings": {"shape": "S32"}, "ContentRedaction": {"shape": "S28"}, "IdentifyLanguage": {"type": "boolean"}, "IdentifyMultipleLanguages": {"type": "boolean"}, "LanguageOptions": {"shape": "S2d"}, "Subtitles": {"type": "structure", "members": {"Formats": {"shape": "S37"}, "OutputStartIndex": {"type": "integer"}}}, "Tags": {"shape": "Sv"}, "LanguageIdSettings": {"shape": "S2e"}}}, "output": {"type": "structure", "members": {"TranscriptionJob": {"shape": "S2z"}}}}, "TagResource": {"input": {"type": "structure", "required": ["ResourceArn", "Tags"], "members": {"ResourceArn": {}, "Tags": {"shape": "Sv"}}}, "output": {"type": "structure", "members": {}}}, "UntagResource": {"input": {"type": "structure", "required": ["ResourceArn", "TagKeys"], "members": {"ResourceArn": {}, "TagKeys": {"type": "list", "member": {}}}}, "output": {"type": "structure", "members": {}}}, "UpdateCallAnalyticsCategory": {"input": {"type": "structure", "required": ["CategoryName", "Rules"], "members": {"CategoryName": {}, "Rules": {"shape": "S3"}, "InputType": {}}}, "output": {"type": "structure", "members": {"CategoryProperties": {"shape": "Sm"}}}}, "UpdateMedicalVocabulary": {"input": {"type": "structure", "required": ["VocabularyName", "LanguageCode", "VocabularyFileUri"], "members": {"VocabularyName": {}, "LanguageCode": {}, "VocabularyFileUri": {}}}, "output": {"type": "structure", "members": {"VocabularyName": {}, "LanguageCode": {}, "LastModifiedTime": {"type": "timestamp"}, "VocabularyState": {}}}}, "UpdateVocabulary": {"input": {"type": "structure", "required": ["VocabularyName", "LanguageCode"], "members": {"VocabularyName": {}, "LanguageCode": {}, "Phrases": {"shape": "S18"}, "VocabularyFileUri": {}}}, "output": {"type": "structure", "members": {"VocabularyName": {}, "LanguageCode": {}, "LastModifiedTime": {"type": "timestamp"}, "VocabularyState": {}}}}, "UpdateVocabularyFilter": {"input": {"type": "structure", "required": ["VocabularyFilterName"], "members": {"VocabularyFilterName": {}, "Words": {"shape": "S1d"}, "VocabularyFilterFileUri": {}}}, "output": {"type": "structure", "members": {"VocabularyFilterName": {}, "LanguageCode": {}, "LastModifiedTime": {"type": "timestamp"}}}}}, "shapes": {"S3": {"type": "list", "member": {"type": "structure", "members": {"NonTalkTimeFilter": {"type": "structure", "members": {"Threshold": {"type": "long"}, "AbsoluteTimeRange": {"shape": "S7"}, "RelativeTimeRange": {"shape": "S8"}, "Negate": {"type": "boolean"}}}, "InterruptionFilter": {"type": "structure", "members": {"Threshold": {"type": "long"}, "ParticipantRole": {}, "AbsoluteTimeRange": {"shape": "S7"}, "RelativeTimeRange": {"shape": "S8"}, "Negate": {"type": "boolean"}}}, "TranscriptFilter": {"type": "structure", "required": ["TranscriptFilterType", "Targets"], "members": {"TranscriptFilterType": {}, "AbsoluteTimeRange": {"shape": "S7"}, "RelativeTimeRange": {"shape": "S8"}, "ParticipantRole": {}, "Negate": {"type": "boolean"}, "Targets": {"type": "list", "member": {}}}}, "SentimentFilter": {"type": "structure", "required": ["Sentiments"], "members": {"Sentiments": {"type": "list", "member": {}}, "AbsoluteTimeRange": {"shape": "S7"}, "RelativeTimeRange": {"shape": "S8"}, "ParticipantRole": {}, "Negate": {"type": "boolean"}}}}, "union": true}}, "S7": {"type": "structure", "members": {"StartTime": {"type": "long"}, "EndTime": {"type": "long"}, "First": {"type": "long"}, "Last": {"type": "long"}}}, "S8": {"type": "structure", "members": {"StartPercentage": {"type": "integer"}, "EndPercentage": {"type": "integer"}, "First": {"type": "integer"}, "Last": {"type": "integer"}}}, "Sm": {"type": "structure", "members": {"CategoryName": {}, "Rules": {"shape": "S3"}, "CreateTime": {"type": "timestamp"}, "LastUpdateTime": {"type": "timestamp"}, "InputType": {}}}, "Ss": {"type": "structure", "required": ["S3Uri", "DataAccessRoleArn"], "members": {"S3Uri": {}, "TuningDataS3Uri": {}, "DataAccessRoleArn": {}}}, "Sv": {"type": "list", "member": {"type": "structure", "required": ["Key", "Value"], "members": {"Key": {}, "Value": {}}}}, "S18": {"type": "list", "member": {}}, "S1d": {"type": "list", "member": {}}, "S1u": {"type": "structure", "members": {"ModelName": {}, "CreateTime": {"type": "timestamp"}, "LastModifiedTime": {"type": "timestamp"}, "LanguageCode": {}, "BaseModelName": {}, "ModelStatus": {}, "UpgradeAvailability": {"type": "boolean"}, "FailureReason": {}, "InputDataConfig": {"shape": "Ss"}}}, "S1z": {"type": "structure", "members": {"CallAnalyticsJobName": {}, "CallAnalyticsJobStatus": {}, "LanguageCode": {}, "MediaSampleRateHertz": {"type": "integer"}, "MediaFormat": {}, "Media": {"shape": "S23"}, "Transcript": {"shape": "S24"}, "StartTime": {"type": "timestamp"}, "CreationTime": {"type": "timestamp"}, "CompletionTime": {"type": "timestamp"}, "FailureReason": {}, "DataAccessRoleArn": {}, "IdentifiedLanguageScore": {"type": "float"}, "Settings": {"shape": "S26"}, "ChannelDefinitions": {"shape": "S2g"}}}, "S23": {"type": "structure", "members": {"MediaFileUri": {}, "RedactedMediaFileUri": {}}}, "S24": {"type": "structure", "members": {"TranscriptFileUri": {}, "RedactedTranscriptFileUri": {}}}, "S26": {"type": "structure", "members": {"VocabularyName": {}, "VocabularyFilterName": {}, "VocabularyFilterMethod": {}, "LanguageModelName": {}, "ContentRedaction": {"shape": "S28"}, "LanguageOptions": {"shape": "S2d"}, "LanguageIdSettings": {"shape": "S2e"}}}, "S28": {"type": "structure", "required": ["RedactionType", "RedactionOutput"], "members": {"RedactionType": {}, "RedactionOutput": {}, "PiiEntityTypes": {"type": "list", "member": {}}}}, "S2d": {"type": "list", "member": {}}, "S2e": {"type": "map", "key": {}, "value": {"type": "structure", "members": {"VocabularyName": {}, "VocabularyFilterName": {}, "LanguageModelName": {}}}}, "S2g": {"type": "list", "member": {"type": "structure", "members": {"ChannelId": {"type": "integer"}, "ParticipantRole": {}}}}, "S2l": {"type": "structure", "members": {"MedicalTranscriptionJobName": {}, "TranscriptionJobStatus": {}, "LanguageCode": {}, "MediaSampleRateHertz": {"type": "integer"}, "MediaFormat": {}, "Media": {"shape": "S23"}, "Transcript": {"type": "structure", "members": {"TranscriptFileUri": {}}}, "StartTime": {"type": "timestamp"}, "CreationTime": {"type": "timestamp"}, "CompletionTime": {"type": "timestamp"}, "FailureReason": {}, "Settings": {"shape": "S2p"}, "ContentIdentificationType": {}, "Specialty": {}, "Type": {}, "Tags": {"shape": "Sv"}}}, "S2p": {"type": "structure", "members": {"ShowSpeakerLabels": {"type": "boolean"}, "MaxSpeakerLabels": {"type": "integer"}, "ChannelIdentification": {"type": "boolean"}, "ShowAlternatives": {"type": "boolean"}, "MaxAlternatives": {"type": "integer"}, "VocabularyName": {}}}, "S2z": {"type": "structure", "members": {"TranscriptionJobName": {}, "TranscriptionJobStatus": {}, "LanguageCode": {}, "MediaSampleRateHertz": {"type": "integer"}, "MediaFormat": {}, "Media": {"shape": "S23"}, "Transcript": {"shape": "S24"}, "StartTime": {"type": "timestamp"}, "CreationTime": {"type": "timestamp"}, "CompletionTime": {"type": "timestamp"}, "FailureReason": {}, "Settings": {"shape": "S30"}, "ModelSettings": {"shape": "S31"}, "JobExecutionSettings": {"shape": "S32"}, "ContentRedaction": {"shape": "S28"}, "IdentifyLanguage": {"type": "boolean"}, "IdentifyMultipleLanguages": {"type": "boolean"}, "LanguageOptions": {"shape": "S2d"}, "IdentifiedLanguageScore": {"type": "float"}, "LanguageCodes": {"shape": "S33"}, "Tags": {"shape": "Sv"}, "Subtitles": {"type": "structure", "members": {"Formats": {"shape": "S37"}, "SubtitleFileUris": {"type": "list", "member": {}}, "OutputStartIndex": {"type": "integer"}}}, "LanguageIdSettings": {"shape": "S2e"}}}, "S30": {"type": "structure", "members": {"VocabularyName": {}, "ShowSpeakerLabels": {"type": "boolean"}, "MaxSpeakerLabels": {"type": "integer"}, "ChannelIdentification": {"type": "boolean"}, "ShowAlternatives": {"type": "boolean"}, "MaxAlternatives": {"type": "integer"}, "VocabularyFilterName": {}, "VocabularyFilterMethod": {}}}, "S31": {"type": "structure", "members": {"LanguageModelName": {}}}, "S32": {"type": "structure", "members": {"AllowDeferredExecution": {"type": "boolean"}, "DataAccessRoleArn": {}}}, "S33": {"type": "list", "member": {"type": "structure", "members": {"LanguageCode": {}, "DurationInSeconds": {"type": "float"}}}}, "S37": {"type": "list", "member": {}}, "S3y": {"type": "list", "member": {"type": "structure", "members": {"VocabularyName": {}, "LanguageCode": {}, "LastModifiedTime": {"type": "timestamp"}, "VocabularyState": {}}}}, "S4j": {"type": "map", "key": {}, "value": {}}}}