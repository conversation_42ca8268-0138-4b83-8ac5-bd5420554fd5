{"pagination": {"DescribeBlueGreenDeployments": {"input_token": "<PERSON><PERSON>", "limit_key": "MaxRecords", "output_token": "<PERSON><PERSON>", "result_key": "BlueGreenDeployments"}, "DescribeCertificates": {"input_token": "<PERSON><PERSON>", "limit_key": "MaxRecords", "output_token": "<PERSON><PERSON>", "result_key": "Certificates"}, "DescribeDBClusterBacktracks": {"input_token": "<PERSON><PERSON>", "limit_key": "MaxRecords", "output_token": "<PERSON><PERSON>", "result_key": "DBClusterBacktracks"}, "DescribeDBClusterEndpoints": {"input_token": "<PERSON><PERSON>", "limit_key": "MaxRecords", "output_token": "<PERSON><PERSON>", "result_key": "DBClusterEndpoints"}, "DescribeDBClusterParameterGroups": {"input_token": "<PERSON><PERSON>", "limit_key": "MaxRecords", "output_token": "<PERSON><PERSON>", "result_key": "DBClusterParameterGroups"}, "DescribeDBClusterParameters": {"input_token": "<PERSON><PERSON>", "limit_key": "MaxRecords", "output_token": "<PERSON><PERSON>", "result_key": "Parameters"}, "DescribeDBClusterSnapshots": {"input_token": "<PERSON><PERSON>", "limit_key": "MaxRecords", "output_token": "<PERSON><PERSON>", "result_key": "DBClusterSnapshots"}, "DescribeDBClusters": {"input_token": "<PERSON><PERSON>", "limit_key": "MaxRecords", "output_token": "<PERSON><PERSON>", "result_key": "DBClusters"}, "DescribeDBEngineVersions": {"input_token": "<PERSON><PERSON>", "limit_key": "MaxRecords", "output_token": "<PERSON><PERSON>", "result_key": "DBEngineVersions"}, "DescribeDBInstanceAutomatedBackups": {"input_token": "<PERSON><PERSON>", "limit_key": "MaxRecords", "output_token": "<PERSON><PERSON>", "result_key": "DBInstanceAutomatedBackups"}, "DescribeDBInstances": {"input_token": "<PERSON><PERSON>", "limit_key": "MaxRecords", "output_token": "<PERSON><PERSON>", "result_key": "DBInstances"}, "DescribeDBLogFiles": {"input_token": "<PERSON><PERSON>", "limit_key": "MaxRecords", "output_token": "<PERSON><PERSON>", "result_key": "DescribeDBLogFiles"}, "DescribeDBParameterGroups": {"input_token": "<PERSON><PERSON>", "limit_key": "MaxRecords", "output_token": "<PERSON><PERSON>", "result_key": "DBParameterGroups"}, "DescribeDBParameters": {"input_token": "<PERSON><PERSON>", "limit_key": "MaxRecords", "output_token": "<PERSON><PERSON>", "result_key": "Parameters"}, "DescribeDBProxies": {"input_token": "<PERSON><PERSON>", "limit_key": "MaxRecords", "output_token": "<PERSON><PERSON>", "result_key": "DBProxies"}, "DescribeDBProxyEndpoints": {"input_token": "<PERSON><PERSON>", "limit_key": "MaxRecords", "output_token": "<PERSON><PERSON>", "result_key": "DBProxyEndpoints"}, "DescribeDBProxyTargetGroups": {"input_token": "<PERSON><PERSON>", "limit_key": "MaxRecords", "output_token": "<PERSON><PERSON>", "result_key": "TargetGroups"}, "DescribeDBProxyTargets": {"input_token": "<PERSON><PERSON>", "limit_key": "MaxRecords", "output_token": "<PERSON><PERSON>", "result_key": "Targets"}, "DescribeDBSecurityGroups": {"input_token": "<PERSON><PERSON>", "limit_key": "MaxRecords", "output_token": "<PERSON><PERSON>", "result_key": "DBSecurityGroups"}, "DescribeDBSnapshots": {"input_token": "<PERSON><PERSON>", "limit_key": "MaxRecords", "output_token": "<PERSON><PERSON>", "result_key": "DBSnapshots"}, "DescribeDBSubnetGroups": {"input_token": "<PERSON><PERSON>", "limit_key": "MaxRecords", "output_token": "<PERSON><PERSON>", "result_key": "DBSubnetGroups"}, "DescribeEngineDefaultParameters": {"input_token": "<PERSON><PERSON>", "limit_key": "MaxRecords", "output_token": "EngineDefaults.Marker", "result_key": "EngineDefaults.Parameters"}, "DescribeEventSubscriptions": {"input_token": "<PERSON><PERSON>", "limit_key": "MaxRecords", "output_token": "<PERSON><PERSON>", "result_key": "EventSubscriptionsList"}, "DescribeEvents": {"input_token": "<PERSON><PERSON>", "limit_key": "MaxRecords", "output_token": "<PERSON><PERSON>", "result_key": "Events"}, "DescribeExportTasks": {"input_token": "<PERSON><PERSON>", "limit_key": "MaxRecords", "output_token": "<PERSON><PERSON>", "result_key": "ExportTasks"}, "DescribeGlobalClusters": {"input_token": "<PERSON><PERSON>", "limit_key": "MaxRecords", "output_token": "<PERSON><PERSON>", "result_key": "GlobalClusters"}, "DescribeOptionGroupOptions": {"input_token": "<PERSON><PERSON>", "limit_key": "MaxRecords", "output_token": "<PERSON><PERSON>", "result_key": "OptionGroupOptions"}, "DescribeOptionGroups": {"input_token": "<PERSON><PERSON>", "limit_key": "MaxRecords", "output_token": "<PERSON><PERSON>", "result_key": "OptionGroupsList"}, "DescribeOrderableDBInstanceOptions": {"input_token": "<PERSON><PERSON>", "limit_key": "MaxRecords", "output_token": "<PERSON><PERSON>", "result_key": "OrderableDBInstanceOptions"}, "DescribePendingMaintenanceActions": {"input_token": "<PERSON><PERSON>", "limit_key": "MaxRecords", "output_token": "<PERSON><PERSON>", "result_key": "PendingMaintenanceActions"}, "DescribeReservedDBInstances": {"input_token": "<PERSON><PERSON>", "limit_key": "MaxRecords", "output_token": "<PERSON><PERSON>", "result_key": "ReservedDBInstances"}, "DescribeReservedDBInstancesOfferings": {"input_token": "<PERSON><PERSON>", "limit_key": "MaxRecords", "output_token": "<PERSON><PERSON>", "result_key": "ReservedDBInstancesOfferings"}, "DescribeSourceRegions": {"input_token": "<PERSON><PERSON>", "limit_key": "MaxRecords", "output_token": "<PERSON><PERSON>", "result_key": "SourceRegions"}, "DownloadDBLogFilePortion": {"input_token": "<PERSON><PERSON>", "limit_key": "NumberOfLines", "more_results": "AdditionalDataPending", "output_token": "<PERSON><PERSON>", "result_key": "LogFileData"}, "ListTagsForResource": {"result_key": "TagList"}}}