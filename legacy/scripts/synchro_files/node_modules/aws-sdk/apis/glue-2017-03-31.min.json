{"version": "2.0", "metadata": {"apiVersion": "2017-03-31", "endpointPrefix": "glue", "jsonVersion": "1.1", "protocol": "json", "serviceFullName": "AWS Glue", "serviceId": "Glue", "signatureVersion": "v4", "targetPrefix": "AWSGlue", "uid": "glue-2017-03-31"}, "operations": {"BatchCreatePartition": {"input": {"type": "structure", "required": ["DatabaseName", "TableName", "PartitionInputList"], "members": {"CatalogId": {}, "DatabaseName": {}, "TableName": {}, "PartitionInputList": {"type": "list", "member": {"shape": "S5"}}}}, "output": {"type": "structure", "members": {"Errors": {"shape": "S12"}}}}, "BatchDeleteConnection": {"input": {"type": "structure", "required": ["ConnectionNameList"], "members": {"CatalogId": {}, "ConnectionNameList": {"type": "list", "member": {}}}}, "output": {"type": "structure", "members": {"Succeeded": {"shape": "Sn"}, "Errors": {"type": "map", "key": {}, "value": {"shape": "S14"}}}}}, "BatchDeletePartition": {"input": {"type": "structure", "required": ["DatabaseName", "TableName", "PartitionsToDelete"], "members": {"CatalogId": {}, "DatabaseName": {}, "TableName": {}, "PartitionsToDelete": {"type": "list", "member": {"shape": "S1c"}}}}, "output": {"type": "structure", "members": {"Errors": {"shape": "S12"}}}}, "BatchDeleteTable": {"input": {"type": "structure", "required": ["DatabaseName", "TablesToDelete"], "members": {"CatalogId": {}, "DatabaseName": {}, "TablesToDelete": {"type": "list", "member": {}}, "TransactionId": {}}}, "output": {"type": "structure", "members": {"Errors": {"type": "list", "member": {"type": "structure", "members": {"TableName": {}, "ErrorDetail": {"shape": "S14"}}}}}}}, "BatchDeleteTableVersion": {"input": {"type": "structure", "required": ["DatabaseName", "TableName", "VersionIds"], "members": {"CatalogId": {}, "DatabaseName": {}, "TableName": {}, "VersionIds": {"type": "list", "member": {}}}}, "output": {"type": "structure", "members": {"Errors": {"type": "list", "member": {"type": "structure", "members": {"TableName": {}, "VersionId": {}, "ErrorDetail": {"shape": "S14"}}}}}}}, "BatchGetBlueprints": {"input": {"type": "structure", "required": ["Names"], "members": {"Names": {"type": "list", "member": {}}, "IncludeBlueprint": {"type": "boolean"}, "IncludeParameterSpec": {"type": "boolean"}}}, "output": {"type": "structure", "members": {"Blueprints": {"type": "list", "member": {"shape": "S1w"}}, "MissingBlueprints": {"shape": "S24"}}}}, "BatchGetCrawlers": {"input": {"type": "structure", "required": ["CrawlerNames"], "members": {"CrawlerNames": {"shape": "S26"}}}, "output": {"type": "structure", "members": {"Crawlers": {"shape": "S28"}, "CrawlersNotFound": {"shape": "S26"}}}}, "BatchGetCustomEntityTypes": {"input": {"type": "structure", "required": ["Names"], "members": {"Names": {"shape": "S3n"}}}, "output": {"type": "structure", "members": {"CustomEntityTypes": {"shape": "S3p"}, "CustomEntityTypesNotFound": {"shape": "S3n"}}}}, "BatchGetDataQualityResult": {"input": {"type": "structure", "required": ["ResultIds"], "members": {"ResultIds": {"shape": "S3t"}}}, "output": {"type": "structure", "required": ["Results"], "members": {"Results": {"type": "list", "member": {"type": "structure", "members": {"ResultId": {}, "Score": {"type": "double"}, "DataSource": {"shape": "S3z"}, "RulesetName": {}, "EvaluationContext": {}, "StartedOn": {"type": "timestamp"}, "CompletedOn": {"type": "timestamp"}, "JobName": {}, "JobRunId": {}, "RulesetEvaluationRunId": {}, "RuleResults": {"shape": "S42"}}}}, "ResultsNotFound": {"shape": "S3t"}}}}, "BatchGetDevEndpoints": {"input": {"type": "structure", "required": ["DevEndpointNames"], "members": {"DevEndpointNames": {"shape": "S46"}}}, "output": {"type": "structure", "members": {"DevEndpoints": {"shape": "S48"}, "DevEndpointsNotFound": {"shape": "S46"}}}}, "BatchGetJobs": {"input": {"type": "structure", "required": ["JobNames"], "members": {"JobNames": {"shape": "S4i"}}}, "output": {"type": "structure", "members": {"Jobs": {"shape": "S4k"}, "JobsNotFound": {"shape": "S4i"}}}}, "BatchGetPartition": {"input": {"type": "structure", "required": ["DatabaseName", "TableName", "PartitionsToGet"], "members": {"CatalogId": {}, "DatabaseName": {}, "TableName": {}, "PartitionsToGet": {"shape": "S95"}}}, "output": {"type": "structure", "members": {"Partitions": {"shape": "S97"}, "UnprocessedKeys": {"shape": "S95"}}}}, "BatchGetTriggers": {"input": {"type": "structure", "required": ["TriggerNames"], "members": {"TriggerNames": {"shape": "S9a"}}}, "output": {"type": "structure", "members": {"Triggers": {"shape": "S9c"}, "TriggersNotFound": {"shape": "S9a"}}}}, "BatchGetWorkflows": {"input": {"type": "structure", "required": ["Names"], "members": {"Names": {"shape": "S9u"}, "IncludeGraph": {"type": "boolean"}}}, "output": {"type": "structure", "members": {"Workflows": {"type": "list", "member": {"shape": "S9x"}}, "MissingWorkflows": {"shape": "S9u"}}}}, "BatchStopJobRun": {"input": {"type": "structure", "required": ["JobName", "JobRunIds"], "members": {"JobName": {}, "JobRunIds": {"type": "list", "member": {}}}}, "output": {"type": "structure", "members": {"SuccessfulSubmissions": {"type": "list", "member": {"type": "structure", "members": {"JobName": {}, "JobRunId": {}}}}, "Errors": {"type": "list", "member": {"type": "structure", "members": {"JobName": {}, "JobRunId": {}, "ErrorDetail": {"shape": "S14"}}}}}}}, "BatchUpdatePartition": {"input": {"type": "structure", "required": ["DatabaseName", "TableName", "Entries"], "members": {"CatalogId": {}, "DatabaseName": {}, "TableName": {}, "Entries": {"type": "list", "member": {"type": "structure", "required": ["PartitionValueList", "PartitionInput"], "members": {"PartitionValueList": {"shape": "Sav"}, "PartitionInput": {"shape": "S5"}}}}}}, "output": {"type": "structure", "members": {"Errors": {"type": "list", "member": {"type": "structure", "members": {"PartitionValueList": {"shape": "Sav"}, "ErrorDetail": {"shape": "S14"}}}}}}}, "CancelDataQualityRuleRecommendationRun": {"input": {"type": "structure", "required": ["RunId"], "members": {"RunId": {}}}, "output": {"type": "structure", "members": {}}}, "CancelDataQualityRulesetEvaluationRun": {"input": {"type": "structure", "required": ["RunId"], "members": {"RunId": {}}}, "output": {"type": "structure", "members": {}}}, "CancelMLTaskRun": {"input": {"type": "structure", "required": ["TransformId", "TaskRunId"], "members": {"TransformId": {}, "TaskRunId": {}}}, "output": {"type": "structure", "members": {"TransformId": {}, "TaskRunId": {}, "Status": {}}}}, "CancelStatement": {"input": {"type": "structure", "required": ["SessionId", "Id"], "members": {"SessionId": {}, "Id": {"type": "integer"}, "RequestOrigin": {}}}, "output": {"type": "structure", "members": {}}}, "CheckSchemaVersionValidity": {"input": {"type": "structure", "required": ["DataFormat", "SchemaDefinition"], "members": {"DataFormat": {}, "SchemaDefinition": {}}}, "output": {"type": "structure", "members": {"Valid": {"type": "boolean"}, "Error": {}}}}, "CreateBlueprint": {"input": {"type": "structure", "required": ["Name", "BlueprintLocation"], "members": {"Name": {}, "Description": {}, "BlueprintLocation": {}, "Tags": {"shape": "Sbg"}}}, "output": {"type": "structure", "members": {"Name": {}}}}, "CreateClassifier": {"input": {"type": "structure", "members": {"GrokClassifier": {"type": "structure", "required": ["Classification", "Name", "GrokPattern"], "members": {"Classification": {}, "Name": {}, "GrokPattern": {}, "CustomPatterns": {}}}, "XMLClassifier": {"type": "structure", "required": ["Classification", "Name"], "members": {"Classification": {}, "Name": {}, "RowTag": {}}}, "JsonClassifier": {"type": "structure", "required": ["Name", "JsonPath"], "members": {"Name": {}, "JsonPath": {}}}, "CsvClassifier": {"type": "structure", "required": ["Name"], "members": {"Name": {}, "Delimiter": {}, "QuoteSymbol": {}, "ContainsHeader": {}, "Header": {"shape": "Sbx"}, "DisableValueTrimming": {"type": "boolean"}, "AllowSingleColumn": {"type": "boolean"}, "CustomDatatypeConfigured": {"type": "boolean"}, "CustomDatatypes": {"shape": "Sby"}}}}}, "output": {"type": "structure", "members": {}}}, "CreateConnection": {"input": {"type": "structure", "required": ["ConnectionInput"], "members": {"CatalogId": {}, "ConnectionInput": {"shape": "Sc1"}, "Tags": {"shape": "Sbg"}}}, "output": {"type": "structure", "members": {}}}, "CreateCrawler": {"input": {"type": "structure", "required": ["Name", "Role", "Targets"], "members": {"Name": {}, "Role": {}, "DatabaseName": {}, "Description": {}, "Targets": {"shape": "S2b"}, "Schedule": {}, "Classifiers": {"shape": "S2y"}, "TablePrefix": {}, "SchemaChangePolicy": {"shape": "S31"}, "RecrawlPolicy": {"shape": "S2z"}, "LineageConfiguration": {"shape": "S34"}, "LakeFormationConfiguration": {"shape": "S3k"}, "Configuration": {}, "CrawlerSecurityConfiguration": {}, "Tags": {"shape": "Sbg"}}}, "output": {"type": "structure", "members": {}}}, "CreateCustomEntityType": {"input": {"type": "structure", "required": ["Name", "RegexString"], "members": {"Name": {}, "RegexString": {}, "ContextWords": {"shape": "S3r"}}}, "output": {"type": "structure", "members": {"Name": {}}}}, "CreateDataQualityRuleset": {"input": {"type": "structure", "required": ["Name", "Ruleset"], "members": {"Name": {}, "Description": {}, "Ruleset": {}, "Tags": {"shape": "Sbg"}, "TargetTable": {"shape": "Scf"}, "ClientToken": {}}}, "output": {"type": "structure", "members": {"Name": {}}}, "idempotent": true}, "CreateDatabase": {"input": {"type": "structure", "required": ["DatabaseInput"], "members": {"CatalogId": {}, "DatabaseInput": {"shape": "<PERSON>i"}, "Tags": {"shape": "Sbg"}}}, "output": {"type": "structure", "members": {}}}, "CreateDevEndpoint": {"input": {"type": "structure", "required": ["EndpointName", "RoleArn"], "members": {"EndpointName": {}, "RoleArn": {}, "SecurityGroupIds": {"shape": "S4b"}, "SubnetId": {}, "PublicKey": {}, "PublicKeys": {"shape": "S4f"}, "NumberOfNodes": {"type": "integer"}, "WorkerType": {}, "GlueVersion": {}, "NumberOfWorkers": {"type": "integer"}, "ExtraPythonLibsS3Path": {}, "ExtraJarsS3Path": {}, "SecurityConfiguration": {}, "Tags": {"shape": "Sbg"}, "Arguments": {"shape": "S4g"}}}, "output": {"type": "structure", "members": {"EndpointName": {}, "Status": {}, "SecurityGroupIds": {"shape": "S4b"}, "SubnetId": {}, "RoleArn": {}, "YarnEndpointAddress": {}, "ZeppelinRemoteSparkInterpreterPort": {"type": "integer"}, "NumberOfNodes": {"type": "integer"}, "WorkerType": {}, "GlueVersion": {}, "NumberOfWorkers": {"type": "integer"}, "AvailabilityZone": {}, "VpcId": {}, "ExtraPythonLibsS3Path": {}, "ExtraJarsS3Path": {}, "FailureReason": {}, "SecurityConfiguration": {}, "CreatedTimestamp": {"type": "timestamp"}, "Arguments": {"shape": "S4g"}}}}, "CreateJob": {"input": {"type": "structure", "required": ["Name", "Role", "Command"], "members": {"Name": {}, "Description": {}, "LogUri": {}, "Role": {}, "ExecutionProperty": {"shape": "S4o"}, "Command": {"shape": "S4q"}, "DefaultArguments": {"shape": "S4t"}, "NonOverridableArguments": {"shape": "S4t"}, "Connections": {"shape": "S4u"}, "MaxRetries": {"type": "integer"}, "AllocatedCapacity": {"deprecated": true, "deprecatedMessage": "This property is deprecated, use MaxCapacity instead.", "type": "integer"}, "Timeout": {"type": "integer"}, "MaxCapacity": {"type": "double"}, "SecurityConfiguration": {}, "Tags": {"shape": "Sbg"}, "NotificationProperty": {"shape": "S4y"}, "GlueVersion": {}, "NumberOfWorkers": {"type": "integer"}, "WorkerType": {}, "CodeGenConfigurationNodes": {"shape": "S50"}, "ExecutionClass": {}, "SourceControlDetails": {"shape": "S91"}}}, "output": {"type": "structure", "members": {"Name": {}}}}, "CreateMLTransform": {"input": {"type": "structure", "required": ["Name", "InputRecordTables", "Parameters", "Role"], "members": {"Name": {}, "Description": {}, "InputRecordTables": {"shape": "Scx"}, "Parameters": {"shape": "<PERSON><PERSON>"}, "Role": {}, "GlueVersion": {}, "MaxCapacity": {"type": "double"}, "WorkerType": {}, "NumberOfWorkers": {"type": "integer"}, "Timeout": {"type": "integer"}, "MaxRetries": {"type": "integer"}, "Tags": {"shape": "Sbg"}, "TransformEncryption": {"shape": "Sd2"}}}, "output": {"type": "structure", "members": {"TransformId": {}}}}, "CreatePartition": {"input": {"type": "structure", "required": ["DatabaseName", "TableName", "PartitionInput"], "members": {"CatalogId": {}, "DatabaseName": {}, "TableName": {}, "PartitionInput": {"shape": "S5"}}}, "output": {"type": "structure", "members": {}}}, "CreatePartitionIndex": {"input": {"type": "structure", "required": ["DatabaseName", "TableName", "PartitionIndex"], "members": {"CatalogId": {}, "DatabaseName": {}, "TableName": {}, "PartitionIndex": {"shape": "Sd9"}}}, "output": {"type": "structure", "members": {}}}, "CreateRegistry": {"input": {"type": "structure", "required": ["RegistryName"], "members": {"RegistryName": {}, "Description": {}, "Tags": {"shape": "Sbg"}}}, "output": {"type": "structure", "members": {"RegistryArn": {}, "RegistryName": {}, "Description": {}, "Tags": {"shape": "Sbg"}}}}, "CreateSchema": {"input": {"type": "structure", "required": ["<PERSON><PERSON>aName", "DataFormat"], "members": {"RegistryId": {"shape": "Sdf"}, "SchemaName": {}, "DataFormat": {}, "Compatibility": {}, "Description": {}, "Tags": {"shape": "Sbg"}, "SchemaDefinition": {}}}, "output": {"type": "structure", "members": {"RegistryName": {}, "RegistryArn": {}, "SchemaName": {}, "SchemaArn": {}, "Description": {}, "DataFormat": {}, "Compatibility": {}, "SchemaCheckpoint": {"type": "long"}, "LatestSchemaVersion": {"type": "long"}, "NextSchemaVersion": {"type": "long"}, "SchemaStatus": {}, "Tags": {"shape": "Sbg"}, "SchemaVersionId": {}, "SchemaVersionStatus": {}}}}, "CreateScript": {"input": {"type": "structure", "members": {"DagNodes": {"shape": "Sdm"}, "DagEdges": {"shape": "Sdu"}, "Language": {}}}, "output": {"type": "structure", "members": {"PythonScript": {}, "ScalaCode": {}}}}, "CreateSecurityConfiguration": {"input": {"type": "structure", "required": ["Name", "EncryptionConfiguration"], "members": {"Name": {}, "EncryptionConfiguration": {"shape": "Se1"}}}, "output": {"type": "structure", "members": {"Name": {}, "CreatedTimestamp": {"type": "timestamp"}}}}, "CreateSession": {"input": {"type": "structure", "required": ["Id", "Role", "Command"], "members": {"Id": {}, "Description": {}, "Role": {}, "Command": {"shape": "Sed"}, "Timeout": {"type": "integer"}, "IdleTimeout": {"type": "integer"}, "DefaultArguments": {"shape": "See"}, "Connections": {"shape": "S4u"}, "MaxCapacity": {"type": "double"}, "NumberOfWorkers": {"type": "integer"}, "WorkerType": {}, "SecurityConfiguration": {}, "GlueVersion": {}, "Tags": {"shape": "Sbg"}, "RequestOrigin": {}}}, "output": {"type": "structure", "members": {"Session": {"shape": "<PERSON><PERSON>"}}}}, "CreateTable": {"input": {"type": "structure", "required": ["DatabaseName", "TableInput"], "members": {"CatalogId": {}, "DatabaseName": {}, "TableInput": {"shape": "<PERSON>l"}, "PartitionIndexes": {"type": "list", "member": {"shape": "Sd9"}}, "TransactionId": {}}}, "output": {"type": "structure", "members": {}}}, "CreateTrigger": {"input": {"type": "structure", "required": ["Name", "Type", "Actions"], "members": {"Name": {}, "WorkflowName": {}, "Type": {}, "Schedule": {}, "Predicate": {"shape": "S9j"}, "Actions": {"shape": "S9h"}, "Description": {}, "StartOnCreation": {"type": "boolean"}, "Tags": {"shape": "Sbg"}, "EventBatchingCondition": {"shape": "S9q"}}}, "output": {"type": "structure", "members": {"Name": {}}}}, "CreateUserDefinedFunction": {"input": {"type": "structure", "required": ["DatabaseName", "FunctionInput"], "members": {"CatalogId": {}, "DatabaseName": {}, "FunctionInput": {"shape": "Sev"}}}, "output": {"type": "structure", "members": {}}}, "CreateWorkflow": {"input": {"type": "structure", "required": ["Name"], "members": {"Name": {}, "Description": {}, "DefaultRunProperties": {"shape": "S9y"}, "Tags": {"shape": "Sbg"}, "MaxConcurrentRuns": {"type": "integer"}}}, "output": {"type": "structure", "members": {"Name": {}}}}, "DeleteBlueprint": {"input": {"type": "structure", "required": ["Name"], "members": {"Name": {}}}, "output": {"type": "structure", "members": {"Name": {}}}}, "DeleteClassifier": {"input": {"type": "structure", "required": ["Name"], "members": {"Name": {}}}, "output": {"type": "structure", "members": {}}}, "DeleteColumnStatisticsForPartition": {"input": {"type": "structure", "required": ["DatabaseName", "TableName", "PartitionValues", "ColumnName"], "members": {"CatalogId": {}, "DatabaseName": {}, "TableName": {}, "PartitionValues": {"shape": "S6"}, "ColumnName": {}}}, "output": {"type": "structure", "members": {}}}, "DeleteColumnStatisticsForTable": {"input": {"type": "structure", "required": ["DatabaseName", "TableName", "ColumnName"], "members": {"CatalogId": {}, "DatabaseName": {}, "TableName": {}, "ColumnName": {}}}, "output": {"type": "structure", "members": {}}}, "DeleteConnection": {"input": {"type": "structure", "required": ["ConnectionName"], "members": {"CatalogId": {}, "ConnectionName": {}}}, "output": {"type": "structure", "members": {}}}, "DeleteCrawler": {"input": {"type": "structure", "required": ["Name"], "members": {"Name": {}}}, "output": {"type": "structure", "members": {}}}, "DeleteCustomEntityType": {"input": {"type": "structure", "required": ["Name"], "members": {"Name": {}}}, "output": {"type": "structure", "members": {"Name": {}}}}, "DeleteDataQualityRuleset": {"input": {"type": "structure", "required": ["Name"], "members": {"Name": {}}}, "output": {"type": "structure", "members": {}}}, "DeleteDatabase": {"input": {"type": "structure", "required": ["Name"], "members": {"CatalogId": {}, "Name": {}}}, "output": {"type": "structure", "members": {}}}, "DeleteDevEndpoint": {"input": {"type": "structure", "required": ["EndpointName"], "members": {"EndpointName": {}}}, "output": {"type": "structure", "members": {}}}, "DeleteJob": {"input": {"type": "structure", "required": ["JobName"], "members": {"JobName": {}}}, "output": {"type": "structure", "members": {"JobName": {}}}}, "DeleteMLTransform": {"input": {"type": "structure", "required": ["TransformId"], "members": {"TransformId": {}}}, "output": {"type": "structure", "members": {"TransformId": {}}}}, "DeletePartition": {"input": {"type": "structure", "required": ["DatabaseName", "TableName", "PartitionValues"], "members": {"CatalogId": {}, "DatabaseName": {}, "TableName": {}, "PartitionValues": {"shape": "S6"}}}, "output": {"type": "structure", "members": {}}}, "DeletePartitionIndex": {"input": {"type": "structure", "required": ["DatabaseName", "TableName", "IndexName"], "members": {"CatalogId": {}, "DatabaseName": {}, "TableName": {}, "IndexName": {}}}, "output": {"type": "structure", "members": {}}}, "DeleteRegistry": {"input": {"type": "structure", "required": ["RegistryId"], "members": {"RegistryId": {"shape": "Sdf"}}}, "output": {"type": "structure", "members": {"RegistryName": {}, "RegistryArn": {}, "Status": {}}}}, "DeleteResourcePolicy": {"input": {"type": "structure", "members": {"PolicyHashCondition": {}, "ResourceArn": {}}}, "output": {"type": "structure", "members": {}}}, "DeleteSchema": {"input": {"type": "structure", "required": ["SchemaId"], "members": {"SchemaId": {"shape": "Sw"}}}, "output": {"type": "structure", "members": {"SchemaArn": {}, "SchemaName": {}, "Status": {}}}}, "DeleteSchemaVersions": {"input": {"type": "structure", "required": ["SchemaId", "Versions"], "members": {"SchemaId": {"shape": "Sw"}, "Versions": {}}}, "output": {"type": "structure", "members": {"SchemaVersionErrors": {"type": "list", "member": {"type": "structure", "members": {"VersionNumber": {"type": "long"}, "ErrorDetails": {"type": "structure", "members": {"ErrorCode": {}, "ErrorMessage": {}}}}}}}}}, "DeleteSecurityConfiguration": {"input": {"type": "structure", "required": ["Name"], "members": {"Name": {}}}, "output": {"type": "structure", "members": {}}}, "DeleteSession": {"input": {"type": "structure", "required": ["Id"], "members": {"Id": {}, "RequestOrigin": {}}}, "output": {"type": "structure", "members": {"Id": {}}}}, "DeleteTable": {"input": {"type": "structure", "required": ["DatabaseName", "Name"], "members": {"CatalogId": {}, "DatabaseName": {}, "Name": {}, "TransactionId": {}}}, "output": {"type": "structure", "members": {}}}, "DeleteTableVersion": {"input": {"type": "structure", "required": ["DatabaseName", "TableName", "VersionId"], "members": {"CatalogId": {}, "DatabaseName": {}, "TableName": {}, "VersionId": {}}}, "output": {"type": "structure", "members": {}}}, "DeleteTrigger": {"input": {"type": "structure", "required": ["Name"], "members": {"Name": {}}}, "output": {"type": "structure", "members": {"Name": {}}}}, "DeleteUserDefinedFunction": {"input": {"type": "structure", "required": ["DatabaseName", "FunctionName"], "members": {"CatalogId": {}, "DatabaseName": {}, "FunctionName": {}}}, "output": {"type": "structure", "members": {}}}, "DeleteWorkflow": {"input": {"type": "structure", "required": ["Name"], "members": {"Name": {}}}, "output": {"type": "structure", "members": {"Name": {}}}}, "GetBlueprint": {"input": {"type": "structure", "required": ["Name"], "members": {"Name": {}, "IncludeBlueprint": {"type": "boolean"}, "IncludeParameterSpec": {"type": "boolean"}}}, "output": {"type": "structure", "members": {"Blueprint": {"shape": "S1w"}}}}, "GetBlueprintRun": {"input": {"type": "structure", "required": ["BlueprintName", "RunId"], "members": {"BlueprintName": {}, "RunId": {}}}, "output": {"type": "structure", "members": {"BlueprintRun": {"shape": "Sgs"}}}}, "GetBlueprintRuns": {"input": {"type": "structure", "required": ["BlueprintName"], "members": {"BlueprintName": {}, "NextToken": {}, "MaxResults": {"type": "integer"}}}, "output": {"type": "structure", "members": {"BlueprintRuns": {"type": "list", "member": {"shape": "Sgs"}}, "NextToken": {}}}}, "GetCatalogImportStatus": {"input": {"type": "structure", "members": {"CatalogId": {}}}, "output": {"type": "structure", "members": {"ImportStatus": {"type": "structure", "members": {"ImportCompleted": {"type": "boolean"}, "ImportTime": {"type": "timestamp"}, "ImportedBy": {}}}}}}, "GetClassifier": {"input": {"type": "structure", "required": ["Name"], "members": {"Name": {}}}, "output": {"type": "structure", "members": {"Classifier": {"shape": "Sh6"}}}}, "GetClassifiers": {"input": {"type": "structure", "members": {"MaxResults": {"type": "integer"}, "NextToken": {}}}, "output": {"type": "structure", "members": {"Classifiers": {"type": "list", "member": {"shape": "Sh6"}}, "NextToken": {}}}}, "GetColumnStatisticsForPartition": {"input": {"type": "structure", "required": ["DatabaseName", "TableName", "PartitionValues", "ColumnNames"], "members": {"CatalogId": {}, "DatabaseName": {}, "TableName": {}, "PartitionValues": {"shape": "S6"}, "ColumnNames": {"shape": "Shg"}}}, "output": {"type": "structure", "members": {"ColumnStatisticsList": {"shape": "Shi"}, "Errors": {"shape": "Si0"}}}}, "GetColumnStatisticsForTable": {"input": {"type": "structure", "required": ["DatabaseName", "TableName", "ColumnNames"], "members": {"CatalogId": {}, "DatabaseName": {}, "TableName": {}, "ColumnNames": {"shape": "Shg"}}}, "output": {"type": "structure", "members": {"ColumnStatisticsList": {"shape": "Shi"}, "Errors": {"shape": "Si0"}}}}, "GetConnection": {"input": {"type": "structure", "required": ["Name"], "members": {"CatalogId": {}, "Name": {}, "HidePassword": {"type": "boolean"}}}, "output": {"type": "structure", "members": {"Connection": {"shape": "Si6"}}}}, "GetConnections": {"input": {"type": "structure", "members": {"CatalogId": {}, "Filter": {"type": "structure", "members": {"MatchCriteria": {"shape": "Sc3"}, "ConnectionType": {}}}, "HidePassword": {"type": "boolean"}, "NextToken": {}, "MaxResults": {"type": "integer"}}}, "output": {"type": "structure", "members": {"ConnectionList": {"type": "list", "member": {"shape": "Si6"}}, "NextToken": {}}}}, "GetCrawler": {"input": {"type": "structure", "required": ["Name"], "members": {"Name": {}}}, "output": {"type": "structure", "members": {"Crawler": {"shape": "S29"}}}}, "GetCrawlerMetrics": {"input": {"type": "structure", "members": {"CrawlerNameList": {"shape": "S26"}, "MaxResults": {"type": "integer"}, "NextToken": {}}}, "output": {"type": "structure", "members": {"CrawlerMetricsList": {"type": "list", "member": {"type": "structure", "members": {"CrawlerName": {}, "TimeLeftSeconds": {"type": "double"}, "StillEstimating": {"type": "boolean"}, "LastRuntimeSeconds": {"type": "double"}, "MedianRuntimeSeconds": {"type": "double"}, "TablesCreated": {"type": "integer"}, "TablesUpdated": {"type": "integer"}, "TablesDeleted": {"type": "integer"}}}}, "NextToken": {}}}}, "GetCrawlers": {"input": {"type": "structure", "members": {"MaxResults": {"type": "integer"}, "NextToken": {}}}, "output": {"type": "structure", "members": {"Crawlers": {"shape": "S28"}, "NextToken": {}}}}, "GetCustomEntityType": {"input": {"type": "structure", "required": ["Name"], "members": {"Name": {}}}, "output": {"type": "structure", "members": {"Name": {}, "RegexString": {}, "ContextWords": {"shape": "S3r"}}}}, "GetDataCatalogEncryptionSettings": {"input": {"type": "structure", "members": {"CatalogId": {}}}, "output": {"type": "structure", "members": {"DataCatalogEncryptionSettings": {"shape": "Sin"}}}}, "GetDataQualityResult": {"input": {"type": "structure", "required": ["ResultId"], "members": {"ResultId": {}}}, "output": {"type": "structure", "members": {"ResultId": {}, "Score": {"type": "double"}, "DataSource": {"shape": "S3z"}, "RulesetName": {}, "EvaluationContext": {}, "StartedOn": {"type": "timestamp"}, "CompletedOn": {"type": "timestamp"}, "JobName": {}, "JobRunId": {}, "RulesetEvaluationRunId": {}, "RuleResults": {"shape": "S42"}}}}, "GetDataQualityRuleRecommendationRun": {"input": {"type": "structure", "required": ["RunId"], "members": {"RunId": {}}}, "output": {"type": "structure", "members": {"RunId": {}, "DataSource": {"shape": "S3z"}, "Role": {}, "NumberOfWorkers": {"type": "integer"}, "Timeout": {"type": "integer"}, "Status": {}, "ErrorString": {}, "StartedOn": {"type": "timestamp"}, "LastModifiedOn": {"type": "timestamp"}, "CompletedOn": {"type": "timestamp"}, "ExecutionTime": {"type": "integer"}, "RecommendedRuleset": {}, "CreatedRulesetName": {}}}}, "GetDataQualityRuleset": {"input": {"type": "structure", "required": ["Name"], "members": {"Name": {}}}, "output": {"type": "structure", "members": {"Name": {}, "Description": {}, "Ruleset": {}, "TargetTable": {"shape": "Scf"}, "CreatedOn": {"type": "timestamp"}, "LastModifiedOn": {"type": "timestamp"}, "RecommendationRunId": {}}}}, "GetDataQualityRulesetEvaluationRun": {"input": {"type": "structure", "required": ["RunId"], "members": {"RunId": {}}}, "output": {"type": "structure", "members": {"RunId": {}, "DataSource": {"shape": "S3z"}, "Role": {}, "NumberOfWorkers": {"type": "integer"}, "Timeout": {"type": "integer"}, "AdditionalRunOptions": {"shape": "Siz"}, "Status": {}, "ErrorString": {}, "StartedOn": {"type": "timestamp"}, "LastModifiedOn": {"type": "timestamp"}, "CompletedOn": {"type": "timestamp"}, "ExecutionTime": {"type": "integer"}, "RulesetNames": {"shape": "Sj0"}, "ResultIds": {"type": "list", "member": {}}}}}, "GetDatabase": {"input": {"type": "structure", "required": ["Name"], "members": {"CatalogId": {}, "Name": {}}}, "output": {"type": "structure", "members": {"Database": {"shape": "Sj4"}}}}, "GetDatabases": {"input": {"type": "structure", "members": {"CatalogId": {}, "NextToken": {}, "MaxResults": {"type": "integer"}, "ResourceShareType": {}}}, "output": {"type": "structure", "required": ["DatabaseList"], "members": {"DatabaseList": {"type": "list", "member": {"shape": "Sj4"}}, "NextToken": {}}}}, "GetDataflowGraph": {"input": {"type": "structure", "members": {"PythonScript": {}}}, "output": {"type": "structure", "members": {"DagNodes": {"shape": "Sdm"}, "DagEdges": {"shape": "Sdu"}}}}, "GetDevEndpoint": {"input": {"type": "structure", "required": ["EndpointName"], "members": {"EndpointName": {}}}, "output": {"type": "structure", "members": {"DevEndpoint": {"shape": "S49"}}}}, "GetDevEndpoints": {"input": {"type": "structure", "members": {"MaxResults": {"type": "integer"}, "NextToken": {}}}, "output": {"type": "structure", "members": {"DevEndpoints": {"shape": "S48"}, "NextToken": {}}}}, "GetJob": {"input": {"type": "structure", "required": ["JobName"], "members": {"JobName": {}}}, "output": {"type": "structure", "members": {"Job": {"shape": "S4l"}}}}, "GetJobBookmark": {"input": {"type": "structure", "required": ["JobName"], "members": {"JobName": {}, "RunId": {}}}, "output": {"type": "structure", "members": {"JobBookmarkEntry": {"shape": "Sjm"}}}}, "GetJobRun": {"input": {"type": "structure", "required": ["JobName", "RunId"], "members": {"JobName": {}, "RunId": {}, "PredecessorsIncluded": {"type": "boolean"}}}, "output": {"type": "structure", "members": {"JobRun": {"shape": "Sa9"}}}}, "GetJobRuns": {"input": {"type": "structure", "required": ["JobName"], "members": {"JobName": {}, "NextToken": {}, "MaxResults": {"type": "integer"}}}, "output": {"type": "structure", "members": {"JobRuns": {"shape": "Sa8"}, "NextToken": {}}}}, "GetJobs": {"input": {"type": "structure", "members": {"NextToken": {}, "MaxResults": {"type": "integer"}}}, "output": {"type": "structure", "members": {"Jobs": {"shape": "S4k"}, "NextToken": {}}}}, "GetMLTaskRun": {"input": {"type": "structure", "required": ["TransformId", "TaskRunId"], "members": {"TransformId": {}, "TaskRunId": {}}}, "output": {"type": "structure", "members": {"TransformId": {}, "TaskRunId": {}, "Status": {}, "LogGroupName": {}, "Properties": {"shape": "Sjw"}, "ErrorString": {}, "StartedOn": {"type": "timestamp"}, "LastModifiedOn": {"type": "timestamp"}, "CompletedOn": {"type": "timestamp"}, "ExecutionTime": {"type": "integer"}}}}, "GetMLTaskRuns": {"input": {"type": "structure", "required": ["TransformId"], "members": {"TransformId": {}, "NextToken": {}, "MaxResults": {"type": "integer"}, "Filter": {"type": "structure", "members": {"TaskRunType": {}, "Status": {}, "StartedBefore": {"type": "timestamp"}, "StartedAfter": {"type": "timestamp"}}}, "Sort": {"type": "structure", "required": ["Column", "SortDirection"], "members": {"Column": {}, "SortDirection": {}}}}}, "output": {"type": "structure", "members": {"TaskRuns": {"type": "list", "member": {"type": "structure", "members": {"TransformId": {}, "TaskRunId": {}, "Status": {}, "LogGroupName": {}, "Properties": {"shape": "Sjw"}, "ErrorString": {}, "StartedOn": {"type": "timestamp"}, "LastModifiedOn": {"type": "timestamp"}, "CompletedOn": {"type": "timestamp"}, "ExecutionTime": {"type": "integer"}}}}, "NextToken": {}}}}, "GetMLTransform": {"input": {"type": "structure", "required": ["TransformId"], "members": {"TransformId": {}}}, "output": {"type": "structure", "members": {"TransformId": {}, "Name": {}, "Description": {}, "Status": {}, "CreatedOn": {"type": "timestamp"}, "LastModifiedOn": {"type": "timestamp"}, "InputRecordTables": {"shape": "Scx"}, "Parameters": {"shape": "<PERSON><PERSON>"}, "EvaluationMetrics": {"shape": "Skf"}, "LabelCount": {"type": "integer"}, "Schema": {"shape": "Skm"}, "Role": {}, "GlueVersion": {}, "MaxCapacity": {"type": "double"}, "WorkerType": {}, "NumberOfWorkers": {"type": "integer"}, "Timeout": {"type": "integer"}, "MaxRetries": {"type": "integer"}, "TransformEncryption": {"shape": "Sd2"}}}}, "GetMLTransforms": {"input": {"type": "structure", "members": {"NextToken": {}, "MaxResults": {"type": "integer"}, "Filter": {"shape": "Skp"}, "Sort": {"shape": "Skq"}}}, "output": {"type": "structure", "required": ["Transforms"], "members": {"Transforms": {"type": "list", "member": {"type": "structure", "members": {"TransformId": {}, "Name": {}, "Description": {}, "Status": {}, "CreatedOn": {"type": "timestamp"}, "LastModifiedOn": {"type": "timestamp"}, "InputRecordTables": {"shape": "Scx"}, "Parameters": {"shape": "<PERSON><PERSON>"}, "EvaluationMetrics": {"shape": "Skf"}, "LabelCount": {"type": "integer"}, "Schema": {"shape": "Skm"}, "Role": {}, "GlueVersion": {}, "MaxCapacity": {"type": "double"}, "WorkerType": {}, "NumberOfWorkers": {"type": "integer"}, "Timeout": {"type": "integer"}, "MaxRetries": {"type": "integer"}, "TransformEncryption": {"shape": "Sd2"}}}}, "NextToken": {}}}}, "GetMapping": {"input": {"type": "structure", "required": ["Source"], "members": {"Source": {"shape": "Skw"}, "Sinks": {"shape": "Skx"}, "Location": {"shape": "Sky"}}}, "output": {"type": "structure", "required": ["Mapping"], "members": {"Mapping": {"shape": "Sl0"}}}}, "GetPartition": {"input": {"type": "structure", "required": ["DatabaseName", "TableName", "PartitionValues"], "members": {"CatalogId": {}, "DatabaseName": {}, "TableName": {}, "PartitionValues": {"shape": "S6"}}}, "output": {"type": "structure", "members": {"Partition": {"shape": "S98"}}}}, "GetPartitionIndexes": {"input": {"type": "structure", "required": ["DatabaseName", "TableName"], "members": {"CatalogId": {}, "DatabaseName": {}, "TableName": {}, "NextToken": {}}}, "output": {"type": "structure", "members": {"PartitionIndexDescriptorList": {"type": "list", "member": {"type": "structure", "required": ["IndexName", "Keys", "IndexStatus"], "members": {"IndexName": {}, "Keys": {"type": "list", "member": {"type": "structure", "required": ["Name", "Type"], "members": {"Name": {}, "Type": {}}}}, "IndexStatus": {}, "BackfillErrors": {"type": "list", "member": {"type": "structure", "members": {"Code": {}, "Partitions": {"type": "list", "member": {"shape": "S1c"}}}}}}}}, "NextToken": {}}}}, "GetPartitions": {"input": {"type": "structure", "required": ["DatabaseName", "TableName"], "members": {"CatalogId": {}, "DatabaseName": {}, "TableName": {}, "Expression": {}, "NextToken": {}, "Segment": {"shape": "Slk"}, "MaxResults": {"type": "integer"}, "ExcludeColumnSchema": {"type": "boolean"}, "TransactionId": {}, "QueryAsOfTime": {"type": "timestamp"}}}, "output": {"type": "structure", "members": {"Partitions": {"shape": "S97"}, "NextToken": {}}}}, "GetPlan": {"input": {"type": "structure", "required": ["Mapping", "Source"], "members": {"Mapping": {"shape": "Sl0"}, "Source": {"shape": "Skw"}, "Sinks": {"shape": "Skx"}, "Location": {"shape": "Sky"}, "Language": {}, "AdditionalPlanOptionsMap": {"type": "map", "key": {}, "value": {}}}}, "output": {"type": "structure", "members": {"PythonScript": {}, "ScalaCode": {}}}}, "GetRegistry": {"input": {"type": "structure", "required": ["RegistryId"], "members": {"RegistryId": {"shape": "Sdf"}}}, "output": {"type": "structure", "members": {"RegistryName": {}, "RegistryArn": {}, "Description": {}, "Status": {}, "CreatedTime": {}, "UpdatedTime": {}}}}, "GetResourcePolicies": {"input": {"type": "structure", "members": {"NextToken": {}, "MaxResults": {"type": "integer"}}}, "output": {"type": "structure", "members": {"GetResourcePoliciesResponseList": {"type": "list", "member": {"type": "structure", "members": {"PolicyInJson": {}, "PolicyHash": {}, "CreateTime": {"type": "timestamp"}, "UpdateTime": {"type": "timestamp"}}}}, "NextToken": {}}}}, "GetResourcePolicy": {"input": {"type": "structure", "members": {"ResourceArn": {}}}, "output": {"type": "structure", "members": {"PolicyInJson": {}, "PolicyHash": {}, "CreateTime": {"type": "timestamp"}, "UpdateTime": {"type": "timestamp"}}}}, "GetSchema": {"input": {"type": "structure", "required": ["SchemaId"], "members": {"SchemaId": {"shape": "Sw"}}}, "output": {"type": "structure", "members": {"RegistryName": {}, "RegistryArn": {}, "SchemaName": {}, "SchemaArn": {}, "Description": {}, "DataFormat": {}, "Compatibility": {}, "SchemaCheckpoint": {"type": "long"}, "LatestSchemaVersion": {"type": "long"}, "NextSchemaVersion": {"type": "long"}, "SchemaStatus": {}, "CreatedTime": {}, "UpdatedTime": {}}}}, "GetSchemaByDefinition": {"input": {"type": "structure", "required": ["SchemaId", "SchemaDefinition"], "members": {"SchemaId": {"shape": "Sw"}, "SchemaDefinition": {}}}, "output": {"type": "structure", "members": {"SchemaVersionId": {}, "SchemaArn": {}, "DataFormat": {}, "Status": {}, "CreatedTime": {}}}}, "GetSchemaVersion": {"input": {"type": "structure", "members": {"SchemaId": {"shape": "Sw"}, "SchemaVersionId": {}, "SchemaVersionNumber": {"shape": "Sm7"}}}, "output": {"type": "structure", "members": {"SchemaVersionId": {}, "SchemaDefinition": {}, "DataFormat": {}, "SchemaArn": {}, "VersionNumber": {"type": "long"}, "Status": {}, "CreatedTime": {}}}}, "GetSchemaVersionsDiff": {"input": {"type": "structure", "required": ["SchemaId", "FirstSchemaVersionNumber", "SecondSchemaVersionNumber", "SchemaDiffType"], "members": {"SchemaId": {"shape": "Sw"}, "FirstSchemaVersionNumber": {"shape": "Sm7"}, "SecondSchemaVersionNumber": {"shape": "Sm7"}, "SchemaDiffType": {}}}, "output": {"type": "structure", "members": {"Diff": {}}}}, "GetSecurityConfiguration": {"input": {"type": "structure", "required": ["Name"], "members": {"Name": {}}}, "output": {"type": "structure", "members": {"SecurityConfiguration": {"shape": "Smg"}}}}, "GetSecurityConfigurations": {"input": {"type": "structure", "members": {"MaxResults": {"type": "integer"}, "NextToken": {}}}, "output": {"type": "structure", "members": {"SecurityConfigurations": {"type": "list", "member": {"shape": "Smg"}}, "NextToken": {}}}}, "GetSession": {"input": {"type": "structure", "required": ["Id"], "members": {"Id": {}, "RequestOrigin": {}}}, "output": {"type": "structure", "members": {"Session": {"shape": "<PERSON><PERSON>"}}}}, "GetStatement": {"input": {"type": "structure", "required": ["SessionId", "Id"], "members": {"SessionId": {}, "Id": {"type": "integer"}, "RequestOrigin": {}}}, "output": {"type": "structure", "members": {"Statement": {"shape": "Smo"}}}}, "GetTable": {"input": {"type": "structure", "required": ["DatabaseName", "Name"], "members": {"CatalogId": {}, "DatabaseName": {}, "Name": {}, "TransactionId": {}, "QueryAsOfTime": {"type": "timestamp"}}}, "output": {"type": "structure", "members": {"Table": {"shape": "Smv"}}}}, "GetTableVersion": {"input": {"type": "structure", "required": ["DatabaseName", "TableName"], "members": {"CatalogId": {}, "DatabaseName": {}, "TableName": {}, "VersionId": {}}}, "output": {"type": "structure", "members": {"TableVersion": {"shape": "Smy"}}}}, "GetTableVersions": {"input": {"type": "structure", "required": ["DatabaseName", "TableName"], "members": {"CatalogId": {}, "DatabaseName": {}, "TableName": {}, "NextToken": {}, "MaxResults": {"type": "integer"}}}, "output": {"type": "structure", "members": {"TableVersions": {"type": "list", "member": {"shape": "Smy"}}, "NextToken": {}}}}, "GetTables": {"input": {"type": "structure", "required": ["DatabaseName"], "members": {"CatalogId": {}, "DatabaseName": {}, "Expression": {}, "NextToken": {}, "MaxResults": {"type": "integer"}, "TransactionId": {}, "QueryAsOfTime": {"type": "timestamp"}}}, "output": {"type": "structure", "members": {"TableList": {"shape": "Sn5"}, "NextToken": {}}}}, "GetTags": {"input": {"type": "structure", "required": ["ResourceArn"], "members": {"ResourceArn": {}}}, "output": {"type": "structure", "members": {"Tags": {"shape": "Sbg"}}}}, "GetTrigger": {"input": {"type": "structure", "required": ["Name"], "members": {"Name": {}}}, "output": {"type": "structure", "members": {"Trigger": {"shape": "S9d"}}}}, "GetTriggers": {"input": {"type": "structure", "members": {"NextToken": {}, "DependentJobName": {}, "MaxResults": {"type": "integer"}}}, "output": {"type": "structure", "members": {"Triggers": {"shape": "S9c"}, "NextToken": {}}}}, "GetUnfilteredPartitionMetadata": {"input": {"type": "structure", "required": ["CatalogId", "DatabaseName", "TableName", "PartitionValues", "SupportedPermissionTypes"], "members": {"CatalogId": {}, "DatabaseName": {}, "TableName": {}, "PartitionValues": {"shape": "S6"}, "AuditContext": {"shape": "Snd"}, "SupportedPermissionTypes": {"shape": "Sng"}}}, "output": {"type": "structure", "members": {"Partition": {"shape": "S98"}, "AuthorizedColumns": {"shape": "Sn"}, "IsRegisteredWithLakeFormation": {"type": "boolean"}}}}, "GetUnfilteredPartitionsMetadata": {"input": {"type": "structure", "required": ["CatalogId", "DatabaseName", "TableName", "SupportedPermissionTypes"], "members": {"CatalogId": {}, "DatabaseName": {}, "TableName": {}, "Expression": {}, "AuditContext": {"shape": "Snd"}, "SupportedPermissionTypes": {"shape": "Sng"}, "NextToken": {}, "Segment": {"shape": "Slk"}, "MaxResults": {"type": "integer"}}}, "output": {"type": "structure", "members": {"UnfilteredPartitions": {"type": "list", "member": {"type": "structure", "members": {"Partition": {"shape": "S98"}, "AuthorizedColumns": {"shape": "Sn"}, "IsRegisteredWithLakeFormation": {"type": "boolean"}}}}, "NextToken": {}}}}, "GetUnfilteredTableMetadata": {"input": {"type": "structure", "required": ["CatalogId", "DatabaseName", "Name", "SupportedPermissionTypes"], "members": {"CatalogId": {}, "DatabaseName": {}, "Name": {}, "AuditContext": {"shape": "Snd"}, "SupportedPermissionTypes": {"shape": "Sng"}}}, "output": {"type": "structure", "members": {"Table": {"shape": "Smv"}, "AuthorizedColumns": {"shape": "Sn"}, "IsRegisteredWithLakeFormation": {"type": "boolean"}, "CellFilters": {"type": "list", "member": {"type": "structure", "members": {"ColumnName": {}, "RowFilterExpression": {}}}}}}}, "GetUserDefinedFunction": {"input": {"type": "structure", "required": ["DatabaseName", "FunctionName"], "members": {"CatalogId": {}, "DatabaseName": {}, "FunctionName": {}}}, "output": {"type": "structure", "members": {"UserDefinedFunction": {"shape": "Snt"}}}}, "GetUserDefinedFunctions": {"input": {"type": "structure", "required": ["Pattern"], "members": {"CatalogId": {}, "DatabaseName": {}, "Pattern": {}, "NextToken": {}, "MaxResults": {"type": "integer"}}}, "output": {"type": "structure", "members": {"UserDefinedFunctions": {"type": "list", "member": {"shape": "Snt"}}, "NextToken": {}}}}, "GetWorkflow": {"input": {"type": "structure", "required": ["Name"], "members": {"Name": {}, "IncludeGraph": {"type": "boolean"}}}, "output": {"type": "structure", "members": {"Workflow": {"shape": "S9x"}}}}, "GetWorkflowRun": {"input": {"type": "structure", "required": ["Name", "RunId"], "members": {"Name": {}, "RunId": {}, "IncludeGraph": {"type": "boolean"}}}, "output": {"type": "structure", "members": {"Run": {"shape": "S9z"}}}}, "GetWorkflowRunProperties": {"input": {"type": "structure", "required": ["Name", "RunId"], "members": {"Name": {}, "RunId": {}}}, "output": {"type": "structure", "members": {"RunProperties": {"shape": "S9y"}}}}, "GetWorkflowRuns": {"input": {"type": "structure", "required": ["Name"], "members": {"Name": {}, "IncludeGraph": {"type": "boolean"}, "NextToken": {}, "MaxResults": {"type": "integer"}}}, "output": {"type": "structure", "members": {"Runs": {"type": "list", "member": {"shape": "S9z"}}, "NextToken": {}}}}, "ImportCatalogToGlue": {"input": {"type": "structure", "members": {"CatalogId": {}}}, "output": {"type": "structure", "members": {}}}, "ListBlueprints": {"input": {"type": "structure", "members": {"NextToken": {}, "MaxResults": {"type": "integer"}, "Tags": {"shape": "Sbg"}}}, "output": {"type": "structure", "members": {"Blueprints": {"shape": "S24"}, "NextToken": {}}}}, "ListCrawlers": {"input": {"type": "structure", "members": {"MaxResults": {"type": "integer"}, "NextToken": {}, "Tags": {"shape": "Sbg"}}}, "output": {"type": "structure", "members": {"CrawlerNames": {"shape": "S26"}, "NextToken": {}}}}, "ListCrawls": {"input": {"type": "structure", "required": ["CrawlerName"], "members": {"CrawlerName": {}, "MaxResults": {"type": "integer"}, "Filters": {"type": "list", "member": {"type": "structure", "members": {"FieldName": {}, "FilterOperator": {}, "FieldValue": {}}}}, "NextToken": {}}}, "output": {"type": "structure", "members": {"Crawls": {"type": "list", "member": {"type": "structure", "members": {"CrawlId": {}, "State": {}, "StartTime": {"type": "timestamp"}, "EndTime": {"type": "timestamp"}, "Summary": {}, "ErrorMessage": {}, "LogGroup": {}, "LogStream": {}, "MessagePrefix": {}, "DPUHour": {"type": "double"}}}}, "NextToken": {}}}}, "ListCustomEntityTypes": {"input": {"type": "structure", "members": {"NextToken": {}, "MaxResults": {"type": "integer"}}}, "output": {"type": "structure", "members": {"CustomEntityTypes": {"shape": "S3p"}, "NextToken": {}}}}, "ListDataQualityResults": {"input": {"type": "structure", "members": {"Filter": {"type": "structure", "members": {"DataSource": {"shape": "S3z"}, "JobName": {}, "JobRunId": {}, "StartedAfter": {"type": "timestamp"}, "StartedBefore": {"type": "timestamp"}}}, "NextToken": {}, "MaxResults": {"type": "integer"}}}, "output": {"type": "structure", "required": ["Results"], "members": {"Results": {"type": "list", "member": {"type": "structure", "members": {"ResultId": {}, "DataSource": {"shape": "S3z"}, "JobName": {}, "JobRunId": {}, "StartedOn": {"type": "timestamp"}}}}, "NextToken": {}}}}, "ListDataQualityRuleRecommendationRuns": {"input": {"type": "structure", "members": {"Filter": {"type": "structure", "required": ["DataSource"], "members": {"DataSource": {"shape": "S3z"}, "StartedBefore": {"type": "timestamp"}, "StartedAfter": {"type": "timestamp"}}}, "NextToken": {}, "MaxResults": {"type": "integer"}}}, "output": {"type": "structure", "members": {"Runs": {"type": "list", "member": {"type": "structure", "members": {"RunId": {}, "Status": {}, "StartedOn": {"type": "timestamp"}, "DataSource": {"shape": "S3z"}}}}, "NextToken": {}}}}, "ListDataQualityRulesetEvaluationRuns": {"input": {"type": "structure", "members": {"Filter": {"type": "structure", "required": ["DataSource"], "members": {"DataSource": {"shape": "S3z"}, "StartedBefore": {"type": "timestamp"}, "StartedAfter": {"type": "timestamp"}}}, "NextToken": {}, "MaxResults": {"type": "integer"}}}, "output": {"type": "structure", "members": {"Runs": {"type": "list", "member": {"type": "structure", "members": {"RunId": {}, "Status": {}, "StartedOn": {"type": "timestamp"}, "DataSource": {"shape": "S3z"}}}}, "NextToken": {}}}}, "ListDataQualityRulesets": {"input": {"type": "structure", "members": {"NextToken": {}, "MaxResults": {"type": "integer"}, "Filter": {"type": "structure", "members": {"Name": {}, "Description": {}, "CreatedBefore": {"type": "timestamp"}, "CreatedAfter": {"type": "timestamp"}, "LastModifiedBefore": {"type": "timestamp"}, "LastModifiedAfter": {"type": "timestamp"}, "TargetTable": {"shape": "Scf"}}}, "Tags": {"shape": "Sbg"}}}, "output": {"type": "structure", "members": {"Rulesets": {"type": "list", "member": {"type": "structure", "members": {"Name": {}, "Description": {}, "CreatedOn": {"type": "timestamp"}, "LastModifiedOn": {"type": "timestamp"}, "TargetTable": {"shape": "Scf"}, "RecommendationRunId": {}, "RuleCount": {"type": "integer"}}}}, "NextToken": {}}}}, "ListDevEndpoints": {"input": {"type": "structure", "members": {"NextToken": {}, "MaxResults": {"type": "integer"}, "Tags": {"shape": "Sbg"}}}, "output": {"type": "structure", "members": {"DevEndpointNames": {"type": "list", "member": {}}, "NextToken": {}}}}, "ListJobs": {"input": {"type": "structure", "members": {"NextToken": {}, "MaxResults": {"type": "integer"}, "Tags": {"shape": "Sbg"}}}, "output": {"type": "structure", "members": {"JobNames": {"shape": "S4i"}, "NextToken": {}}}}, "ListMLTransforms": {"input": {"type": "structure", "members": {"NextToken": {}, "MaxResults": {"type": "integer"}, "Filter": {"shape": "Skp"}, "Sort": {"shape": "Skq"}, "Tags": {"shape": "Sbg"}}}, "output": {"type": "structure", "required": ["TransformIds"], "members": {"TransformIds": {"type": "list", "member": {}}, "NextToken": {}}}}, "ListRegistries": {"input": {"type": "structure", "members": {"MaxResults": {"type": "integer"}, "NextToken": {}}}, "output": {"type": "structure", "members": {"Registries": {"type": "list", "member": {"type": "structure", "members": {"RegistryName": {}, "RegistryArn": {}, "Description": {}, "Status": {}, "CreatedTime": {}, "UpdatedTime": {}}}}, "NextToken": {}}}}, "ListSchemaVersions": {"input": {"type": "structure", "required": ["SchemaId"], "members": {"SchemaId": {"shape": "Sw"}, "MaxResults": {"type": "integer"}, "NextToken": {}}}, "output": {"type": "structure", "members": {"Schemas": {"type": "list", "member": {"type": "structure", "members": {"SchemaArn": {}, "SchemaVersionId": {}, "VersionNumber": {"type": "long"}, "Status": {}, "CreatedTime": {}}}}, "NextToken": {}}}}, "ListSchemas": {"input": {"type": "structure", "members": {"RegistryId": {"shape": "Sdf"}, "MaxResults": {"type": "integer"}, "NextToken": {}}}, "output": {"type": "structure", "members": {"Schemas": {"type": "list", "member": {"type": "structure", "members": {"RegistryName": {}, "SchemaName": {}, "SchemaArn": {}, "Description": {}, "SchemaStatus": {}, "CreatedTime": {}, "UpdatedTime": {}}}}, "NextToken": {}}}}, "ListSessions": {"input": {"type": "structure", "members": {"NextToken": {}, "MaxResults": {"type": "integer"}, "Tags": {"shape": "Sbg"}, "RequestOrigin": {}}}, "output": {"type": "structure", "members": {"Ids": {"type": "list", "member": {}}, "Sessions": {"type": "list", "member": {"shape": "<PERSON><PERSON>"}}, "NextToken": {}}}}, "ListStatements": {"input": {"type": "structure", "required": ["SessionId"], "members": {"SessionId": {}, "RequestOrigin": {}, "NextToken": {}}}, "output": {"type": "structure", "members": {"Statements": {"type": "list", "member": {"shape": "Smo"}}, "NextToken": {}}}}, "ListTriggers": {"input": {"type": "structure", "members": {"NextToken": {}, "DependentJobName": {}, "MaxResults": {"type": "integer"}, "Tags": {"shape": "Sbg"}}}, "output": {"type": "structure", "members": {"TriggerNames": {"shape": "S9a"}, "NextToken": {}}}}, "ListWorkflows": {"input": {"type": "structure", "members": {"NextToken": {}, "MaxResults": {"type": "integer"}}}, "output": {"type": "structure", "members": {"Workflows": {"shape": "S9u"}, "NextToken": {}}}}, "PutDataCatalogEncryptionSettings": {"input": {"type": "structure", "required": ["DataCatalogEncryptionSettings"], "members": {"CatalogId": {}, "DataCatalogEncryptionSettings": {"shape": "Sin"}}}, "output": {"type": "structure", "members": {}}}, "PutResourcePolicy": {"input": {"type": "structure", "required": ["PolicyInJson"], "members": {"PolicyInJson": {}, "ResourceArn": {}, "PolicyHashCondition": {}, "PolicyExistsCondition": {}, "EnableHybrid": {}}}, "output": {"type": "structure", "members": {"PolicyHash": {}}}}, "PutSchemaVersionMetadata": {"input": {"type": "structure", "required": ["MetadataKeyValue"], "members": {"SchemaId": {"shape": "Sw"}, "SchemaVersionNumber": {"shape": "Sm7"}, "SchemaVersionId": {}, "MetadataKeyValue": {"shape": "Sqd"}}}, "output": {"type": "structure", "members": {"SchemaArn": {}, "SchemaName": {}, "RegistryName": {}, "LatestVersion": {"type": "boolean"}, "VersionNumber": {"type": "long"}, "SchemaVersionId": {}, "MetadataKey": {}, "MetadataValue": {}}}}, "PutWorkflowRunProperties": {"input": {"type": "structure", "required": ["Name", "RunId", "RunProperties"], "members": {"Name": {}, "RunId": {}, "RunProperties": {"shape": "S9y"}}}, "output": {"type": "structure", "members": {}}}, "QuerySchemaVersionMetadata": {"input": {"type": "structure", "members": {"SchemaId": {"shape": "Sw"}, "SchemaVersionNumber": {"shape": "Sm7"}, "SchemaVersionId": {}, "MetadataList": {"type": "list", "member": {"shape": "Sqd"}}, "MaxResults": {"type": "integer"}, "NextToken": {}}}, "output": {"type": "structure", "members": {"MetadataInfoMap": {"type": "map", "key": {}, "value": {"type": "structure", "members": {"MetadataValue": {}, "CreatedTime": {}, "OtherMetadataValueList": {"type": "list", "member": {"type": "structure", "members": {"MetadataValue": {}, "CreatedTime": {}}}}}}}, "SchemaVersionId": {}, "NextToken": {}}}}, "RegisterSchemaVersion": {"input": {"type": "structure", "required": ["SchemaId", "SchemaDefinition"], "members": {"SchemaId": {"shape": "Sw"}, "SchemaDefinition": {}}}, "output": {"type": "structure", "members": {"SchemaVersionId": {}, "VersionNumber": {"type": "long"}, "Status": {}}}}, "RemoveSchemaVersionMetadata": {"input": {"type": "structure", "required": ["MetadataKeyValue"], "members": {"SchemaId": {"shape": "Sw"}, "SchemaVersionNumber": {"shape": "Sm7"}, "SchemaVersionId": {}, "MetadataKeyValue": {"shape": "Sqd"}}}, "output": {"type": "structure", "members": {"SchemaArn": {}, "SchemaName": {}, "RegistryName": {}, "LatestVersion": {"type": "boolean"}, "VersionNumber": {"type": "long"}, "SchemaVersionId": {}, "MetadataKey": {}, "MetadataValue": {}}}}, "ResetJobBookmark": {"input": {"type": "structure", "required": ["JobName"], "members": {"JobName": {}, "RunId": {}}}, "output": {"type": "structure", "members": {"JobBookmarkEntry": {"shape": "Sjm"}}}}, "ResumeWorkflowRun": {"input": {"type": "structure", "required": ["Name", "RunId", "NodeIds"], "members": {"Name": {}, "RunId": {}, "NodeIds": {"shape": "Sqy"}}}, "output": {"type": "structure", "members": {"RunId": {}, "NodeIds": {"shape": "Sqy"}}}}, "RunStatement": {"input": {"type": "structure", "required": ["SessionId", "Code"], "members": {"SessionId": {}, "Code": {}, "RequestOrigin": {}}}, "output": {"type": "structure", "members": {"Id": {"type": "integer"}}}}, "SearchTables": {"input": {"type": "structure", "members": {"CatalogId": {}, "NextToken": {}, "Filters": {"type": "list", "member": {"type": "structure", "members": {"Key": {}, "Value": {}, "Comparator": {}}}}, "SearchText": {}, "SortCriteria": {"type": "list", "member": {"type": "structure", "members": {"FieldName": {}, "Sort": {}}}}, "MaxResults": {"type": "integer"}, "ResourceShareType": {}}}, "output": {"type": "structure", "members": {"NextToken": {}, "TableList": {"shape": "Sn5"}}}}, "StartBlueprintRun": {"input": {"type": "structure", "required": ["BlueprintName", "RoleArn"], "members": {"BlueprintName": {}, "Parameters": {}, "RoleArn": {}}}, "output": {"type": "structure", "members": {"RunId": {}}}}, "StartCrawler": {"input": {"type": "structure", "required": ["Name"], "members": {"Name": {}}}, "output": {"type": "structure", "members": {}}}, "StartCrawlerSchedule": {"input": {"type": "structure", "required": ["CrawlerName"], "members": {"CrawlerName": {}}}, "output": {"type": "structure", "members": {}}}, "StartDataQualityRuleRecommendationRun": {"input": {"type": "structure", "required": ["DataSource", "Role"], "members": {"DataSource": {"shape": "S3z"}, "Role": {}, "NumberOfWorkers": {"type": "integer"}, "Timeout": {"type": "integer"}, "CreatedRulesetName": {}, "ClientToken": {}}}, "output": {"type": "structure", "members": {"RunId": {}}}, "idempotent": true}, "StartDataQualityRulesetEvaluationRun": {"input": {"type": "structure", "required": ["DataSource", "Role", "RulesetNames"], "members": {"DataSource": {"shape": "S3z"}, "Role": {}, "NumberOfWorkers": {"type": "integer"}, "Timeout": {"type": "integer"}, "ClientToken": {}, "AdditionalRunOptions": {"shape": "Siz"}, "RulesetNames": {"shape": "Sj0"}}}, "output": {"type": "structure", "members": {"RunId": {}}}, "idempotent": true}, "StartExportLabelsTaskRun": {"input": {"type": "structure", "required": ["TransformId", "OutputS3Path"], "members": {"TransformId": {}, "OutputS3Path": {}}}, "output": {"type": "structure", "members": {"TaskRunId": {}}}}, "StartImportLabelsTaskRun": {"input": {"type": "structure", "required": ["TransformId", "InputS3Path"], "members": {"TransformId": {}, "InputS3Path": {}, "ReplaceAllLabels": {"type": "boolean"}}}, "output": {"type": "structure", "members": {"TaskRunId": {}}}}, "StartJobRun": {"input": {"type": "structure", "required": ["JobName"], "members": {"JobName": {}, "JobRunId": {}, "Arguments": {"shape": "S4t"}, "AllocatedCapacity": {"deprecated": true, "deprecatedMessage": "This property is deprecated, use MaxCapacity instead.", "type": "integer"}, "Timeout": {"type": "integer"}, "MaxCapacity": {"type": "double"}, "SecurityConfiguration": {}, "NotificationProperty": {"shape": "S4y"}, "WorkerType": {}, "NumberOfWorkers": {"type": "integer"}, "ExecutionClass": {}}}, "output": {"type": "structure", "members": {"JobRunId": {}}}}, "StartMLEvaluationTaskRun": {"input": {"type": "structure", "required": ["TransformId"], "members": {"TransformId": {}}}, "output": {"type": "structure", "members": {"TaskRunId": {}}}}, "StartMLLabelingSetGenerationTaskRun": {"input": {"type": "structure", "required": ["TransformId", "OutputS3Path"], "members": {"TransformId": {}, "OutputS3Path": {}}}, "output": {"type": "structure", "members": {"TaskRunId": {}}}}, "StartTrigger": {"input": {"type": "structure", "required": ["Name"], "members": {"Name": {}}}, "output": {"type": "structure", "members": {"Name": {}}}}, "StartWorkflowRun": {"input": {"type": "structure", "required": ["Name"], "members": {"Name": {}, "RunProperties": {"shape": "S9y"}}}, "output": {"type": "structure", "members": {"RunId": {}}}}, "StopCrawler": {"input": {"type": "structure", "required": ["Name"], "members": {"Name": {}}}, "output": {"type": "structure", "members": {}}}, "StopCrawlerSchedule": {"input": {"type": "structure", "required": ["CrawlerName"], "members": {"CrawlerName": {}}}, "output": {"type": "structure", "members": {}}}, "StopSession": {"input": {"type": "structure", "required": ["Id"], "members": {"Id": {}, "RequestOrigin": {}}}, "output": {"type": "structure", "members": {"Id": {}}}}, "StopTrigger": {"input": {"type": "structure", "required": ["Name"], "members": {"Name": {}}}, "output": {"type": "structure", "members": {"Name": {}}}}, "StopWorkflowRun": {"input": {"type": "structure", "required": ["Name", "RunId"], "members": {"Name": {}, "RunId": {}}}, "output": {"type": "structure", "members": {}}}, "TagResource": {"input": {"type": "structure", "required": ["ResourceArn", "TagsToAdd"], "members": {"ResourceArn": {}, "TagsToAdd": {"shape": "Sbg"}}}, "output": {"type": "structure", "members": {}}}, "UntagResource": {"input": {"type": "structure", "required": ["ResourceArn", "TagsToRemove"], "members": {"ResourceArn": {}, "TagsToRemove": {"type": "list", "member": {}}}}, "output": {"type": "structure", "members": {}}}, "UpdateBlueprint": {"input": {"type": "structure", "required": ["Name", "BlueprintLocation"], "members": {"Name": {}, "Description": {}, "BlueprintLocation": {}}}, "output": {"type": "structure", "members": {"Name": {}}}}, "UpdateClassifier": {"input": {"type": "structure", "members": {"GrokClassifier": {"type": "structure", "required": ["Name"], "members": {"Name": {}, "Classification": {}, "GrokPattern": {}, "CustomPatterns": {}}}, "XMLClassifier": {"type": "structure", "required": ["Name"], "members": {"Name": {}, "Classification": {}, "RowTag": {}}}, "JsonClassifier": {"type": "structure", "required": ["Name"], "members": {"Name": {}, "JsonPath": {}}}, "CsvClassifier": {"type": "structure", "required": ["Name"], "members": {"Name": {}, "Delimiter": {}, "QuoteSymbol": {}, "ContainsHeader": {}, "Header": {"shape": "Sbx"}, "DisableValueTrimming": {"type": "boolean"}, "AllowSingleColumn": {"type": "boolean"}, "CustomDatatypeConfigured": {"type": "boolean"}, "CustomDatatypes": {"shape": "Sby"}}}}}, "output": {"type": "structure", "members": {}}}, "UpdateColumnStatisticsForPartition": {"input": {"type": "structure", "required": ["DatabaseName", "TableName", "PartitionValues", "ColumnStatisticsList"], "members": {"CatalogId": {}, "DatabaseName": {}, "TableName": {}, "PartitionValues": {"shape": "S6"}, "ColumnStatisticsList": {"shape": "Ssn"}}}, "output": {"type": "structure", "members": {"Errors": {"shape": "Ssp"}}}}, "UpdateColumnStatisticsForTable": {"input": {"type": "structure", "required": ["DatabaseName", "TableName", "ColumnStatisticsList"], "members": {"CatalogId": {}, "DatabaseName": {}, "TableName": {}, "ColumnStatisticsList": {"shape": "Ssn"}}}, "output": {"type": "structure", "members": {"Errors": {"shape": "Ssp"}}}}, "UpdateConnection": {"input": {"type": "structure", "required": ["Name", "ConnectionInput"], "members": {"CatalogId": {}, "Name": {}, "ConnectionInput": {"shape": "Sc1"}}}, "output": {"type": "structure", "members": {}}}, "UpdateCrawler": {"input": {"type": "structure", "required": ["Name"], "members": {"Name": {}, "Role": {}, "DatabaseName": {}, "Description": {}, "Targets": {"shape": "S2b"}, "Schedule": {}, "Classifiers": {"shape": "S2y"}, "TablePrefix": {}, "SchemaChangePolicy": {"shape": "S31"}, "RecrawlPolicy": {"shape": "S2z"}, "LineageConfiguration": {"shape": "S34"}, "LakeFormationConfiguration": {"shape": "S3k"}, "Configuration": {}, "CrawlerSecurityConfiguration": {}}}, "output": {"type": "structure", "members": {}}}, "UpdateCrawlerSchedule": {"input": {"type": "structure", "required": ["CrawlerName"], "members": {"CrawlerName": {}, "Schedule": {}}}, "output": {"type": "structure", "members": {}}}, "UpdateDataQualityRuleset": {"input": {"type": "structure", "required": ["Name"], "members": {"Name": {}, "UpdatedName": {}, "Description": {}, "Ruleset": {}}}, "output": {"type": "structure", "members": {"Name": {}, "Description": {}, "Ruleset": {}}}}, "UpdateDatabase": {"input": {"type": "structure", "required": ["Name", "DatabaseInput"], "members": {"CatalogId": {}, "Name": {}, "DatabaseInput": {"shape": "<PERSON>i"}}}, "output": {"type": "structure", "members": {}}}, "UpdateDevEndpoint": {"input": {"type": "structure", "required": ["EndpointName"], "members": {"EndpointName": {}, "PublicKey": {}, "AddPublicKeys": {"shape": "S4f"}, "DeletePublicKeys": {"shape": "S4f"}, "CustomLibraries": {"type": "structure", "members": {"ExtraPythonLibsS3Path": {}, "ExtraJarsS3Path": {}}}, "UpdateEtlLibraries": {"type": "boolean"}, "DeleteArguments": {"shape": "S4b"}, "AddArguments": {"shape": "S4g"}}}, "output": {"type": "structure", "members": {}}}, "UpdateJob": {"input": {"type": "structure", "required": ["JobName", "JobUpdate"], "members": {"JobName": {}, "JobUpdate": {"type": "structure", "members": {"Description": {}, "LogUri": {}, "Role": {}, "ExecutionProperty": {"shape": "S4o"}, "Command": {"shape": "S4q"}, "DefaultArguments": {"shape": "S4t"}, "NonOverridableArguments": {"shape": "S4t"}, "Connections": {"shape": "S4u"}, "MaxRetries": {"type": "integer"}, "AllocatedCapacity": {"deprecated": true, "deprecatedMessage": "This property is deprecated, use MaxCapacity instead.", "type": "integer"}, "Timeout": {"type": "integer"}, "MaxCapacity": {"type": "double"}, "WorkerType": {}, "NumberOfWorkers": {"type": "integer"}, "SecurityConfiguration": {}, "NotificationProperty": {"shape": "S4y"}, "GlueVersion": {}, "CodeGenConfigurationNodes": {"shape": "S50"}, "ExecutionClass": {}, "SourceControlDetails": {"shape": "S91"}}}}}, "output": {"type": "structure", "members": {"JobName": {}}}}, "UpdateJobFromSourceControl": {"input": {"type": "structure", "members": {"JobName": {}, "Provider": {}, "RepositoryName": {}, "RepositoryOwner": {}, "BranchName": {}, "Folder": {}, "CommitId": {}, "AuthStrategy": {}, "AuthToken": {}}}, "output": {"type": "structure", "members": {"JobName": {}}}}, "UpdateMLTransform": {"input": {"type": "structure", "required": ["TransformId"], "members": {"TransformId": {}, "Name": {}, "Description": {}, "Parameters": {"shape": "<PERSON><PERSON>"}, "Role": {}, "GlueVersion": {}, "MaxCapacity": {"type": "double"}, "WorkerType": {}, "NumberOfWorkers": {"type": "integer"}, "Timeout": {"type": "integer"}, "MaxRetries": {"type": "integer"}}}, "output": {"type": "structure", "members": {"TransformId": {}}}}, "UpdatePartition": {"input": {"type": "structure", "required": ["DatabaseName", "TableName", "PartitionValueList", "PartitionInput"], "members": {"CatalogId": {}, "DatabaseName": {}, "TableName": {}, "PartitionValueList": {"shape": "Sav"}, "PartitionInput": {"shape": "S5"}}}, "output": {"type": "structure", "members": {}}}, "UpdateRegistry": {"input": {"type": "structure", "required": ["RegistryId", "Description"], "members": {"RegistryId": {"shape": "Sdf"}, "Description": {}}}, "output": {"type": "structure", "members": {"RegistryName": {}, "RegistryArn": {}}}}, "UpdateSchema": {"input": {"type": "structure", "required": ["SchemaId"], "members": {"SchemaId": {"shape": "Sw"}, "SchemaVersionNumber": {"shape": "Sm7"}, "Compatibility": {}, "Description": {}}}, "output": {"type": "structure", "members": {"SchemaArn": {}, "SchemaName": {}, "RegistryName": {}}}}, "UpdateSourceControlFromJob": {"input": {"type": "structure", "members": {"JobName": {}, "Provider": {}, "RepositoryName": {}, "RepositoryOwner": {}, "BranchName": {}, "Folder": {}, "CommitId": {}, "AuthStrategy": {}, "AuthToken": {}}}, "output": {"type": "structure", "members": {"JobName": {}}}}, "UpdateTable": {"input": {"type": "structure", "required": ["DatabaseName", "TableInput"], "members": {"CatalogId": {}, "DatabaseName": {}, "TableInput": {"shape": "<PERSON>l"}, "SkipArchive": {"type": "boolean"}, "TransactionId": {}, "VersionId": {}}}, "output": {"type": "structure", "members": {}}}, "UpdateTrigger": {"input": {"type": "structure", "required": ["Name", "TriggerUpdate"], "members": {"Name": {}, "TriggerUpdate": {"type": "structure", "members": {"Name": {}, "Description": {}, "Schedule": {}, "Actions": {"shape": "S9h"}, "Predicate": {"shape": "S9j"}, "EventBatchingCondition": {"shape": "S9q"}}}}}, "output": {"type": "structure", "members": {"Trigger": {"shape": "S9d"}}}}, "UpdateUserDefinedFunction": {"input": {"type": "structure", "required": ["DatabaseName", "FunctionName", "FunctionInput"], "members": {"CatalogId": {}, "DatabaseName": {}, "FunctionName": {}, "FunctionInput": {"shape": "Sev"}}}, "output": {"type": "structure", "members": {}}}, "UpdateWorkflow": {"input": {"type": "structure", "required": ["Name"], "members": {"Name": {}, "Description": {}, "DefaultRunProperties": {"shape": "S9y"}, "MaxConcurrentRuns": {"type": "integer"}}}, "output": {"type": "structure", "members": {"Name": {}}}}}, "shapes": {"S5": {"type": "structure", "members": {"Values": {"shape": "S6"}, "LastAccessTime": {"type": "timestamp"}, "StorageDescriptor": {"shape": "S9"}, "Parameters": {"shape": "Se"}, "LastAnalyzedTime": {"type": "timestamp"}}}, "S6": {"type": "list", "member": {}}, "S9": {"type": "structure", "members": {"Columns": {"shape": "Sa"}, "Location": {}, "AdditionalLocations": {"type": "list", "member": {}}, "InputFormat": {}, "OutputFormat": {}, "Compressed": {"type": "boolean"}, "NumberOfBuckets": {"type": "integer"}, "SerdeInfo": {"type": "structure", "members": {"Name": {}, "SerializationLibrary": {}, "Parameters": {"shape": "Se"}}}, "BucketColumns": {"shape": "Sn"}, "SortColumns": {"type": "list", "member": {"type": "structure", "required": ["Column", "SortOrder"], "members": {"Column": {}, "SortOrder": {"type": "integer"}}}}, "Parameters": {"shape": "Se"}, "SkewedInfo": {"type": "structure", "members": {"SkewedColumnNames": {"shape": "Sn"}, "SkewedColumnValues": {"type": "list", "member": {}}, "SkewedColumnValueLocationMaps": {"type": "map", "key": {}, "value": {}}}}, "StoredAsSubDirectories": {"type": "boolean"}, "SchemaReference": {"type": "structure", "members": {"SchemaId": {"shape": "Sw"}, "SchemaVersionId": {}, "SchemaVersionNumber": {"type": "long"}}}}}, "Sa": {"type": "list", "member": {"type": "structure", "required": ["Name"], "members": {"Name": {}, "Type": {}, "Comment": {}, "Parameters": {"shape": "Se"}}}}, "Se": {"type": "map", "key": {}, "value": {}}, "Sn": {"type": "list", "member": {}}, "Sw": {"type": "structure", "members": {"SchemaArn": {}, "SchemaName": {}, "RegistryName": {}}}, "S12": {"type": "list", "member": {"type": "structure", "members": {"PartitionValues": {"shape": "S6"}, "ErrorDetail": {"shape": "S14"}}}}, "S14": {"type": "structure", "members": {"ErrorCode": {}, "ErrorMessage": {}}}, "S1c": {"type": "structure", "required": ["Values"], "members": {"Values": {"shape": "S6"}}}, "S1w": {"type": "structure", "members": {"Name": {}, "Description": {}, "CreatedOn": {"type": "timestamp"}, "LastModifiedOn": {"type": "timestamp"}, "ParameterSpec": {}, "BlueprintLocation": {}, "BlueprintServiceLocation": {}, "Status": {}, "ErrorMessage": {}, "LastActiveDefinition": {"type": "structure", "members": {"Description": {}, "LastModifiedOn": {"type": "timestamp"}, "ParameterSpec": {}, "BlueprintLocation": {}, "BlueprintServiceLocation": {}}}}}, "S24": {"type": "list", "member": {}}, "S26": {"type": "list", "member": {}}, "S28": {"type": "list", "member": {"shape": "S29"}}, "S29": {"type": "structure", "members": {"Name": {}, "Role": {}, "Targets": {"shape": "S2b"}, "DatabaseName": {}, "Description": {}, "Classifiers": {"shape": "S2y"}, "RecrawlPolicy": {"shape": "S2z"}, "SchemaChangePolicy": {"shape": "S31"}, "LineageConfiguration": {"shape": "S34"}, "State": {}, "TablePrefix": {}, "Schedule": {"type": "structure", "members": {"ScheduleExpression": {}, "State": {}}}, "CrawlElapsedTime": {"type": "long"}, "CreationTime": {"type": "timestamp"}, "LastUpdated": {"type": "timestamp"}, "LastCrawl": {"type": "structure", "members": {"Status": {}, "ErrorMessage": {}, "LogGroup": {}, "LogStream": {}, "MessagePrefix": {}, "StartTime": {"type": "timestamp"}}}, "Version": {"type": "long"}, "Configuration": {}, "CrawlerSecurityConfiguration": {}, "LakeFormationConfiguration": {"shape": "S3k"}}}, "S2b": {"type": "structure", "members": {"S3Targets": {"type": "list", "member": {"type": "structure", "members": {"Path": {}, "Exclusions": {"shape": "S2f"}, "ConnectionName": {}, "SampleSize": {"type": "integer"}, "EventQueueArn": {}, "DlqEventQueueArn": {}}}}, "JdbcTargets": {"type": "list", "member": {"type": "structure", "members": {"ConnectionName": {}, "Path": {}, "Exclusions": {"shape": "S2f"}, "EnableAdditionalMetadata": {"type": "list", "member": {}}}}}, "MongoDBTargets": {"type": "list", "member": {"type": "structure", "members": {"ConnectionName": {}, "Path": {}, "ScanAll": {"type": "boolean"}}}}, "DynamoDBTargets": {"type": "list", "member": {"type": "structure", "members": {"Path": {}, "scanAll": {"type": "boolean"}, "scanRate": {"type": "double"}}}}, "CatalogTargets": {"type": "list", "member": {"type": "structure", "required": ["DatabaseName", "Tables"], "members": {"DatabaseName": {}, "Tables": {"type": "list", "member": {}}, "ConnectionName": {}, "EventQueueArn": {}, "DlqEventQueueArn": {}}}}, "DeltaTargets": {"type": "list", "member": {"type": "structure", "members": {"DeltaTables": {"shape": "S2f"}, "ConnectionName": {}, "WriteManifest": {"type": "boolean"}, "CreateNativeDeltaTable": {"type": "boolean"}}}}}}, "S2f": {"type": "list", "member": {}}, "S2y": {"type": "list", "member": {}}, "S2z": {"type": "structure", "members": {"RecrawlBehavior": {}}}, "S31": {"type": "structure", "members": {"UpdateBehavior": {}, "DeleteBehavior": {}}}, "S34": {"type": "structure", "members": {"CrawlerLineageSettings": {}}}, "S3k": {"type": "structure", "members": {"UseLakeFormationCredentials": {"type": "boolean"}, "AccountId": {}}}, "S3n": {"type": "list", "member": {}}, "S3p": {"type": "list", "member": {"type": "structure", "required": ["Name", "RegexString"], "members": {"Name": {}, "RegexString": {}, "ContextWords": {"shape": "S3r"}}}}, "S3r": {"type": "list", "member": {}}, "S3t": {"type": "list", "member": {}}, "S3z": {"type": "structure", "required": ["GlueTable"], "members": {"GlueTable": {"shape": "S40"}}}, "S40": {"type": "structure", "required": ["DatabaseName", "TableName"], "members": {"DatabaseName": {}, "TableName": {}, "CatalogId": {}, "ConnectionName": {}, "AdditionalOptions": {"type": "map", "key": {}, "value": {}}}}, "S42": {"type": "list", "member": {"type": "structure", "members": {"Name": {}, "Description": {}, "EvaluationMessage": {}, "Result": {}}}}, "S46": {"type": "list", "member": {}}, "S48": {"type": "list", "member": {"shape": "S49"}}, "S49": {"type": "structure", "members": {"EndpointName": {}, "RoleArn": {}, "SecurityGroupIds": {"shape": "S4b"}, "SubnetId": {}, "YarnEndpointAddress": {}, "PrivateAddress": {}, "ZeppelinRemoteSparkInterpreterPort": {"type": "integer"}, "PublicAddress": {}, "Status": {}, "WorkerType": {}, "GlueVersion": {}, "NumberOfWorkers": {"type": "integer"}, "NumberOfNodes": {"type": "integer"}, "AvailabilityZone": {}, "VpcId": {}, "ExtraPythonLibsS3Path": {}, "ExtraJarsS3Path": {}, "FailureReason": {}, "LastUpdateStatus": {}, "CreatedTimestamp": {"type": "timestamp"}, "LastModifiedTimestamp": {"type": "timestamp"}, "PublicKey": {}, "PublicKeys": {"shape": "S4f"}, "SecurityConfiguration": {}, "Arguments": {"shape": "S4g"}}}, "S4b": {"type": "list", "member": {}}, "S4f": {"type": "list", "member": {}}, "S4g": {"type": "map", "key": {}, "value": {}}, "S4i": {"type": "list", "member": {}}, "S4k": {"type": "list", "member": {"shape": "S4l"}}, "S4l": {"type": "structure", "members": {"Name": {}, "Description": {}, "LogUri": {}, "Role": {}, "CreatedOn": {"type": "timestamp"}, "LastModifiedOn": {"type": "timestamp"}, "ExecutionProperty": {"shape": "S4o"}, "Command": {"shape": "S4q"}, "DefaultArguments": {"shape": "S4t"}, "NonOverridableArguments": {"shape": "S4t"}, "Connections": {"shape": "S4u"}, "MaxRetries": {"type": "integer"}, "AllocatedCapacity": {"deprecated": true, "deprecatedMessage": "This property is deprecated, use MaxCapacity instead.", "type": "integer"}, "Timeout": {"type": "integer"}, "MaxCapacity": {"type": "double"}, "WorkerType": {}, "NumberOfWorkers": {"type": "integer"}, "SecurityConfiguration": {}, "NotificationProperty": {"shape": "S4y"}, "GlueVersion": {}, "CodeGenConfigurationNodes": {"shape": "S50"}, "ExecutionClass": {}, "SourceControlDetails": {"shape": "S91"}}}, "S4o": {"type": "structure", "members": {"MaxConcurrentRuns": {"type": "integer"}}}, "S4q": {"type": "structure", "members": {"Name": {}, "ScriptLocation": {}, "PythonVersion": {}}}, "S4t": {"type": "map", "key": {}, "value": {}}, "S4u": {"type": "structure", "members": {"Connections": {"shape": "S4v"}}}, "S4v": {"type": "list", "member": {}}, "S4y": {"type": "structure", "members": {"NotifyDelayAfter": {"type": "integer"}}}, "S50": {"type": "map", "key": {}, "value": {"type": "structure", "members": {"AthenaConnectorSource": {"type": "structure", "required": ["Name", "ConnectionName", "ConnectorName", "ConnectionType", "<PERSON><PERSON>aName"], "members": {"Name": {}, "ConnectionName": {}, "ConnectorName": {}, "ConnectionType": {}, "ConnectionTable": {}, "SchemaName": {}, "OutputSchemas": {"shape": "S57"}}}, "JDBCConnectorSource": {"type": "structure", "required": ["Name", "ConnectionName", "ConnectorName", "ConnectionType"], "members": {"Name": {}, "ConnectionName": {}, "ConnectorName": {}, "ConnectionType": {}, "AdditionalOptions": {"type": "structure", "members": {"FilterPredicate": {}, "PartitionColumn": {}, "LowerBound": {"type": "long"}, "UpperBound": {"type": "long"}, "NumPartitions": {"type": "long"}, "JobBookmarkKeys": {"shape": "S5f"}, "JobBookmarkKeysSortOrder": {}, "DataTypeMapping": {"type": "map", "key": {}, "value": {}}}}, "ConnectionTable": {}, "Query": {}, "OutputSchemas": {"shape": "S57"}}}, "SparkConnectorSource": {"type": "structure", "required": ["Name", "ConnectionName", "ConnectorName", "ConnectionType"], "members": {"Name": {}, "ConnectionName": {}, "ConnectorName": {}, "ConnectionType": {}, "AdditionalOptions": {"shape": "S5l"}, "OutputSchemas": {"shape": "S57"}}}, "CatalogSource": {"type": "structure", "required": ["Name", "Database", "Table"], "members": {"Name": {}, "Database": {}, "Table": {}}}, "RedshiftSource": {"type": "structure", "required": ["Name", "Database", "Table"], "members": {"Name": {}, "Database": {}, "Table": {}, "RedshiftTmpDir": {}, "TmpDirIAMRole": {}}}, "S3CatalogSource": {"type": "structure", "required": ["Name", "Database", "Table"], "members": {"Name": {}, "Database": {}, "Table": {}, "PartitionPredicate": {}, "AdditionalOptions": {"shape": "S5p"}}}, "S3CsvSource": {"type": "structure", "required": ["Name", "Paths", "Separator", "QuoteChar"], "members": {"Name": {}, "Paths": {"shape": "S5f"}, "CompressionType": {}, "Exclusions": {"shape": "S5f"}, "GroupSize": {}, "GroupFiles": {}, "Recurse": {"type": "boolean"}, "MaxBand": {"type": "integer"}, "MaxFilesInBand": {"type": "integer"}, "AdditionalOptions": {"shape": "S5v"}, "Separator": {}, "Escaper": {}, "QuoteChar": {}, "Multiline": {"type": "boolean"}, "WithHeader": {"type": "boolean"}, "WriteHeader": {"type": "boolean"}, "SkipFirst": {"type": "boolean"}, "OptimizePerformance": {"type": "boolean"}, "OutputSchemas": {"shape": "S57"}}}, "S3JsonSource": {"type": "structure", "required": ["Name", "Paths"], "members": {"Name": {}, "Paths": {"shape": "S5f"}, "CompressionType": {}, "Exclusions": {"shape": "S5f"}, "GroupSize": {}, "GroupFiles": {}, "Recurse": {"type": "boolean"}, "MaxBand": {"type": "integer"}, "MaxFilesInBand": {"type": "integer"}, "AdditionalOptions": {"shape": "S5v"}, "JsonPath": {}, "Multiline": {"type": "boolean"}, "OutputSchemas": {"shape": "S57"}}}, "S3ParquetSource": {"type": "structure", "required": ["Name", "Paths"], "members": {"Name": {}, "Paths": {"shape": "S5f"}, "CompressionType": {}, "Exclusions": {"shape": "S5f"}, "GroupSize": {}, "GroupFiles": {}, "Recurse": {"type": "boolean"}, "MaxBand": {"type": "integer"}, "MaxFilesInBand": {"type": "integer"}, "AdditionalOptions": {"shape": "S5v"}, "OutputSchemas": {"shape": "S57"}}}, "RelationalCatalogSource": {"type": "structure", "required": ["Name", "Database", "Table"], "members": {"Name": {}, "Database": {}, "Table": {}}}, "DynamoDBCatalogSource": {"type": "structure", "required": ["Name", "Database", "Table"], "members": {"Name": {}, "Database": {}, "Table": {}}}, "JDBCConnectorTarget": {"type": "structure", "required": ["Name", "Inputs", "ConnectionName", "ConnectionTable", "ConnectorName", "ConnectionType"], "members": {"Name": {}, "Inputs": {"shape": "S65"}, "ConnectionName": {}, "ConnectionTable": {}, "ConnectorName": {}, "ConnectionType": {}, "AdditionalOptions": {"shape": "S5l"}, "OutputSchemas": {"shape": "S57"}}}, "SparkConnectorTarget": {"type": "structure", "required": ["Name", "Inputs", "ConnectionName", "ConnectorName", "ConnectionType"], "members": {"Name": {}, "Inputs": {"shape": "S65"}, "ConnectionName": {}, "ConnectorName": {}, "ConnectionType": {}, "AdditionalOptions": {"shape": "S5l"}, "OutputSchemas": {"shape": "S57"}}}, "CatalogTarget": {"type": "structure", "required": ["Name", "Inputs", "Database", "Table"], "members": {"Name": {}, "Inputs": {"shape": "S65"}, "Database": {}, "Table": {}}}, "RedshiftTarget": {"type": "structure", "required": ["Name", "Inputs", "Database", "Table"], "members": {"Name": {}, "Inputs": {"shape": "S65"}, "Database": {}, "Table": {}, "RedshiftTmpDir": {}, "TmpDirIAMRole": {}, "UpsertRedshiftOptions": {"type": "structure", "members": {"TableLocation": {}, "ConnectionName": {}, "UpsertKeys": {"type": "list", "member": {}}}}}}, "S3CatalogTarget": {"type": "structure", "required": ["Name", "Inputs", "Table", "Database"], "members": {"Name": {}, "Inputs": {"shape": "S65"}, "PartitionKeys": {"shape": "S6c"}, "Table": {}, "Database": {}, "SchemaChangePolicy": {"shape": "S6d"}}}, "S3GlueParquetTarget": {"type": "structure", "required": ["Name", "Inputs", "Path"], "members": {"Name": {}, "Inputs": {"shape": "S65"}, "PartitionKeys": {"shape": "S6c"}, "Path": {}, "Compression": {}, "SchemaChangePolicy": {"shape": "S6g"}}}, "S3DirectTarget": {"type": "structure", "required": ["Name", "Inputs", "Path", "Format"], "members": {"Name": {}, "Inputs": {"shape": "S65"}, "PartitionKeys": {"shape": "S6c"}, "Path": {}, "Compression": {}, "Format": {}, "SchemaChangePolicy": {"shape": "S6g"}}}, "ApplyMapping": {"type": "structure", "required": ["Name", "Inputs", "Mapping"], "members": {"Name": {}, "Inputs": {"shape": "S65"}, "Mapping": {"shape": "S6k"}}}, "SelectFields": {"type": "structure", "required": ["Name", "Inputs", "Paths"], "members": {"Name": {}, "Inputs": {"shape": "S65"}, "Paths": {"shape": "S6c"}}}, "DropFields": {"type": "structure", "required": ["Name", "Inputs", "Paths"], "members": {"Name": {}, "Inputs": {"shape": "S65"}, "Paths": {"shape": "S6c"}}}, "RenameField": {"type": "structure", "required": ["Name", "Inputs", "SourcePath", "TargetPath"], "members": {"Name": {}, "Inputs": {"shape": "S65"}, "SourcePath": {"shape": "S5f"}, "TargetPath": {"shape": "S5f"}}}, "Spigot": {"type": "structure", "required": ["Name", "Inputs", "Path"], "members": {"Name": {}, "Inputs": {"shape": "S65"}, "Path": {}, "Topk": {"type": "integer"}, "Prob": {"type": "double"}}}, "Join": {"type": "structure", "required": ["Name", "Inputs", "JoinType", "Columns"], "members": {"Name": {}, "Inputs": {"shape": "S6t"}, "JoinType": {}, "Columns": {"type": "list", "member": {"type": "structure", "required": ["From", "Keys"], "members": {"From": {}, "Keys": {"shape": "S6c"}}}}}}, "SplitFields": {"type": "structure", "required": ["Name", "Inputs", "Paths"], "members": {"Name": {}, "Inputs": {"shape": "S65"}, "Paths": {"shape": "S6c"}}}, "SelectFromCollection": {"type": "structure", "required": ["Name", "Inputs", "Index"], "members": {"Name": {}, "Inputs": {"shape": "S65"}, "Index": {"type": "integer"}}}, "FillMissingValues": {"type": "structure", "required": ["Name", "Inputs", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], "members": {"Name": {}, "Inputs": {"shape": "S65"}, "ImputedPath": {}, "FilledPath": {}}}, "Filter": {"type": "structure", "required": ["Name", "Inputs", "LogicalOperator", "Filters"], "members": {"Name": {}, "Inputs": {"shape": "S65"}, "LogicalOperator": {}, "Filters": {"type": "list", "member": {"type": "structure", "required": ["Operation", "Values"], "members": {"Operation": {}, "Negated": {"type": "boolean"}, "Values": {"type": "list", "member": {"type": "structure", "required": ["Type", "Value"], "members": {"Type": {}, "Value": {"shape": "S5f"}}}}}}}}}, "CustomCode": {"type": "structure", "required": ["Name", "Inputs", "Code", "ClassName"], "members": {"Name": {}, "Inputs": {"shape": "S7a"}, "Code": {}, "ClassName": {}, "OutputSchemas": {"shape": "S57"}}}, "SparkSQL": {"type": "structure", "required": ["Name", "Inputs", "SqlQuery", "SqlAliases"], "members": {"Name": {}, "Inputs": {"shape": "S7a"}, "SqlQuery": {}, "SqlAliases": {"type": "list", "member": {"type": "structure", "required": ["From", "<PERSON><PERSON>"], "members": {"From": {}, "Alias": {}}}}, "OutputSchemas": {"shape": "S57"}}}, "DirectKinesisSource": {"type": "structure", "required": ["Name"], "members": {"Name": {}, "WindowSize": {"type": "integer"}, "DetectSchema": {"type": "boolean"}, "StreamingOptions": {"shape": "S7h"}, "DataPreviewOptions": {"shape": "S7j"}}}, "DirectKafkaSource": {"type": "structure", "required": ["Name"], "members": {"Name": {}, "StreamingOptions": {"shape": "S7n"}, "WindowSize": {"type": "integer"}, "DetectSchema": {"type": "boolean"}, "DataPreviewOptions": {"shape": "S7j"}}}, "CatalogKinesisSource": {"type": "structure", "required": ["Name", "Table", "Database"], "members": {"Name": {}, "WindowSize": {"type": "integer"}, "DetectSchema": {"type": "boolean"}, "Table": {}, "Database": {}, "StreamingOptions": {"shape": "S7h"}, "DataPreviewOptions": {"shape": "S7j"}}}, "CatalogKafkaSource": {"type": "structure", "required": ["Name", "Table", "Database"], "members": {"Name": {}, "WindowSize": {"type": "integer"}, "DetectSchema": {"type": "boolean"}, "Table": {}, "Database": {}, "StreamingOptions": {"shape": "S7n"}, "DataPreviewOptions": {"shape": "S7j"}}}, "DropNullFields": {"type": "structure", "required": ["Name", "Inputs"], "members": {"Name": {}, "Inputs": {"shape": "S65"}, "NullCheckBoxList": {"type": "structure", "members": {"IsEmpty": {"type": "boolean"}, "IsNullString": {"type": "boolean"}, "IsNegOne": {"type": "boolean"}}}, "NullTextList": {"type": "list", "member": {"type": "structure", "required": ["Value", "Datatype"], "members": {"Value": {}, "Datatype": {"type": "structure", "required": ["Id", "Label"], "members": {"Id": {}, "Label": {}}}}}}}}, "Merge": {"type": "structure", "required": ["Name", "Inputs", "Source", "<PERSON><PERSON><PERSON><PERSON>"], "members": {"Name": {}, "Inputs": {"shape": "S6t"}, "Source": {}, "PrimaryKeys": {"shape": "S6c"}}}, "Union": {"type": "structure", "required": ["Name", "Inputs", "UnionType"], "members": {"Name": {}, "Inputs": {"shape": "S6t"}, "UnionType": {}}}, "PIIDetection": {"type": "structure", "required": ["Name", "Inputs", "PiiType", "EntityTypesToDetect"], "members": {"Name": {}, "Inputs": {"shape": "S65"}, "PiiType": {}, "EntityTypesToDetect": {"shape": "S5f"}, "OutputColumnName": {}, "SampleFraction": {"type": "double"}, "ThresholdFraction": {"type": "double"}, "MaskValue": {}}}, "Aggregate": {"type": "structure", "required": ["Name", "Inputs", "Groups", "<PERSON><PERSON>"], "members": {"Name": {}, "Inputs": {"shape": "S65"}, "Groups": {"shape": "S6c"}, "Aggs": {"type": "list", "member": {"type": "structure", "required": ["Column", "AggFunc"], "members": {"Column": {"shape": "S5f"}, "AggFunc": {}}}}}}, "DropDuplicates": {"type": "structure", "required": ["Name", "Inputs"], "members": {"Name": {}, "Inputs": {"shape": "S65"}, "Columns": {"type": "list", "member": {"type": "list", "member": {}}}}}, "GovernedCatalogTarget": {"type": "structure", "required": ["Name", "Inputs", "Table", "Database"], "members": {"Name": {}, "Inputs": {"shape": "S65"}, "PartitionKeys": {"shape": "S6c"}, "Table": {}, "Database": {}, "SchemaChangePolicy": {"shape": "S6d"}}}, "GovernedCatalogSource": {"type": "structure", "required": ["Name", "Database", "Table"], "members": {"Name": {}, "Database": {}, "Table": {}, "PartitionPredicate": {}, "AdditionalOptions": {"shape": "S5p"}}}, "MicrosoftSQLServerCatalogSource": {"type": "structure", "required": ["Name", "Database", "Table"], "members": {"Name": {}, "Database": {}, "Table": {}}}, "MySQLCatalogSource": {"type": "structure", "required": ["Name", "Database", "Table"], "members": {"Name": {}, "Database": {}, "Table": {}}}, "OracleSQLCatalogSource": {"type": "structure", "required": ["Name", "Database", "Table"], "members": {"Name": {}, "Database": {}, "Table": {}}}, "PostgreSQLCatalogSource": {"type": "structure", "required": ["Name", "Database", "Table"], "members": {"Name": {}, "Database": {}, "Table": {}}}, "MicrosoftSQLServerCatalogTarget": {"type": "structure", "required": ["Name", "Inputs", "Database", "Table"], "members": {"Name": {}, "Inputs": {"shape": "S65"}, "Database": {}, "Table": {}}}, "MySQLCatalogTarget": {"type": "structure", "required": ["Name", "Inputs", "Database", "Table"], "members": {"Name": {}, "Inputs": {"shape": "S65"}, "Database": {}, "Table": {}}}, "OracleSQLCatalogTarget": {"type": "structure", "required": ["Name", "Inputs", "Database", "Table"], "members": {"Name": {}, "Inputs": {"shape": "S65"}, "Database": {}, "Table": {}}}, "PostgreSQLCatalogTarget": {"type": "structure", "required": ["Name", "Inputs", "Database", "Table"], "members": {"Name": {}, "Inputs": {"shape": "S65"}, "Database": {}, "Table": {}}}, "DynamicTransform": {"type": "structure", "required": ["Name", "TransformName", "Inputs", "FunctionName", "Path"], "members": {"Name": {}, "TransformName": {}, "Inputs": {"shape": "S65"}, "Parameters": {"type": "list", "member": {"type": "structure", "required": ["Name", "Type"], "members": {"Name": {}, "Type": {}, "ValidationRule": {}, "ValidationMessage": {}, "Value": {"shape": "S5f"}, "ListType": {}, "IsOptional": {"type": "boolean"}}}}, "FunctionName": {}, "Path": {}, "Version": {}}}, "EvaluateDataQuality": {"type": "structure", "required": ["Name", "Inputs", "Ruleset"], "members": {"Name": {}, "Inputs": {"shape": "S65"}, "Ruleset": {}, "Output": {}, "PublishingOptions": {"type": "structure", "members": {"EvaluationContext": {}, "ResultsS3Prefix": {}, "CloudWatchMetricsEnabled": {"type": "boolean"}, "ResultsPublishingEnabled": {"type": "boolean"}}}, "StopJobOnFailureOptions": {"type": "structure", "members": {"StopJobOnFailureTiming": {}}}}}, "S3CatalogHudiSource": {"type": "structure", "required": ["Name", "Database", "Table"], "members": {"Name": {}, "Database": {}, "Table": {}, "AdditionalHudiOptions": {"shape": "S5l"}, "OutputSchemas": {"shape": "S57"}}}, "CatalogHudiSource": {"type": "structure", "required": ["Name", "Database", "Table"], "members": {"Name": {}, "Database": {}, "Table": {}, "AdditionalHudiOptions": {"shape": "S5l"}, "OutputSchemas": {"shape": "S57"}}}, "S3HudiSource": {"type": "structure", "required": ["Name", "Paths"], "members": {"Name": {}, "Paths": {"shape": "S5f"}, "AdditionalHudiOptions": {"shape": "S5l"}, "AdditionalOptions": {"shape": "S5v"}, "OutputSchemas": {"shape": "S57"}}}, "S3HudiCatalogTarget": {"type": "structure", "required": ["Name", "Inputs", "Table", "Database", "AdditionalOptions"], "members": {"Name": {}, "Inputs": {"shape": "S65"}, "PartitionKeys": {"shape": "S6c"}, "Table": {}, "Database": {}, "AdditionalOptions": {"shape": "S5l"}, "SchemaChangePolicy": {"shape": "S6d"}}}, "S3HudiDirectTarget": {"type": "structure", "required": ["Name", "Inputs", "Path", "Compression", "Format", "AdditionalOptions"], "members": {"Name": {}, "Inputs": {"shape": "S65"}, "Path": {}, "Compression": {}, "PartitionKeys": {"shape": "S6c"}, "Format": {}, "AdditionalOptions": {"shape": "S5l"}, "SchemaChangePolicy": {"shape": "S6g"}}}}}, "sensitive": true}, "S57": {"type": "list", "member": {"type": "structure", "members": {"Columns": {"type": "list", "member": {"type": "structure", "required": ["Name"], "members": {"Name": {}, "Type": {}}}}}}}, "S5f": {"type": "list", "member": {}}, "S5l": {"type": "map", "key": {}, "value": {}}, "S5p": {"type": "structure", "members": {"BoundedSize": {"type": "long"}, "BoundedFiles": {"type": "long"}}}, "S5v": {"type": "structure", "members": {"BoundedSize": {"type": "long"}, "BoundedFiles": {"type": "long"}, "EnableSamplePath": {"type": "boolean"}, "SamplePath": {}}}, "S65": {"type": "list", "member": {}}, "S6c": {"type": "list", "member": {"shape": "S5f"}}, "S6d": {"type": "structure", "members": {"EnableUpdateCatalog": {"type": "boolean"}, "UpdateBehavior": {}}}, "S6g": {"type": "structure", "members": {"EnableUpdateCatalog": {"type": "boolean"}, "UpdateBehavior": {}, "Table": {}, "Database": {}}}, "S6k": {"type": "list", "member": {"type": "structure", "members": {"ToKey": {}, "FromPath": {"shape": "S5f"}, "FromType": {}, "ToType": {}, "Dropped": {"type": "boolean"}, "Children": {"shape": "S6k"}}}}, "S6t": {"type": "list", "member": {}}, "S7a": {"type": "list", "member": {}}, "S7h": {"type": "structure", "members": {"EndpointUrl": {}, "StreamName": {}, "Classification": {}, "Delimiter": {}, "StartingPosition": {}, "MaxFetchTimeInMs": {"type": "long"}, "MaxFetchRecordsPerShard": {"type": "long"}, "MaxRecordPerRead": {"type": "long"}, "AddIdleTimeBetweenReads": {"type": "boolean"}, "IdleTimeBetweenReadsInMs": {"type": "long"}, "DescribeShardInterval": {"type": "long"}, "NumRetries": {"type": "integer"}, "RetryIntervalMs": {"type": "long"}, "MaxRetryIntervalMs": {"type": "long"}, "AvoidEmptyBatches": {"type": "boolean"}, "StreamArn": {}, "RoleArn": {}, "RoleSessionName": {}, "AddRecordTimestamp": {}, "EmitConsumerLagMetrics": {}}}, "S7j": {"type": "structure", "members": {"PollingTime": {"type": "long"}, "RecordPollingLimit": {"type": "long"}}}, "S7n": {"type": "structure", "members": {"BootstrapServers": {}, "SecurityProtocol": {}, "ConnectionName": {}, "TopicName": {}, "Assign": {}, "SubscribePattern": {}, "Classification": {}, "Delimiter": {}, "StartingOffsets": {}, "EndingOffsets": {}, "PollTimeoutMs": {"type": "long"}, "NumRetries": {"type": "integer"}, "RetryIntervalMs": {"type": "long"}, "MaxOffsetsPerTrigger": {"type": "long"}, "MinPartitions": {"type": "integer"}, "IncludeHeaders": {"type": "boolean"}, "AddRecordTimestamp": {}, "EmitConsumerLagMetrics": {}}}, "S91": {"type": "structure", "members": {"Provider": {}, "Repository": {}, "Owner": {}, "Branch": {}, "Folder": {}, "LastCommitId": {}, "AuthStrategy": {}, "AuthToken": {}}}, "S95": {"type": "list", "member": {"shape": "S1c"}}, "S97": {"type": "list", "member": {"shape": "S98"}}, "S98": {"type": "structure", "members": {"Values": {"shape": "S6"}, "DatabaseName": {}, "TableName": {}, "CreationTime": {"type": "timestamp"}, "LastAccessTime": {"type": "timestamp"}, "StorageDescriptor": {"shape": "S9"}, "Parameters": {"shape": "Se"}, "LastAnalyzedTime": {"type": "timestamp"}, "CatalogId": {}}}, "S9a": {"type": "list", "member": {}}, "S9c": {"type": "list", "member": {"shape": "S9d"}}, "S9d": {"type": "structure", "members": {"Name": {}, "WorkflowName": {}, "Id": {}, "Type": {}, "State": {}, "Description": {}, "Schedule": {}, "Actions": {"shape": "S9h"}, "Predicate": {"shape": "S9j"}, "EventBatchingCondition": {"shape": "S9q"}}}, "S9h": {"type": "list", "member": {"type": "structure", "members": {"JobName": {}, "Arguments": {"shape": "S4t"}, "Timeout": {"type": "integer"}, "SecurityConfiguration": {}, "NotificationProperty": {"shape": "S4y"}, "CrawlerName": {}}}}, "S9j": {"type": "structure", "members": {"Logical": {}, "Conditions": {"type": "list", "member": {"type": "structure", "members": {"LogicalOperator": {}, "JobName": {}, "State": {}, "CrawlerName": {}, "CrawlState": {}}}}}}, "S9q": {"type": "structure", "required": ["BatchSize"], "members": {"BatchSize": {"type": "integer"}, "BatchWindow": {"type": "integer"}}}, "S9u": {"type": "list", "member": {}}, "S9x": {"type": "structure", "members": {"Name": {}, "Description": {}, "DefaultRunProperties": {"shape": "S9y"}, "CreatedOn": {"type": "timestamp"}, "LastModifiedOn": {"type": "timestamp"}, "LastRun": {"shape": "S9z"}, "Graph": {"shape": "Sa2"}, "MaxConcurrentRuns": {"type": "integer"}, "BlueprintDetails": {"type": "structure", "members": {"BlueprintName": {}, "RunId": {}}}}}, "S9y": {"type": "map", "key": {}, "value": {}}, "S9z": {"type": "structure", "members": {"Name": {}, "WorkflowRunId": {}, "PreviousRunId": {}, "WorkflowRunProperties": {"shape": "S9y"}, "StartedOn": {"type": "timestamp"}, "CompletedOn": {"type": "timestamp"}, "Status": {}, "ErrorMessage": {}, "Statistics": {"type": "structure", "members": {"TotalActions": {"type": "integer"}, "TimeoutActions": {"type": "integer"}, "FailedActions": {"type": "integer"}, "StoppedActions": {"type": "integer"}, "SucceededActions": {"type": "integer"}, "RunningActions": {"type": "integer"}, "ErroredActions": {"type": "integer"}, "WaitingActions": {"type": "integer"}}}, "Graph": {"shape": "Sa2"}, "StartingEventBatchCondition": {"type": "structure", "members": {"BatchSize": {"type": "integer"}, "BatchWindow": {"type": "integer"}}}}}, "Sa2": {"type": "structure", "members": {"Nodes": {"type": "list", "member": {"type": "structure", "members": {"Type": {}, "Name": {}, "UniqueId": {}, "TriggerDetails": {"type": "structure", "members": {"Trigger": {"shape": "S9d"}}}, "JobDetails": {"type": "structure", "members": {"JobRuns": {"shape": "Sa8"}}}, "CrawlerDetails": {"type": "structure", "members": {"Crawls": {"type": "list", "member": {"type": "structure", "members": {"State": {}, "StartedOn": {"type": "timestamp"}, "CompletedOn": {"type": "timestamp"}, "ErrorMessage": {}, "LogGroup": {}, "LogStream": {}}}}}}}}}, "Edges": {"type": "list", "member": {"type": "structure", "members": {"SourceId": {}, "DestinationId": {}}}}}}, "Sa8": {"type": "list", "member": {"shape": "Sa9"}}, "Sa9": {"type": "structure", "members": {"Id": {}, "Attempt": {"type": "integer"}, "PreviousRunId": {}, "TriggerName": {}, "JobName": {}, "StartedOn": {"type": "timestamp"}, "LastModifiedOn": {"type": "timestamp"}, "CompletedOn": {"type": "timestamp"}, "JobRunState": {}, "Arguments": {"shape": "S4t"}, "ErrorMessage": {}, "PredecessorRuns": {"type": "list", "member": {"type": "structure", "members": {"JobName": {}, "RunId": {}}}}, "AllocatedCapacity": {"deprecated": true, "deprecatedMessage": "This property is deprecated, use MaxCapacity instead.", "type": "integer"}, "ExecutionTime": {"type": "integer"}, "Timeout": {"type": "integer"}, "MaxCapacity": {"type": "double"}, "WorkerType": {}, "NumberOfWorkers": {"type": "integer"}, "SecurityConfiguration": {}, "LogGroupName": {}, "NotificationProperty": {"shape": "S4y"}, "GlueVersion": {}, "DPUSeconds": {"type": "double"}, "ExecutionClass": {}}}, "Sav": {"type": "list", "member": {}}, "Sbg": {"type": "map", "key": {}, "value": {}}, "Sbx": {"type": "list", "member": {}}, "Sby": {"type": "list", "member": {}}, "Sc1": {"type": "structure", "required": ["Name", "ConnectionType", "ConnectionProperties"], "members": {"Name": {}, "Description": {}, "ConnectionType": {}, "MatchCriteria": {"shape": "Sc3"}, "ConnectionProperties": {"shape": "Sc4"}, "PhysicalConnectionRequirements": {"shape": "Sc6"}}}, "Sc3": {"type": "list", "member": {}}, "Sc4": {"type": "map", "key": {}, "value": {}}, "Sc6": {"type": "structure", "members": {"SubnetId": {}, "SecurityGroupIdList": {"type": "list", "member": {}}, "AvailabilityZone": {}}}, "Scf": {"type": "structure", "required": ["TableName", "DatabaseName"], "members": {"TableName": {}, "DatabaseName": {}}}, "Sci": {"type": "structure", "required": ["Name"], "members": {"Name": {}, "Description": {}, "LocationUri": {}, "Parameters": {"shape": "Se"}, "CreateTableDefaultPermissions": {"shape": "Sck"}, "TargetDatabase": {"shape": "Scq"}}}, "Sck": {"type": "list", "member": {"type": "structure", "members": {"Principal": {"type": "structure", "members": {"DataLakePrincipalIdentifier": {}}}, "Permissions": {"type": "list", "member": {}}}}}, "Scq": {"type": "structure", "members": {"CatalogId": {}, "DatabaseName": {}}}, "Scx": {"type": "list", "member": {"shape": "S40"}}, "Scy": {"type": "structure", "required": ["TransformType"], "members": {"TransformType": {}, "FindMatchesParameters": {"type": "structure", "members": {"PrimaryKeyColumnName": {}, "PrecisionRecallTradeoff": {"type": "double"}, "AccuracyCostTradeoff": {"type": "double"}, "EnforceProvidedLabels": {"type": "boolean"}}}}}, "Sd2": {"type": "structure", "members": {"MlUserDataEncryption": {"type": "structure", "required": ["MlUserDataEncryptionMode"], "members": {"MlUserDataEncryptionMode": {}, "KmsKeyId": {}}}, "TaskRunSecurityConfigurationName": {}}}, "Sd9": {"type": "structure", "required": ["Keys", "IndexName"], "members": {"Keys": {"type": "list", "member": {}}, "IndexName": {}}}, "Sdf": {"type": "structure", "members": {"RegistryName": {}, "RegistryArn": {}}}, "Sdm": {"type": "list", "member": {"type": "structure", "required": ["Id", "NodeType", "<PERSON><PERSON><PERSON>"], "members": {"Id": {}, "NodeType": {}, "Args": {"shape": "Sdq"}, "LineNumber": {"type": "integer"}}}}, "Sdq": {"type": "list", "member": {"type": "structure", "required": ["Name", "Value"], "members": {"Name": {}, "Value": {}, "Param": {"type": "boolean"}}}}, "Sdu": {"type": "list", "member": {"type": "structure", "required": ["Source", "Target"], "members": {"Source": {}, "Target": {}, "TargetParameter": {}}}}, "Se1": {"type": "structure", "members": {"S3Encryption": {"type": "list", "member": {"type": "structure", "members": {"S3EncryptionMode": {}, "KmsKeyArn": {}}}}, "CloudWatchEncryption": {"type": "structure", "members": {"CloudWatchEncryptionMode": {}, "KmsKeyArn": {}}}, "JobBookmarksEncryption": {"type": "structure", "members": {"JobBookmarksEncryptionMode": {}, "KmsKeyArn": {}}}}}, "Sed": {"type": "structure", "members": {"Name": {}, "PythonVersion": {}}}, "See": {"type": "map", "key": {}, "value": {}}, "Seh": {"type": "structure", "members": {"Id": {}, "CreatedOn": {"type": "timestamp"}, "Status": {}, "ErrorMessage": {}, "Description": {}, "Role": {}, "Command": {"shape": "Sed"}, "DefaultArguments": {"shape": "See"}, "Connections": {"shape": "S4u"}, "Progress": {"type": "double"}, "MaxCapacity": {"type": "double"}, "SecurityConfiguration": {}, "GlueVersion": {}}}, "Sel": {"type": "structure", "required": ["Name"], "members": {"Name": {}, "Description": {}, "Owner": {}, "LastAccessTime": {"type": "timestamp"}, "LastAnalyzedTime": {"type": "timestamp"}, "Retention": {"type": "integer"}, "StorageDescriptor": {"shape": "S9"}, "PartitionKeys": {"shape": "Sa"}, "ViewOriginalText": {}, "ViewExpandedText": {}, "TableType": {}, "Parameters": {"shape": "Se"}, "TargetTable": {"shape": "Sep"}}}, "Sep": {"type": "structure", "members": {"CatalogId": {}, "DatabaseName": {}, "Name": {}}}, "Sev": {"type": "structure", "members": {"FunctionName": {}, "ClassName": {}, "OwnerName": {}, "OwnerType": {}, "ResourceUris": {"shape": "Sex"}}}, "Sex": {"type": "list", "member": {"type": "structure", "members": {"ResourceType": {}, "Uri": {}}}}, "Sgs": {"type": "structure", "members": {"BlueprintName": {}, "RunId": {}, "WorkflowName": {}, "State": {}, "StartedOn": {"type": "timestamp"}, "CompletedOn": {"type": "timestamp"}, "ErrorMessage": {}, "RollbackErrorMessage": {}, "Parameters": {}, "RoleArn": {}}}, "Sh6": {"type": "structure", "members": {"GrokClassifier": {"type": "structure", "required": ["Name", "Classification", "GrokPattern"], "members": {"Name": {}, "Classification": {}, "CreationTime": {"type": "timestamp"}, "LastUpdated": {"type": "timestamp"}, "Version": {"type": "long"}, "GrokPattern": {}, "CustomPatterns": {}}}, "XMLClassifier": {"type": "structure", "required": ["Name", "Classification"], "members": {"Name": {}, "Classification": {}, "CreationTime": {"type": "timestamp"}, "LastUpdated": {"type": "timestamp"}, "Version": {"type": "long"}, "RowTag": {}}}, "JsonClassifier": {"type": "structure", "required": ["Name", "JsonPath"], "members": {"Name": {}, "CreationTime": {"type": "timestamp"}, "LastUpdated": {"type": "timestamp"}, "Version": {"type": "long"}, "JsonPath": {}}}, "CsvClassifier": {"type": "structure", "required": ["Name"], "members": {"Name": {}, "CreationTime": {"type": "timestamp"}, "LastUpdated": {"type": "timestamp"}, "Version": {"type": "long"}, "Delimiter": {}, "QuoteSymbol": {}, "ContainsHeader": {}, "Header": {"shape": "Sbx"}, "DisableValueTrimming": {"type": "boolean"}, "AllowSingleColumn": {"type": "boolean"}, "CustomDatatypeConfigured": {"type": "boolean"}, "CustomDatatypes": {"shape": "Sby"}}}}}, "Shg": {"type": "list", "member": {}}, "Shi": {"type": "list", "member": {"shape": "Shj"}}, "Shj": {"type": "structure", "required": ["ColumnName", "ColumnType", "AnalyzedTime", "StatisticsData"], "members": {"ColumnName": {}, "ColumnType": {}, "AnalyzedTime": {"type": "timestamp"}, "StatisticsData": {"type": "structure", "required": ["Type"], "members": {"Type": {}, "BooleanColumnStatisticsData": {"type": "structure", "required": ["NumberOfTrues", "NumberOfFalses", "NumberOfNulls"], "members": {"NumberOfTrues": {"type": "long"}, "NumberOfFalses": {"type": "long"}, "NumberOfNulls": {"type": "long"}}}, "DateColumnStatisticsData": {"type": "structure", "required": ["NumberOfNulls", "NumberOfDistinctValues"], "members": {"MinimumValue": {"type": "timestamp"}, "MaximumValue": {"type": "timestamp"}, "NumberOfNulls": {"type": "long"}, "NumberOfDistinctValues": {"type": "long"}}}, "DecimalColumnStatisticsData": {"type": "structure", "required": ["NumberOfNulls", "NumberOfDistinctValues"], "members": {"MinimumValue": {"shape": "Shr"}, "MaximumValue": {"shape": "Shr"}, "NumberOfNulls": {"type": "long"}, "NumberOfDistinctValues": {"type": "long"}}}, "DoubleColumnStatisticsData": {"type": "structure", "required": ["NumberOfNulls", "NumberOfDistinctValues"], "members": {"MinimumValue": {"type": "double"}, "MaximumValue": {"type": "double"}, "NumberOfNulls": {"type": "long"}, "NumberOfDistinctValues": {"type": "long"}}}, "LongColumnStatisticsData": {"type": "structure", "required": ["NumberOfNulls", "NumberOfDistinctValues"], "members": {"MinimumValue": {"type": "long"}, "MaximumValue": {"type": "long"}, "NumberOfNulls": {"type": "long"}, "NumberOfDistinctValues": {"type": "long"}}}, "StringColumnStatisticsData": {"type": "structure", "required": ["<PERSON><PERSON><PERSON><PERSON>", "AverageLength", "NumberOfNulls", "NumberOfDistinctValues"], "members": {"MaximumLength": {"type": "long"}, "AverageLength": {"type": "double"}, "NumberOfNulls": {"type": "long"}, "NumberOfDistinctValues": {"type": "long"}}}, "BinaryColumnStatisticsData": {"type": "structure", "required": ["<PERSON><PERSON><PERSON><PERSON>", "AverageLength", "NumberOfNulls"], "members": {"MaximumLength": {"type": "long"}, "AverageLength": {"type": "double"}, "NumberOfNulls": {"type": "long"}}}}}}}, "Shr": {"type": "structure", "required": ["UnscaledValue", "Scale"], "members": {"UnscaledValue": {"type": "blob"}, "Scale": {"type": "integer"}}}, "Si0": {"type": "list", "member": {"type": "structure", "members": {"ColumnName": {}, "Error": {"shape": "S14"}}}}, "Si6": {"type": "structure", "members": {"Name": {}, "Description": {}, "ConnectionType": {}, "MatchCriteria": {"shape": "Sc3"}, "ConnectionProperties": {"shape": "Sc4"}, "PhysicalConnectionRequirements": {"shape": "Sc6"}, "CreationTime": {"type": "timestamp"}, "LastUpdatedTime": {"type": "timestamp"}, "LastUpdatedBy": {}}}, "Sin": {"type": "structure", "members": {"EncryptionAtRest": {"type": "structure", "required": ["CatalogEncryptionMode"], "members": {"CatalogEncryptionMode": {}, "SseAwsKmsKeyId": {}}}, "ConnectionPasswordEncryption": {"type": "structure", "required": ["ReturnConnectionPasswordEncrypted"], "members": {"ReturnConnectionPasswordEncrypted": {"type": "boolean"}, "AwsKmsKeyId": {}}}}}, "Siz": {"type": "structure", "members": {"CloudWatchMetricsEnabled": {"type": "boolean"}, "ResultsS3Prefix": {}}}, "Sj0": {"type": "list", "member": {}}, "Sj4": {"type": "structure", "required": ["Name"], "members": {"Name": {}, "Description": {}, "LocationUri": {}, "Parameters": {"shape": "Se"}, "CreateTime": {"type": "timestamp"}, "CreateTableDefaultPermissions": {"shape": "Sck"}, "TargetDatabase": {"shape": "Scq"}, "CatalogId": {}}}, "Sjm": {"type": "structure", "members": {"JobName": {}, "Version": {"type": "integer"}, "Run": {"type": "integer"}, "Attempt": {"type": "integer"}, "PreviousRunId": {}, "RunId": {}, "JobBookmark": {}}}, "Sjw": {"type": "structure", "members": {"TaskType": {}, "ImportLabelsTaskRunProperties": {"type": "structure", "members": {"InputS3Path": {}, "Replace": {"type": "boolean"}}}, "ExportLabelsTaskRunProperties": {"type": "structure", "members": {"OutputS3Path": {}}}, "LabelingSetGenerationTaskRunProperties": {"type": "structure", "members": {"OutputS3Path": {}}}, "FindMatchesTaskRunProperties": {"type": "structure", "members": {"JobId": {}, "JobName": {}, "JobRunId": {}}}}}, "Skf": {"type": "structure", "required": ["TransformType"], "members": {"TransformType": {}, "FindMatchesMetrics": {"type": "structure", "members": {"AreaUnderPRCurve": {"type": "double"}, "Precision": {"type": "double"}, "Recall": {"type": "double"}, "F1": {"type": "double"}, "ConfusionMatrix": {"type": "structure", "members": {"NumTruePositives": {"type": "long"}, "NumFalsePositives": {"type": "long"}, "NumTrueNegatives": {"type": "long"}, "NumFalseNegatives": {"type": "long"}}}, "ColumnImportances": {"type": "list", "member": {"type": "structure", "members": {"ColumnName": {}, "Importance": {"type": "double"}}}}}}}}, "Skm": {"type": "list", "member": {"type": "structure", "members": {"Name": {}, "DataType": {}}}}, "Skp": {"type": "structure", "members": {"Name": {}, "TransformType": {}, "Status": {}, "GlueVersion": {}, "CreatedBefore": {"type": "timestamp"}, "CreatedAfter": {"type": "timestamp"}, "LastModifiedBefore": {"type": "timestamp"}, "LastModifiedAfter": {"type": "timestamp"}, "Schema": {"shape": "Skm"}}}, "Skq": {"type": "structure", "required": ["Column", "SortDirection"], "members": {"Column": {}, "SortDirection": {}}}, "Skw": {"type": "structure", "required": ["DatabaseName", "TableName"], "members": {"DatabaseName": {}, "TableName": {}}}, "Skx": {"type": "list", "member": {"shape": "Skw"}}, "Sky": {"type": "structure", "members": {"Jdbc": {"shape": "Sdq"}, "S3": {"shape": "Sdq"}, "DynamoDB": {"shape": "Sdq"}}}, "Sl0": {"type": "list", "member": {"type": "structure", "members": {"SourceTable": {}, "SourcePath": {}, "SourceType": {}, "TargetTable": {}, "TargetPath": {}, "TargetType": {}}}}, "Slk": {"type": "structure", "required": ["SegmentNumber", "TotalSegments"], "members": {"SegmentNumber": {"type": "integer"}, "TotalSegments": {"type": "integer"}}}, "Sm7": {"type": "structure", "members": {"LatestVersion": {"type": "boolean"}, "VersionNumber": {"type": "long"}}}, "Smg": {"type": "structure", "members": {"Name": {}, "CreatedTimeStamp": {"type": "timestamp"}, "EncryptionConfiguration": {"shape": "Se1"}}}, "Smo": {"type": "structure", "members": {"Id": {"type": "integer"}, "Code": {}, "State": {}, "Output": {"type": "structure", "members": {"Data": {"type": "structure", "members": {"TextPlain": {}}}, "ExecutionCount": {"type": "integer"}, "Status": {}, "ErrorName": {}, "ErrorValue": {}, "Traceback": {"shape": "S4v"}}}, "Progress": {"type": "double"}, "StartedOn": {"type": "long"}, "CompletedOn": {"type": "long"}}}, "Smv": {"type": "structure", "required": ["Name"], "members": {"Name": {}, "DatabaseName": {}, "Description": {}, "Owner": {}, "CreateTime": {"type": "timestamp"}, "UpdateTime": {"type": "timestamp"}, "LastAccessTime": {"type": "timestamp"}, "LastAnalyzedTime": {"type": "timestamp"}, "Retention": {"type": "integer"}, "StorageDescriptor": {"shape": "S9"}, "PartitionKeys": {"shape": "Sa"}, "ViewOriginalText": {}, "ViewExpandedText": {}, "TableType": {}, "Parameters": {"shape": "Se"}, "CreatedBy": {}, "IsRegisteredWithLakeFormation": {"type": "boolean"}, "TargetTable": {"shape": "Sep"}, "CatalogId": {}, "VersionId": {}}}, "Smy": {"type": "structure", "members": {"Table": {"shape": "Smv"}, "VersionId": {}}}, "Sn5": {"type": "list", "member": {"shape": "Smv"}}, "Snd": {"type": "structure", "members": {"AdditionalAuditContext": {}, "RequestedColumns": {"type": "list", "member": {}}, "AllColumnsRequested": {"type": "boolean"}}}, "Sng": {"type": "list", "member": {}}, "Snt": {"type": "structure", "members": {"FunctionName": {}, "DatabaseName": {}, "ClassName": {}, "OwnerName": {}, "OwnerType": {}, "CreateTime": {"type": "timestamp"}, "ResourceUris": {"shape": "Sex"}, "CatalogId": {}}}, "Sqd": {"type": "structure", "members": {"MetadataKey": {}, "MetadataValue": {}}}, "Sqy": {"type": "list", "member": {}}, "Ssn": {"type": "list", "member": {"shape": "Shj"}}, "Ssp": {"type": "list", "member": {"type": "structure", "members": {"ColumnStatistics": {"shape": "Shj"}, "Error": {"shape": "S14"}}}}}}