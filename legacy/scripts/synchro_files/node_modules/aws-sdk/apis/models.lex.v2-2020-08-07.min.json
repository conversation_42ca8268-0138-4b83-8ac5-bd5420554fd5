{"version": "2.0", "metadata": {"apiVersion": "2020-08-07", "endpointPrefix": "models-v2-lex", "jsonVersion": "1.1", "protocol": "rest-json", "serviceAbbreviation": "Lex Models V2", "serviceFullName": "Amazon Lex Model Building V2", "serviceId": "Lex Models V2", "signatureVersion": "v4", "signingName": "lex", "uid": "models.lex.v2-2020-08-07"}, "operations": {"BatchCreateCustomVocabularyItem": {"http": {"method": "PUT", "requestUri": "/bots/{botId}/botversions/{botVersion}/botlocales/{localeId}/customvocabulary/DEFAULT/batchcreate", "responseCode": 200}, "input": {"type": "structure", "required": ["botId", "botVersion", "localeId", "customVocabularyItemList"], "members": {"botId": {"location": "uri", "locationName": "botId"}, "botVersion": {"location": "uri", "locationName": "botVersion"}, "localeId": {"location": "uri", "locationName": "localeId"}, "customVocabularyItemList": {"type": "list", "member": {"type": "structure", "required": ["phrase"], "members": {"phrase": {}, "weight": {"type": "integer"}, "displayAs": {}}}}}}, "output": {"type": "structure", "members": {"botId": {}, "botVersion": {}, "localeId": {}, "errors": {"shape": "Sa"}, "resources": {"shape": "Sf"}}}}, "BatchDeleteCustomVocabularyItem": {"http": {"requestUri": "/bots/{botId}/botversions/{botVersion}/botlocales/{localeId}/customvocabulary/DEFAULT/batchdelete", "responseCode": 200}, "input": {"type": "structure", "required": ["botId", "botVersion", "localeId", "customVocabularyItemList"], "members": {"botId": {"location": "uri", "locationName": "botId"}, "botVersion": {"location": "uri", "locationName": "botVersion"}, "localeId": {"location": "uri", "locationName": "localeId"}, "customVocabularyItemList": {"type": "list", "member": {"type": "structure", "required": ["itemId"], "members": {"itemId": {}}}}}}, "output": {"type": "structure", "members": {"botId": {}, "botVersion": {}, "localeId": {}, "errors": {"shape": "Sa"}, "resources": {"shape": "Sf"}}}}, "BatchUpdateCustomVocabularyItem": {"http": {"method": "PUT", "requestUri": "/bots/{botId}/botversions/{botVersion}/botlocales/{localeId}/customvocabulary/DEFAULT/batchupdate", "responseCode": 200}, "input": {"type": "structure", "required": ["botId", "botVersion", "localeId", "customVocabularyItemList"], "members": {"botId": {"location": "uri", "locationName": "botId"}, "botVersion": {"location": "uri", "locationName": "botVersion"}, "localeId": {"location": "uri", "locationName": "localeId"}, "customVocabularyItemList": {"type": "list", "member": {"shape": "Sg"}}}}, "output": {"type": "structure", "members": {"botId": {}, "botVersion": {}, "localeId": {}, "errors": {"shape": "Sa"}, "resources": {"shape": "Sf"}}}}, "BuildBotLocale": {"http": {"requestUri": "/bots/{botId}/botversions/{botVersion}/botlocales/{localeId}/", "responseCode": 202}, "input": {"type": "structure", "required": ["botId", "botVersion", "localeId"], "members": {"botId": {"location": "uri", "locationName": "botId"}, "botVersion": {"location": "uri", "locationName": "botVersion"}, "localeId": {"location": "uri", "locationName": "localeId"}}}, "output": {"type": "structure", "members": {"botId": {}, "botVersion": {}, "localeId": {}, "botLocaleStatus": {}, "lastBuildSubmittedDateTime": {"type": "timestamp"}}}}, "CreateBot": {"http": {"method": "PUT", "requestUri": "/bots/", "responseCode": 202}, "input": {"type": "structure", "required": ["botName", "roleArn", "dataPrivacy", "idleSessionTTLInSeconds"], "members": {"botName": {}, "description": {}, "roleArn": {}, "dataPrivacy": {"shape": "Sx"}, "idleSessionTTLInSeconds": {"type": "integer"}, "botTags": {"shape": "S10"}, "testBotAliasTags": {"shape": "S10"}, "botType": {}, "botMembers": {"shape": "S14"}}}, "output": {"type": "structure", "members": {"botId": {}, "botName": {}, "description": {}, "roleArn": {}, "dataPrivacy": {"shape": "Sx"}, "idleSessionTTLInSeconds": {"type": "integer"}, "botStatus": {}, "creationDateTime": {"type": "timestamp"}, "botTags": {"shape": "S10"}, "testBotAliasTags": {"shape": "S10"}, "botType": {}, "botMembers": {"shape": "S14"}}}}, "CreateBotAlias": {"http": {"method": "PUT", "requestUri": "/bots/{botId}/botaliases/", "responseCode": 202}, "input": {"type": "structure", "required": ["botAliasName", "botId"], "members": {"botAliasName": {}, "description": {}, "botVersion": {}, "botAliasLocaleSettings": {"shape": "S1c"}, "conversationLogSettings": {"shape": "S1j"}, "sentimentAnalysisSettings": {"shape": "S1w"}, "botId": {"location": "uri", "locationName": "botId"}, "tags": {"shape": "S10"}}}, "output": {"type": "structure", "members": {"botAliasId": {}, "botAliasName": {}, "description": {}, "botVersion": {}, "botAliasLocaleSettings": {"shape": "S1c"}, "conversationLogSettings": {"shape": "S1j"}, "sentimentAnalysisSettings": {"shape": "S1w"}, "botAliasStatus": {}, "botId": {}, "creationDateTime": {"type": "timestamp"}, "tags": {"shape": "S10"}}}}, "CreateBotLocale": {"http": {"method": "PUT", "requestUri": "/bots/{botId}/botversions/{botVersion}/botlocales/", "responseCode": 202}, "input": {"type": "structure", "required": ["botId", "botVersion", "localeId", "nluIntentConfidenceThreshold"], "members": {"botId": {"location": "uri", "locationName": "botId"}, "botVersion": {"location": "uri", "locationName": "botVersion"}, "localeId": {}, "description": {}, "nluIntentConfidenceThreshold": {"type": "double"}, "voiceSettings": {"shape": "S21"}}}, "output": {"type": "structure", "members": {"botId": {}, "botVersion": {}, "localeName": {}, "localeId": {}, "description": {}, "nluIntentConfidenceThreshold": {"type": "double"}, "voiceSettings": {"shape": "S21"}, "botLocaleStatus": {}, "creationDateTime": {"type": "timestamp"}}}}, "CreateBotVersion": {"http": {"method": "PUT", "requestUri": "/bots/{botId}/botversions/", "responseCode": 202}, "input": {"type": "structure", "required": ["botId", "botVersionLocaleSpecification"], "members": {"botId": {"location": "uri", "locationName": "botId"}, "description": {}, "botVersionLocaleSpecification": {"shape": "S27"}}}, "output": {"type": "structure", "members": {"botId": {}, "description": {}, "botVersion": {}, "botVersionLocaleSpecification": {"shape": "S27"}, "botStatus": {}, "creationDateTime": {"type": "timestamp"}}}}, "CreateExport": {"http": {"method": "PUT", "requestUri": "/exports/", "responseCode": 202}, "input": {"type": "structure", "required": ["resourceSpecification", "fileFormat"], "members": {"resourceSpecification": {"shape": "S2b"}, "fileFormat": {}, "filePassword": {"shape": "S2g"}}}, "output": {"type": "structure", "members": {"exportId": {}, "resourceSpecification": {"shape": "S2b"}, "fileFormat": {}, "exportStatus": {}, "creationDateTime": {"type": "timestamp"}}}}, "CreateIntent": {"http": {"method": "PUT", "requestUri": "/bots/{botId}/botversions/{botVersion}/botlocales/{localeId}/intents/", "responseCode": 200}, "input": {"type": "structure", "required": ["intentName", "botId", "botVersion", "localeId"], "members": {"intentName": {}, "description": {}, "parentIntentSignature": {}, "sampleUtterances": {"shape": "S2l"}, "dialogCodeHook": {"shape": "S2o"}, "fulfillmentCodeHook": {"shape": "S2p"}, "intentConfirmationSetting": {"shape": "S3y"}, "intentClosingSetting": {"shape": "S4g"}, "inputContexts": {"shape": "S4h"}, "outputContexts": {"shape": "S4j"}, "kendraConfiguration": {"shape": "S4n"}, "botId": {"location": "uri", "locationName": "botId"}, "botVersion": {"location": "uri", "locationName": "botVersion"}, "localeId": {"location": "uri", "locationName": "localeId"}, "initialResponseSetting": {"shape": "S4q"}}}, "output": {"type": "structure", "members": {"intentId": {}, "intentName": {}, "description": {}, "parentIntentSignature": {}, "sampleUtterances": {"shape": "S2l"}, "dialogCodeHook": {"shape": "S2o"}, "fulfillmentCodeHook": {"shape": "S2p"}, "intentConfirmationSetting": {"shape": "S3y"}, "intentClosingSetting": {"shape": "S4g"}, "inputContexts": {"shape": "S4h"}, "outputContexts": {"shape": "S4j"}, "kendraConfiguration": {"shape": "S4n"}, "botId": {}, "botVersion": {}, "localeId": {}, "creationDateTime": {"type": "timestamp"}, "initialResponseSetting": {"shape": "S4q"}}}}, "CreateResourcePolicy": {"http": {"requestUri": "/policy/{resourceArn}/", "responseCode": 200}, "input": {"type": "structure", "required": ["resourceArn", "policy"], "members": {"resourceArn": {"location": "uri", "locationName": "resourceArn"}, "policy": {}}}, "output": {"type": "structure", "members": {"resourceArn": {}, "revisionId": {}}}}, "CreateResourcePolicyStatement": {"http": {"requestUri": "/policy/{resourceArn}/statements/", "responseCode": 200}, "input": {"type": "structure", "required": ["resourceArn", "statementId", "effect", "principal", "action"], "members": {"resourceArn": {"location": "uri", "locationName": "resourceArn"}, "statementId": {}, "effect": {}, "principal": {"type": "list", "member": {"type": "structure", "members": {"service": {}, "arn": {}}}}, "action": {"type": "list", "member": {}}, "condition": {"type": "map", "key": {}, "value": {"type": "map", "key": {}, "value": {}}}, "expectedRevisionId": {"location": "querystring", "locationName": "expectedRevisionId"}}}, "output": {"type": "structure", "members": {"resourceArn": {}, "revisionId": {}}}}, "CreateSlot": {"http": {"method": "PUT", "requestUri": "/bots/{botId}/botversions/{botVersion}/botlocales/{localeId}/intents/{intentId}/slots/", "responseCode": 200}, "input": {"type": "structure", "required": ["slotName", "valueElicitationSetting", "botId", "botVersion", "localeId", "intentId"], "members": {"slotName": {}, "description": {}, "slotTypeId": {}, "valueElicitationSetting": {"shape": "S5d"}, "obfuscationSetting": {"shape": "S5o"}, "botId": {"location": "uri", "locationName": "botId"}, "botVersion": {"location": "uri", "locationName": "botVersion"}, "localeId": {"location": "uri", "locationName": "localeId"}, "intentId": {"location": "uri", "locationName": "intentId"}, "multipleValuesSetting": {"shape": "S5q"}, "subSlotSetting": {"shape": "S5r"}}}, "output": {"type": "structure", "members": {"slotId": {}, "slotName": {}, "description": {}, "slotTypeId": {}, "valueElicitationSetting": {"shape": "S5d"}, "obfuscationSetting": {"shape": "S5o"}, "botId": {}, "botVersion": {}, "localeId": {}, "intentId": {}, "creationDateTime": {"type": "timestamp"}, "multipleValuesSetting": {"shape": "S5q"}, "subSlotSetting": {"shape": "S5r"}}}}, "CreateSlotType": {"http": {"method": "PUT", "requestUri": "/bots/{botId}/botversions/{botVersion}/botlocales/{localeId}/slottypes/", "responseCode": 200}, "input": {"type": "structure", "required": ["slotTypeName", "botId", "botVersion", "localeId"], "members": {"slotTypeName": {}, "description": {}, "slotTypeValues": {"shape": "S5y"}, "valueSelectionSetting": {"shape": "S63"}, "parentSlotTypeSignature": {}, "botId": {"location": "uri", "locationName": "botId"}, "botVersion": {"location": "uri", "locationName": "botVersion"}, "localeId": {"location": "uri", "locationName": "localeId"}, "externalSourceSetting": {"shape": "S6a"}, "compositeSlotTypeSetting": {"shape": "S6f"}}}, "output": {"type": "structure", "members": {"slotTypeId": {}, "slotTypeName": {}, "description": {}, "slotTypeValues": {"shape": "S5y"}, "valueSelectionSetting": {"shape": "S63"}, "parentSlotTypeSignature": {}, "botId": {}, "botVersion": {}, "localeId": {}, "creationDateTime": {"type": "timestamp"}, "externalSourceSetting": {"shape": "S6a"}, "compositeSlotTypeSetting": {"shape": "S6f"}}}}, "CreateUploadUrl": {"http": {"requestUri": "/createuploadurl/", "responseCode": 200}, "input": {"type": "structure", "members": {}}, "output": {"type": "structure", "members": {"importId": {}, "uploadUrl": {}}}}, "DeleteBot": {"http": {"method": "DELETE", "requestUri": "/bots/{botId}/", "responseCode": 202}, "input": {"type": "structure", "required": ["botId"], "members": {"botId": {"location": "uri", "locationName": "botId"}, "skipResourceInUseCheck": {"location": "querystring", "locationName": "skipResourceInUseCheck", "type": "boolean"}}}, "output": {"type": "structure", "members": {"botId": {}, "botStatus": {}}}}, "DeleteBotAlias": {"http": {"method": "DELETE", "requestUri": "/bots/{botId}/botaliases/{botAliasId}/", "responseCode": 202}, "input": {"type": "structure", "required": ["botAliasId", "botId"], "members": {"botAliasId": {"location": "uri", "locationName": "botAliasId"}, "botId": {"location": "uri", "locationName": "botId"}, "skipResourceInUseCheck": {"location": "querystring", "locationName": "skipResourceInUseCheck", "type": "boolean"}}}, "output": {"type": "structure", "members": {"botAliasId": {}, "botId": {}, "botAliasStatus": {}}}}, "DeleteBotLocale": {"http": {"method": "DELETE", "requestUri": "/bots/{botId}/botversions/{botVersion}/botlocales/{localeId}/", "responseCode": 202}, "input": {"type": "structure", "required": ["botId", "botVersion", "localeId"], "members": {"botId": {"location": "uri", "locationName": "botId"}, "botVersion": {"location": "uri", "locationName": "botVersion"}, "localeId": {"location": "uri", "locationName": "localeId"}}}, "output": {"type": "structure", "members": {"botId": {}, "botVersion": {}, "localeId": {}, "botLocaleStatus": {}}}}, "DeleteBotVersion": {"http": {"method": "DELETE", "requestUri": "/bots/{botId}/botversions/{botVersion}/", "responseCode": 202}, "input": {"type": "structure", "required": ["botId", "botVersion"], "members": {"botId": {"location": "uri", "locationName": "botId"}, "botVersion": {"location": "uri", "locationName": "botVersion"}, "skipResourceInUseCheck": {"location": "querystring", "locationName": "skipResourceInUseCheck", "type": "boolean"}}}, "output": {"type": "structure", "members": {"botId": {}, "botVersion": {}, "botStatus": {}}}}, "DeleteCustomVocabulary": {"http": {"method": "DELETE", "requestUri": "/bots/{botId}/botversions/{botVersion}/botlocales/{localeId}/customvocabulary", "responseCode": 202}, "input": {"type": "structure", "required": ["botId", "botVersion", "localeId"], "members": {"botId": {"location": "uri", "locationName": "botId"}, "botVersion": {"location": "uri", "locationName": "botVersion"}, "localeId": {"location": "uri", "locationName": "localeId"}}}, "output": {"type": "structure", "members": {"botId": {}, "botVersion": {}, "localeId": {}, "customVocabularyStatus": {}}}}, "DeleteExport": {"http": {"method": "DELETE", "requestUri": "/exports/{exportId}/", "responseCode": 202}, "input": {"type": "structure", "required": ["exportId"], "members": {"exportId": {"location": "uri", "locationName": "exportId"}}}, "output": {"type": "structure", "members": {"exportId": {}, "exportStatus": {}}}}, "DeleteImport": {"http": {"method": "DELETE", "requestUri": "/imports/{importId}/", "responseCode": 202}, "input": {"type": "structure", "required": ["importId"], "members": {"importId": {"location": "uri", "locationName": "importId"}}}, "output": {"type": "structure", "members": {"importId": {}, "importStatus": {}}}}, "DeleteIntent": {"http": {"method": "DELETE", "requestUri": "/bots/{botId}/botversions/{botVersion}/botlocales/{localeId}/intents/{intentId}/", "responseCode": 204}, "input": {"type": "structure", "required": ["intentId", "botId", "botVersion", "localeId"], "members": {"intentId": {"location": "uri", "locationName": "intentId"}, "botId": {"location": "uri", "locationName": "botId"}, "botVersion": {"location": "uri", "locationName": "botVersion"}, "localeId": {"location": "uri", "locationName": "localeId"}}}}, "DeleteResourcePolicy": {"http": {"method": "DELETE", "requestUri": "/policy/{resourceArn}/", "responseCode": 204}, "input": {"type": "structure", "required": ["resourceArn"], "members": {"resourceArn": {"location": "uri", "locationName": "resourceArn"}, "expectedRevisionId": {"location": "querystring", "locationName": "expectedRevisionId"}}}, "output": {"type": "structure", "members": {"resourceArn": {}, "revisionId": {}}}}, "DeleteResourcePolicyStatement": {"http": {"method": "DELETE", "requestUri": "/policy/{resourceArn}/statements/{statementId}/", "responseCode": 204}, "input": {"type": "structure", "required": ["resourceArn", "statementId"], "members": {"resourceArn": {"location": "uri", "locationName": "resourceArn"}, "statementId": {"location": "uri", "locationName": "statementId"}, "expectedRevisionId": {"location": "querystring", "locationName": "expectedRevisionId"}}}, "output": {"type": "structure", "members": {"resourceArn": {}, "revisionId": {}}}}, "DeleteSlot": {"http": {"method": "DELETE", "requestUri": "/bots/{botId}/botversions/{botVersion}/botlocales/{localeId}/intents/{intentId}/slots/{slotId}/", "responseCode": 204}, "input": {"type": "structure", "required": ["slotId", "botId", "botVersion", "localeId", "intentId"], "members": {"slotId": {"location": "uri", "locationName": "slotId"}, "botId": {"location": "uri", "locationName": "botId"}, "botVersion": {"location": "uri", "locationName": "botVersion"}, "localeId": {"location": "uri", "locationName": "localeId"}, "intentId": {"location": "uri", "locationName": "intentId"}}}}, "DeleteSlotType": {"http": {"method": "DELETE", "requestUri": "/bots/{botId}/botversions/{botVersion}/botlocales/{localeId}/slottypes/{slotTypeId}/", "responseCode": 204}, "input": {"type": "structure", "required": ["slotTypeId", "botId", "botVersion", "localeId"], "members": {"slotTypeId": {"location": "uri", "locationName": "slotTypeId"}, "botId": {"location": "uri", "locationName": "botId"}, "botVersion": {"location": "uri", "locationName": "botVersion"}, "localeId": {"location": "uri", "locationName": "localeId"}, "skipResourceInUseCheck": {"location": "querystring", "locationName": "skipResourceInUseCheck", "type": "boolean"}}}}, "DeleteUtterances": {"http": {"method": "DELETE", "requestUri": "/bots/{botId}/utterances/", "responseCode": 204}, "input": {"type": "structure", "required": ["botId"], "members": {"botId": {"location": "uri", "locationName": "botId"}, "localeId": {"location": "querystring", "locationName": "localeId"}, "sessionId": {"location": "querystring", "locationName": "sessionId"}}}, "output": {"type": "structure", "members": {}}}, "DescribeBot": {"http": {"method": "GET", "requestUri": "/bots/{botId}/", "responseCode": 200}, "input": {"type": "structure", "required": ["botId"], "members": {"botId": {"location": "uri", "locationName": "botId"}}}, "output": {"type": "structure", "members": {"botId": {}, "botName": {}, "description": {}, "roleArn": {}, "dataPrivacy": {"shape": "Sx"}, "idleSessionTTLInSeconds": {"type": "integer"}, "botStatus": {}, "creationDateTime": {"type": "timestamp"}, "lastUpdatedDateTime": {"type": "timestamp"}, "botType": {}, "botMembers": {"shape": "S14"}, "failureReasons": {"shape": "S7f"}}}}, "DescribeBotAlias": {"http": {"method": "GET", "requestUri": "/bots/{botId}/botaliases/{botAliasId}/", "responseCode": 200}, "input": {"type": "structure", "required": ["botAliasId", "botId"], "members": {"botAliasId": {"location": "uri", "locationName": "botAliasId"}, "botId": {"location": "uri", "locationName": "botId"}}}, "output": {"type": "structure", "members": {"botAliasId": {}, "botAliasName": {}, "description": {}, "botVersion": {}, "botAliasLocaleSettings": {"shape": "S1c"}, "conversationLogSettings": {"shape": "S1j"}, "sentimentAnalysisSettings": {"shape": "S1w"}, "botAliasHistoryEvents": {"type": "list", "member": {"type": "structure", "members": {"botVersion": {}, "startDate": {"type": "timestamp"}, "endDate": {"type": "timestamp"}}}}, "botAliasStatus": {}, "botId": {}, "creationDateTime": {"type": "timestamp"}, "lastUpdatedDateTime": {"type": "timestamp"}, "parentBotNetworks": {"shape": "S7l"}}}}, "DescribeBotLocale": {"http": {"method": "GET", "requestUri": "/bots/{botId}/botversions/{botVersion}/botlocales/{localeId}/", "responseCode": 200}, "input": {"type": "structure", "required": ["botId", "botVersion", "localeId"], "members": {"botId": {"location": "uri", "locationName": "botId"}, "botVersion": {"location": "uri", "locationName": "botVersion"}, "localeId": {"location": "uri", "locationName": "localeId"}}}, "output": {"type": "structure", "members": {"botId": {}, "botVersion": {}, "localeId": {}, "localeName": {}, "description": {}, "nluIntentConfidenceThreshold": {"type": "double"}, "voiceSettings": {"shape": "S21"}, "intentsCount": {"type": "integer"}, "slotTypesCount": {"type": "integer"}, "botLocaleStatus": {}, "failureReasons": {"shape": "S7f"}, "creationDateTime": {"type": "timestamp"}, "lastUpdatedDateTime": {"type": "timestamp"}, "lastBuildSubmittedDateTime": {"type": "timestamp"}, "botLocaleHistoryEvents": {"type": "list", "member": {"type": "structure", "required": ["event", "eventDate"], "members": {"event": {}, "eventDate": {"type": "timestamp"}}}}, "recommendedActions": {"shape": "S7t"}}}}, "DescribeBotRecommendation": {"http": {"method": "GET", "requestUri": "/bots/{botId}/botversions/{botVersion}/botlocales/{localeId}/botrecommendations/{botRecommendationId}/", "responseCode": 200}, "input": {"type": "structure", "required": ["botId", "botVersion", "localeId", "botRecommendationId"], "members": {"botId": {"location": "uri", "locationName": "botId"}, "botVersion": {"location": "uri", "locationName": "botVersion"}, "localeId": {"location": "uri", "locationName": "localeId"}, "botRecommendationId": {"location": "uri", "locationName": "botRecommendationId"}}}, "output": {"type": "structure", "members": {"botId": {}, "botVersion": {}, "localeId": {}, "botRecommendationStatus": {}, "botRecommendationId": {}, "failureReasons": {"shape": "S7f"}, "creationDateTime": {"type": "timestamp"}, "lastUpdatedDateTime": {"type": "timestamp"}, "transcriptSourceSetting": {"shape": "S7y"}, "encryptionSetting": {"shape": "S87"}, "botRecommendationResults": {"type": "structure", "members": {"botLocaleExportUrl": {}, "associatedTranscriptsUrl": {}, "statistics": {"type": "structure", "members": {"intents": {"type": "structure", "members": {"discoveredIntentCount": {"type": "integer"}}}, "slotTypes": {"type": "structure", "members": {"discoveredSlotTypeCount": {"type": "integer"}}}}}}}}}}, "DescribeBotVersion": {"http": {"method": "GET", "requestUri": "/bots/{botId}/botversions/{botVersion}/", "responseCode": 200}, "input": {"type": "structure", "required": ["botId", "botVersion"], "members": {"botId": {"location": "uri", "locationName": "botId"}, "botVersion": {"location": "uri", "locationName": "botVersion"}}}, "output": {"type": "structure", "members": {"botId": {}, "botName": {}, "botVersion": {}, "description": {}, "roleArn": {}, "dataPrivacy": {"shape": "Sx"}, "idleSessionTTLInSeconds": {"type": "integer"}, "botStatus": {}, "failureReasons": {"shape": "S7f"}, "creationDateTime": {"type": "timestamp"}, "parentBotNetworks": {"shape": "S7l"}, "botType": {}, "botMembers": {"shape": "S14"}}}}, "DescribeCustomVocabularyMetadata": {"http": {"method": "GET", "requestUri": "/bots/{botId}/botversions/{botVersion}/botlocales/{localeId}/customvocabulary/DEFAULT/metadata", "responseCode": 200}, "input": {"type": "structure", "required": ["botId", "botVersion", "localeId"], "members": {"botId": {"location": "uri", "locationName": "botId"}, "botVersion": {"location": "uri", "locationName": "botVersion"}, "localeId": {"location": "uri", "locationName": "localeId"}}}, "output": {"type": "structure", "members": {"botId": {}, "botVersion": {}, "localeId": {}, "customVocabularyStatus": {}, "creationDateTime": {"type": "timestamp"}, "lastUpdatedDateTime": {"type": "timestamp"}}}}, "DescribeExport": {"http": {"method": "GET", "requestUri": "/exports/{exportId}/", "responseCode": 200}, "input": {"type": "structure", "required": ["exportId"], "members": {"exportId": {"location": "uri", "locationName": "exportId"}}}, "output": {"type": "structure", "members": {"exportId": {}, "resourceSpecification": {"shape": "S2b"}, "fileFormat": {}, "exportStatus": {}, "failureReasons": {"shape": "S7f"}, "downloadUrl": {}, "creationDateTime": {"type": "timestamp"}, "lastUpdatedDateTime": {"type": "timestamp"}}}}, "DescribeImport": {"http": {"method": "GET", "requestUri": "/imports/{importId}/", "responseCode": 200}, "input": {"type": "structure", "required": ["importId"], "members": {"importId": {"location": "uri", "locationName": "importId"}}}, "output": {"type": "structure", "members": {"importId": {}, "resourceSpecification": {"shape": "S8m"}, "importedResourceId": {}, "importedResourceName": {}, "mergeStrategy": {}, "importStatus": {}, "failureReasons": {"shape": "S7f"}, "creationDateTime": {"type": "timestamp"}, "lastUpdatedDateTime": {"type": "timestamp"}}}}, "DescribeIntent": {"http": {"method": "GET", "requestUri": "/bots/{botId}/botversions/{botVersion}/botlocales/{localeId}/intents/{intentId}/", "responseCode": 200}, "input": {"type": "structure", "required": ["intentId", "botId", "botVersion", "localeId"], "members": {"intentId": {"location": "uri", "locationName": "intentId"}, "botId": {"location": "uri", "locationName": "botId"}, "botVersion": {"location": "uri", "locationName": "botVersion"}, "localeId": {"location": "uri", "locationName": "localeId"}}}, "output": {"type": "structure", "members": {"intentId": {}, "intentName": {}, "description": {}, "parentIntentSignature": {}, "sampleUtterances": {"shape": "S2l"}, "dialogCodeHook": {"shape": "S2o"}, "fulfillmentCodeHook": {"shape": "S2p"}, "slotPriorities": {"shape": "S8u"}, "intentConfirmationSetting": {"shape": "S3y"}, "intentClosingSetting": {"shape": "S4g"}, "inputContexts": {"shape": "S4h"}, "outputContexts": {"shape": "S4j"}, "kendraConfiguration": {"shape": "S4n"}, "botId": {}, "botVersion": {}, "localeId": {}, "creationDateTime": {"type": "timestamp"}, "lastUpdatedDateTime": {"type": "timestamp"}, "initialResponseSetting": {"shape": "S4q"}}}}, "DescribeResourcePolicy": {"http": {"method": "GET", "requestUri": "/policy/{resourceArn}/", "responseCode": 200}, "input": {"type": "structure", "required": ["resourceArn"], "members": {"resourceArn": {"location": "uri", "locationName": "resourceArn"}}}, "output": {"type": "structure", "members": {"resourceArn": {}, "policy": {}, "revisionId": {}}}}, "DescribeSlot": {"http": {"method": "GET", "requestUri": "/bots/{botId}/botversions/{botVersion}/botlocales/{localeId}/intents/{intentId}/slots/{slotId}/", "responseCode": 200}, "input": {"type": "structure", "required": ["slotId", "botId", "botVersion", "localeId", "intentId"], "members": {"slotId": {"location": "uri", "locationName": "slotId"}, "botId": {"location": "uri", "locationName": "botId"}, "botVersion": {"location": "uri", "locationName": "botVersion"}, "localeId": {"location": "uri", "locationName": "localeId"}, "intentId": {"location": "uri", "locationName": "intentId"}}}, "output": {"type": "structure", "members": {"slotId": {}, "slotName": {}, "description": {}, "slotTypeId": {}, "valueElicitationSetting": {"shape": "S5d"}, "obfuscationSetting": {"shape": "S5o"}, "botId": {}, "botVersion": {}, "localeId": {}, "intentId": {}, "creationDateTime": {"type": "timestamp"}, "lastUpdatedDateTime": {"type": "timestamp"}, "multipleValuesSetting": {"shape": "S5q"}, "subSlotSetting": {"shape": "S5r"}}}}, "DescribeSlotType": {"http": {"method": "GET", "requestUri": "/bots/{botId}/botversions/{botVersion}/botlocales/{localeId}/slottypes/{slotTypeId}/", "responseCode": 200}, "input": {"type": "structure", "required": ["slotTypeId", "botId", "botVersion", "localeId"], "members": {"slotTypeId": {"location": "uri", "locationName": "slotTypeId"}, "botId": {"location": "uri", "locationName": "botId"}, "botVersion": {"location": "uri", "locationName": "botVersion"}, "localeId": {"location": "uri", "locationName": "localeId"}}}, "output": {"type": "structure", "members": {"slotTypeId": {}, "slotTypeName": {}, "description": {}, "slotTypeValues": {"shape": "S5y"}, "valueSelectionSetting": {"shape": "S63"}, "parentSlotTypeSignature": {}, "botId": {}, "botVersion": {}, "localeId": {}, "creationDateTime": {"type": "timestamp"}, "lastUpdatedDateTime": {"type": "timestamp"}, "externalSourceSetting": {"shape": "S6a"}, "compositeSlotTypeSetting": {"shape": "S6f"}}}}, "ListAggregatedUtterances": {"http": {"requestUri": "/bots/{botId}/aggregatedutterances/", "responseCode": 200}, "input": {"type": "structure", "required": ["botId", "localeId", "aggregationDuration"], "members": {"botId": {"location": "uri", "locationName": "botId"}, "botAliasId": {}, "botVersion": {}, "localeId": {}, "aggregationDuration": {"shape": "S94"}, "sortBy": {"type": "structure", "required": ["attribute", "order"], "members": {"attribute": {}, "order": {}}}, "filters": {"type": "list", "member": {"type": "structure", "required": ["name", "values", "operator"], "members": {"name": {}, "values": {"shape": "S9e"}, "operator": {}}}}, "maxResults": {"type": "integer"}, "nextToken": {}}}, "output": {"type": "structure", "members": {"botId": {}, "botAliasId": {}, "botVersion": {}, "localeId": {}, "aggregationDuration": {"shape": "S94"}, "aggregationWindowStartTime": {"type": "timestamp"}, "aggregationWindowEndTime": {"type": "timestamp"}, "aggregationLastRefreshedDateTime": {"type": "timestamp"}, "aggregatedUtterancesSummaries": {"type": "list", "member": {"type": "structure", "members": {"utterance": {}, "hitCount": {"type": "integer"}, "missedCount": {"type": "integer"}, "utteranceFirstRecordedInAggregationDuration": {"type": "timestamp"}, "utteranceLastRecordedInAggregationDuration": {"type": "timestamp"}, "containsDataFromDeletedResources": {"type": "boolean"}}}}, "nextToken": {}}}}, "ListBotAliases": {"http": {"requestUri": "/bots/{botId}/botaliases/", "responseCode": 200}, "input": {"type": "structure", "required": ["botId"], "members": {"botId": {"location": "uri", "locationName": "botId"}, "maxResults": {"type": "integer"}, "nextToken": {}}}, "output": {"type": "structure", "members": {"botAliasSummaries": {"type": "list", "member": {"type": "structure", "members": {"botAliasId": {}, "botAliasName": {}, "description": {}, "botVersion": {}, "botAliasStatus": {}, "creationDateTime": {"type": "timestamp"}, "lastUpdatedDateTime": {"type": "timestamp"}}}}, "nextToken": {}, "botId": {}}}}, "ListBotLocales": {"http": {"requestUri": "/bots/{botId}/botversions/{botVersion}/botlocales/", "responseCode": 200}, "input": {"type": "structure", "required": ["botId", "botVersion"], "members": {"botId": {"location": "uri", "locationName": "botId"}, "botVersion": {"location": "uri", "locationName": "botVersion"}, "sortBy": {"type": "structure", "required": ["attribute", "order"], "members": {"attribute": {}, "order": {}}}, "filters": {"type": "list", "member": {"type": "structure", "required": ["name", "values", "operator"], "members": {"name": {}, "values": {"shape": "S9e"}, "operator": {}}}}, "maxResults": {"type": "integer"}, "nextToken": {}}}, "output": {"type": "structure", "members": {"botId": {}, "botVersion": {}, "nextToken": {}, "botLocaleSummaries": {"type": "list", "member": {"type": "structure", "members": {"localeId": {}, "localeName": {}, "description": {}, "botLocaleStatus": {}, "lastUpdatedDateTime": {"type": "timestamp"}, "lastBuildSubmittedDateTime": {"type": "timestamp"}}}}}}}, "ListBotRecommendations": {"http": {"requestUri": "/bots/{botId}/botversions/{botVersion}/botlocales/{localeId}/botrecommendations/", "responseCode": 200}, "input": {"type": "structure", "required": ["botId", "botVersion", "localeId"], "members": {"botId": {"location": "uri", "locationName": "botId"}, "botVersion": {"location": "uri", "locationName": "botVersion"}, "localeId": {"location": "uri", "locationName": "localeId"}, "maxResults": {"type": "integer"}, "nextToken": {}}}, "output": {"type": "structure", "members": {"botId": {}, "botVersion": {}, "localeId": {}, "botRecommendationSummaries": {"type": "list", "member": {"type": "structure", "required": ["botRecommendationStatus", "botRecommendationId"], "members": {"botRecommendationStatus": {}, "botRecommendationId": {}, "creationDateTime": {"type": "timestamp"}, "lastUpdatedDateTime": {"type": "timestamp"}}}}, "nextToken": {}}}}, "ListBotVersions": {"http": {"requestUri": "/bots/{botId}/botversions/", "responseCode": 200}, "input": {"type": "structure", "required": ["botId"], "members": {"botId": {"location": "uri", "locationName": "botId"}, "sortBy": {"type": "structure", "required": ["attribute", "order"], "members": {"attribute": {}, "order": {}}}, "maxResults": {"type": "integer"}, "nextToken": {}}}, "output": {"type": "structure", "members": {"botId": {}, "botVersionSummaries": {"type": "list", "member": {"type": "structure", "members": {"botName": {}, "botVersion": {}, "description": {}, "botStatus": {}, "creationDateTime": {"type": "timestamp"}}}}, "nextToken": {}}}}, "ListBots": {"http": {"requestUri": "/bots/", "responseCode": 200}, "input": {"type": "structure", "members": {"sortBy": {"type": "structure", "required": ["attribute", "order"], "members": {"attribute": {}, "order": {}}}, "filters": {"type": "list", "member": {"type": "structure", "required": ["name", "values", "operator"], "members": {"name": {}, "values": {"shape": "S9e"}, "operator": {}}}}, "maxResults": {"type": "integer"}, "nextToken": {}}}, "output": {"type": "structure", "members": {"botSummaries": {"type": "list", "member": {"type": "structure", "members": {"botId": {}, "botName": {}, "description": {}, "botStatus": {}, "latestBotVersion": {}, "lastUpdatedDateTime": {"type": "timestamp"}, "botType": {}}}}, "nextToken": {}}}}, "ListBuiltInIntents": {"http": {"requestUri": "/builtins/locales/{localeId}/intents/", "responseCode": 200}, "input": {"type": "structure", "required": ["localeId"], "members": {"localeId": {"location": "uri", "locationName": "localeId"}, "sortBy": {"type": "structure", "required": ["attribute", "order"], "members": {"attribute": {}, "order": {}}}, "maxResults": {"type": "integer"}, "nextToken": {}}}, "output": {"type": "structure", "members": {"builtInIntentSummaries": {"type": "list", "member": {"type": "structure", "members": {"intentSignature": {}, "description": {}}}}, "nextToken": {}, "localeId": {}}}}, "ListBuiltInSlotTypes": {"http": {"requestUri": "/builtins/locales/{localeId}/slottypes/", "responseCode": 200}, "input": {"type": "structure", "required": ["localeId"], "members": {"localeId": {"location": "uri", "locationName": "localeId"}, "sortBy": {"type": "structure", "required": ["attribute", "order"], "members": {"attribute": {}, "order": {}}}, "maxResults": {"type": "integer"}, "nextToken": {}}}, "output": {"type": "structure", "members": {"builtInSlotTypeSummaries": {"type": "list", "member": {"type": "structure", "members": {"slotTypeSignature": {}, "description": {}}}}, "nextToken": {}, "localeId": {}}}}, "ListCustomVocabularyItems": {"http": {"requestUri": "/bots/{botId}/botversions/{botVersion}/botlocales/{localeId}/customvocabulary/DEFAULT/list", "responseCode": 200}, "input": {"type": "structure", "required": ["botId", "botVersion", "localeId"], "members": {"botId": {"location": "uri", "locationName": "botId"}, "botVersion": {"location": "uri", "locationName": "botVersion"}, "localeId": {"location": "uri", "locationName": "localeId"}, "maxResults": {"type": "integer"}, "nextToken": {}}}, "output": {"type": "structure", "members": {"botId": {}, "botVersion": {}, "localeId": {}, "customVocabularyItems": {"shape": "Sf"}, "nextToken": {}}}}, "ListExports": {"http": {"requestUri": "/exports/", "responseCode": 200}, "input": {"type": "structure", "members": {"botId": {}, "botVersion": {}, "sortBy": {"type": "structure", "required": ["attribute", "order"], "members": {"attribute": {}, "order": {}}}, "filters": {"type": "list", "member": {"type": "structure", "required": ["name", "values", "operator"], "members": {"name": {}, "values": {"shape": "S9e"}, "operator": {}}}}, "maxResults": {"type": "integer"}, "nextToken": {}, "localeId": {}}}, "output": {"type": "structure", "members": {"botId": {}, "botVersion": {}, "exportSummaries": {"type": "list", "member": {"type": "structure", "members": {"exportId": {}, "resourceSpecification": {"shape": "S2b"}, "fileFormat": {}, "exportStatus": {}, "creationDateTime": {"type": "timestamp"}, "lastUpdatedDateTime": {"type": "timestamp"}}}}, "nextToken": {}, "localeId": {}}}}, "ListImports": {"http": {"requestUri": "/imports/", "responseCode": 200}, "input": {"type": "structure", "members": {"botId": {}, "botVersion": {}, "sortBy": {"type": "structure", "required": ["attribute", "order"], "members": {"attribute": {}, "order": {}}}, "filters": {"type": "list", "member": {"type": "structure", "required": ["name", "values", "operator"], "members": {"name": {}, "values": {"shape": "S9e"}, "operator": {}}}}, "maxResults": {"type": "integer"}, "nextToken": {}, "localeId": {}}}, "output": {"type": "structure", "members": {"botId": {}, "botVersion": {}, "importSummaries": {"type": "list", "member": {"type": "structure", "members": {"importId": {}, "importedResourceId": {}, "importedResourceName": {}, "importStatus": {}, "mergeStrategy": {}, "creationDateTime": {"type": "timestamp"}, "lastUpdatedDateTime": {"type": "timestamp"}, "importedResourceType": {}}}}, "nextToken": {}, "localeId": {}}}}, "ListIntents": {"http": {"requestUri": "/bots/{botId}/botversions/{botVersion}/botlocales/{localeId}/intents/", "responseCode": 200}, "input": {"type": "structure", "required": ["botId", "botVersion", "localeId"], "members": {"botId": {"location": "uri", "locationName": "botId"}, "botVersion": {"location": "uri", "locationName": "botVersion"}, "localeId": {"location": "uri", "locationName": "localeId"}, "sortBy": {"type": "structure", "required": ["attribute", "order"], "members": {"attribute": {}, "order": {}}}, "filters": {"type": "list", "member": {"type": "structure", "required": ["name", "values", "operator"], "members": {"name": {}, "values": {"shape": "S9e"}, "operator": {}}}}, "maxResults": {"type": "integer"}, "nextToken": {}}}, "output": {"type": "structure", "members": {"botId": {}, "botVersion": {}, "localeId": {}, "intentSummaries": {"type": "list", "member": {"type": "structure", "members": {"intentId": {}, "intentName": {}, "description": {}, "parentIntentSignature": {}, "inputContexts": {"shape": "S4h"}, "outputContexts": {"shape": "S4j"}, "lastUpdatedDateTime": {"type": "timestamp"}}}}, "nextToken": {}}}}, "ListRecommendedIntents": {"http": {"requestUri": "/bots/{botId}/botversions/{botVersion}/botlocales/{localeId}/botrecommendations/{botRecommendationId}/intents", "responseCode": 200}, "input": {"type": "structure", "required": ["botId", "botVersion", "localeId", "botRecommendationId"], "members": {"botId": {"location": "uri", "locationName": "botId"}, "botVersion": {"location": "uri", "locationName": "botVersion"}, "localeId": {"location": "uri", "locationName": "localeId"}, "botRecommendationId": {"location": "uri", "locationName": "botRecommendationId"}, "nextToken": {}, "maxResults": {"type": "integer"}}}, "output": {"type": "structure", "members": {"botId": {}, "botVersion": {}, "localeId": {}, "botRecommendationId": {}, "summaryList": {"type": "list", "member": {"type": "structure", "members": {"intentId": {}, "intentName": {}, "sampleUtterancesCount": {"type": "integer"}}}}, "nextToken": {}}}}, "ListSlotTypes": {"http": {"requestUri": "/bots/{botId}/botversions/{botVersion}/botlocales/{localeId}/slottypes/", "responseCode": 200}, "input": {"type": "structure", "required": ["botId", "botVersion", "localeId"], "members": {"botId": {"location": "uri", "locationName": "botId"}, "botVersion": {"location": "uri", "locationName": "botVersion"}, "localeId": {"location": "uri", "locationName": "localeId"}, "sortBy": {"type": "structure", "required": ["attribute", "order"], "members": {"attribute": {}, "order": {}}}, "filters": {"type": "list", "member": {"type": "structure", "required": ["name", "values", "operator"], "members": {"name": {}, "values": {"shape": "S9e"}, "operator": {}}}}, "maxResults": {"type": "integer"}, "nextToken": {}}}, "output": {"type": "structure", "members": {"botId": {}, "botVersion": {}, "localeId": {}, "slotTypeSummaries": {"type": "list", "member": {"type": "structure", "members": {"slotTypeId": {}, "slotTypeName": {}, "description": {}, "parentSlotTypeSignature": {}, "lastUpdatedDateTime": {"type": "timestamp"}, "slotTypeCategory": {}}}}, "nextToken": {}}}}, "ListSlots": {"http": {"requestUri": "/bots/{botId}/botversions/{botVersion}/botlocales/{localeId}/intents/{intentId}/slots/", "responseCode": 200}, "input": {"type": "structure", "required": ["botId", "botVersion", "localeId", "intentId"], "members": {"botId": {"location": "uri", "locationName": "botId"}, "botVersion": {"location": "uri", "locationName": "botVersion"}, "localeId": {"location": "uri", "locationName": "localeId"}, "intentId": {"location": "uri", "locationName": "intentId"}, "sortBy": {"type": "structure", "required": ["attribute", "order"], "members": {"attribute": {}, "order": {}}}, "filters": {"type": "list", "member": {"type": "structure", "required": ["name", "values", "operator"], "members": {"name": {}, "values": {"shape": "S9e"}, "operator": {}}}}, "maxResults": {"type": "integer"}, "nextToken": {}}}, "output": {"type": "structure", "members": {"botId": {}, "botVersion": {}, "localeId": {}, "intentId": {}, "slotSummaries": {"type": "list", "member": {"type": "structure", "members": {"slotId": {}, "slotName": {}, "description": {}, "slotConstraint": {}, "slotTypeId": {}, "valueElicitationPromptSpecification": {"shape": "S3z"}, "lastUpdatedDateTime": {"type": "timestamp"}}}}, "nextToken": {}}}}, "ListTagsForResource": {"http": {"method": "GET", "requestUri": "/tags/{resourceARN}", "responseCode": 200}, "input": {"type": "structure", "required": ["resourceARN"], "members": {"resourceARN": {"location": "uri", "locationName": "resourceARN"}}}, "output": {"type": "structure", "members": {"tags": {"shape": "S10"}}}}, "SearchAssociatedTranscripts": {"http": {"requestUri": "/bots/{botId}/botversions/{botVersion}/botlocales/{localeId}/botrecommendations/{botRecommendationId}/associatedtranscripts", "responseCode": 200}, "input": {"type": "structure", "required": ["botId", "botVersion", "localeId", "botRecommendationId", "filters"], "members": {"botId": {"location": "uri", "locationName": "botId"}, "botVersion": {"location": "uri", "locationName": "botVersion"}, "localeId": {"location": "uri", "locationName": "localeId"}, "botRecommendationId": {"location": "uri", "locationName": "botRecommendationId"}, "searchOrder": {}, "filters": {"type": "list", "member": {"type": "structure", "required": ["name", "values"], "members": {"name": {}, "values": {"shape": "S9e"}}}}, "maxResults": {"type": "integer"}, "nextIndex": {"type": "integer"}}}, "output": {"type": "structure", "members": {"botId": {}, "botVersion": {}, "localeId": {}, "botRecommendationId": {}, "nextIndex": {"type": "integer"}, "associatedTranscripts": {"type": "list", "member": {"type": "structure", "members": {"transcript": {}}}}, "totalResults": {"type": "integer"}}}}, "StartBotRecommendation": {"http": {"method": "PUT", "requestUri": "/bots/{botId}/botversions/{botVersion}/botlocales/{localeId}/botrecommendations/", "responseCode": 202}, "input": {"type": "structure", "required": ["botId", "botVersion", "localeId", "transcriptSourceSetting"], "members": {"botId": {"location": "uri", "locationName": "botId"}, "botVersion": {"location": "uri", "locationName": "botVersion"}, "localeId": {"location": "uri", "locationName": "localeId"}, "transcriptSourceSetting": {"shape": "S7y"}, "encryptionSetting": {"shape": "S87"}}}, "output": {"type": "structure", "members": {"botId": {}, "botVersion": {}, "localeId": {}, "botRecommendationStatus": {}, "botRecommendationId": {}, "creationDateTime": {"type": "timestamp"}, "transcriptSourceSetting": {"shape": "S7y"}, "encryptionSetting": {"shape": "S87"}}}}, "StartImport": {"http": {"method": "PUT", "requestUri": "/imports/", "responseCode": 202}, "input": {"type": "structure", "required": ["importId", "resourceSpecification", "mergeStrategy"], "members": {"importId": {}, "resourceSpecification": {"shape": "S8m"}, "mergeStrategy": {}, "filePassword": {"shape": "S2g"}}}, "output": {"type": "structure", "members": {"importId": {}, "resourceSpecification": {"shape": "S8m"}, "mergeStrategy": {}, "importStatus": {}, "creationDateTime": {"type": "timestamp"}}}}, "StopBotRecommendation": {"http": {"method": "PUT", "requestUri": "/bots/{botId}/botversions/{botVersion}/botlocales/{localeId}/botrecommendations/{botRecommendationId}/stopbotrecommendation", "responseCode": 202}, "input": {"type": "structure", "required": ["botId", "botVersion", "localeId", "botRecommendationId"], "members": {"botId": {"location": "uri", "locationName": "botId"}, "botVersion": {"location": "uri", "locationName": "botVersion"}, "localeId": {"location": "uri", "locationName": "localeId"}, "botRecommendationId": {"location": "uri", "locationName": "botRecommendationId"}}}, "output": {"type": "structure", "members": {"botId": {}, "botVersion": {}, "localeId": {}, "botRecommendationStatus": {}, "botRecommendationId": {}}}}, "TagResource": {"http": {"requestUri": "/tags/{resourceARN}", "responseCode": 200}, "input": {"type": "structure", "required": ["resourceARN", "tags"], "members": {"resourceARN": {"location": "uri", "locationName": "resourceARN"}, "tags": {"shape": "S10"}}}, "output": {"type": "structure", "members": {}}}, "UntagResource": {"http": {"method": "DELETE", "requestUri": "/tags/{resourceARN}", "responseCode": 200}, "input": {"type": "structure", "required": ["resourceARN", "tagKeys"], "members": {"resourceARN": {"location": "uri", "locationName": "resourceARN"}, "tagKeys": {"location": "querystring", "locationName": "tagKeys", "type": "list", "member": {}}}}, "output": {"type": "structure", "members": {}}}, "UpdateBot": {"http": {"method": "PUT", "requestUri": "/bots/{botId}/", "responseCode": 202}, "input": {"type": "structure", "required": ["botId", "botName", "roleArn", "dataPrivacy", "idleSessionTTLInSeconds"], "members": {"botId": {"location": "uri", "locationName": "botId"}, "botName": {}, "description": {}, "roleArn": {}, "dataPrivacy": {"shape": "Sx"}, "idleSessionTTLInSeconds": {"type": "integer"}, "botType": {}, "botMembers": {"shape": "S14"}}}, "output": {"type": "structure", "members": {"botId": {}, "botName": {}, "description": {}, "roleArn": {}, "dataPrivacy": {"shape": "Sx"}, "idleSessionTTLInSeconds": {"type": "integer"}, "botStatus": {}, "creationDateTime": {"type": "timestamp"}, "lastUpdatedDateTime": {"type": "timestamp"}, "botType": {}, "botMembers": {"shape": "S14"}}}}, "UpdateBotAlias": {"http": {"method": "PUT", "requestUri": "/bots/{botId}/botaliases/{botAliasId}/", "responseCode": 202}, "input": {"type": "structure", "required": ["botAliasId", "botAliasName", "botId"], "members": {"botAliasId": {"location": "uri", "locationName": "botAliasId"}, "botAliasName": {}, "description": {}, "botVersion": {}, "botAliasLocaleSettings": {"shape": "S1c"}, "conversationLogSettings": {"shape": "S1j"}, "sentimentAnalysisSettings": {"shape": "S1w"}, "botId": {"location": "uri", "locationName": "botId"}}}, "output": {"type": "structure", "members": {"botAliasId": {}, "botAliasName": {}, "description": {}, "botVersion": {}, "botAliasLocaleSettings": {"shape": "S1c"}, "conversationLogSettings": {"shape": "S1j"}, "sentimentAnalysisSettings": {"shape": "S1w"}, "botAliasStatus": {}, "botId": {}, "creationDateTime": {"type": "timestamp"}, "lastUpdatedDateTime": {"type": "timestamp"}}}}, "UpdateBotLocale": {"http": {"method": "PUT", "requestUri": "/bots/{botId}/botversions/{botVersion}/botlocales/{localeId}/", "responseCode": 202}, "input": {"type": "structure", "required": ["botId", "botVersion", "localeId", "nluIntentConfidenceThreshold"], "members": {"botId": {"location": "uri", "locationName": "botId"}, "botVersion": {"location": "uri", "locationName": "botVersion"}, "localeId": {"location": "uri", "locationName": "localeId"}, "description": {}, "nluIntentConfidenceThreshold": {"type": "double"}, "voiceSettings": {"shape": "S21"}}}, "output": {"type": "structure", "members": {"botId": {}, "botVersion": {}, "localeId": {}, "localeName": {}, "description": {}, "nluIntentConfidenceThreshold": {"type": "double"}, "voiceSettings": {"shape": "S21"}, "botLocaleStatus": {}, "failureReasons": {"shape": "S7f"}, "creationDateTime": {"type": "timestamp"}, "lastUpdatedDateTime": {"type": "timestamp"}, "recommendedActions": {"shape": "S7t"}}}}, "UpdateBotRecommendation": {"http": {"method": "PUT", "requestUri": "/bots/{botId}/botversions/{botVersion}/botlocales/{localeId}/botrecommendations/{botRecommendationId}/", "responseCode": 202}, "input": {"type": "structure", "required": ["botId", "botVersion", "localeId", "botRecommendationId", "encryptionSetting"], "members": {"botId": {"location": "uri", "locationName": "botId"}, "botVersion": {"location": "uri", "locationName": "botVersion"}, "localeId": {"location": "uri", "locationName": "localeId"}, "botRecommendationId": {"location": "uri", "locationName": "botRecommendationId"}, "encryptionSetting": {"shape": "S87"}}}, "output": {"type": "structure", "members": {"botId": {}, "botVersion": {}, "localeId": {}, "botRecommendationStatus": {}, "botRecommendationId": {}, "creationDateTime": {"type": "timestamp"}, "lastUpdatedDateTime": {"type": "timestamp"}, "transcriptSourceSetting": {"shape": "S7y"}, "encryptionSetting": {"shape": "S87"}}}}, "UpdateExport": {"http": {"method": "PUT", "requestUri": "/exports/{exportId}/", "responseCode": 202}, "input": {"type": "structure", "required": ["exportId"], "members": {"exportId": {"location": "uri", "locationName": "exportId"}, "filePassword": {"shape": "S2g"}}}, "output": {"type": "structure", "members": {"exportId": {}, "resourceSpecification": {"shape": "S2b"}, "fileFormat": {}, "exportStatus": {}, "creationDateTime": {"type": "timestamp"}, "lastUpdatedDateTime": {"type": "timestamp"}}}}, "UpdateIntent": {"http": {"method": "PUT", "requestUri": "/bots/{botId}/botversions/{botVersion}/botlocales/{localeId}/intents/{intentId}/", "responseCode": 200}, "input": {"type": "structure", "required": ["intentId", "intentName", "botId", "botVersion", "localeId"], "members": {"intentId": {"location": "uri", "locationName": "intentId"}, "intentName": {}, "description": {}, "parentIntentSignature": {}, "sampleUtterances": {"shape": "S2l"}, "dialogCodeHook": {"shape": "S2o"}, "fulfillmentCodeHook": {"shape": "S2p"}, "slotPriorities": {"shape": "S8u"}, "intentConfirmationSetting": {"shape": "S3y"}, "intentClosingSetting": {"shape": "S4g"}, "inputContexts": {"shape": "S4h"}, "outputContexts": {"shape": "S4j"}, "kendraConfiguration": {"shape": "S4n"}, "botId": {"location": "uri", "locationName": "botId"}, "botVersion": {"location": "uri", "locationName": "botVersion"}, "localeId": {"location": "uri", "locationName": "localeId"}, "initialResponseSetting": {"shape": "S4q"}}}, "output": {"type": "structure", "members": {"intentId": {}, "intentName": {}, "description": {}, "parentIntentSignature": {}, "sampleUtterances": {"shape": "S2l"}, "dialogCodeHook": {"shape": "S2o"}, "fulfillmentCodeHook": {"shape": "S2p"}, "slotPriorities": {"shape": "S8u"}, "intentConfirmationSetting": {"shape": "S3y"}, "intentClosingSetting": {"shape": "S4g"}, "inputContexts": {"shape": "S4h"}, "outputContexts": {"shape": "S4j"}, "kendraConfiguration": {"shape": "S4n"}, "botId": {}, "botVersion": {}, "localeId": {}, "creationDateTime": {"type": "timestamp"}, "lastUpdatedDateTime": {"type": "timestamp"}, "initialResponseSetting": {"shape": "S4q"}}}}, "UpdateResourcePolicy": {"http": {"method": "PUT", "requestUri": "/policy/{resourceArn}/", "responseCode": 200}, "input": {"type": "structure", "required": ["resourceArn", "policy"], "members": {"resourceArn": {"location": "uri", "locationName": "resourceArn"}, "policy": {}, "expectedRevisionId": {"location": "querystring", "locationName": "expectedRevisionId"}}}, "output": {"type": "structure", "members": {"resourceArn": {}, "revisionId": {}}}}, "UpdateSlot": {"http": {"method": "PUT", "requestUri": "/bots/{botId}/botversions/{botVersion}/botlocales/{localeId}/intents/{intentId}/slots/{slotId}/", "responseCode": 200}, "input": {"type": "structure", "required": ["slotId", "slotName", "valueElicitationSetting", "botId", "botVersion", "localeId", "intentId"], "members": {"slotId": {"location": "uri", "locationName": "slotId"}, "slotName": {}, "description": {}, "slotTypeId": {}, "valueElicitationSetting": {"shape": "S5d"}, "obfuscationSetting": {"shape": "S5o"}, "botId": {"location": "uri", "locationName": "botId"}, "botVersion": {"location": "uri", "locationName": "botVersion"}, "localeId": {"location": "uri", "locationName": "localeId"}, "intentId": {"location": "uri", "locationName": "intentId"}, "multipleValuesSetting": {"shape": "S5q"}, "subSlotSetting": {"shape": "S5r"}}}, "output": {"type": "structure", "members": {"slotId": {}, "slotName": {}, "description": {}, "slotTypeId": {}, "valueElicitationSetting": {"shape": "S5d"}, "obfuscationSetting": {"shape": "S5o"}, "botId": {}, "botVersion": {}, "localeId": {}, "intentId": {}, "creationDateTime": {"type": "timestamp"}, "lastUpdatedDateTime": {"type": "timestamp"}, "multipleValuesSetting": {"shape": "S5q"}, "subSlotSetting": {"shape": "S5r"}}}}, "UpdateSlotType": {"http": {"method": "PUT", "requestUri": "/bots/{botId}/botversions/{botVersion}/botlocales/{localeId}/slottypes/{slotTypeId}/", "responseCode": 202}, "input": {"type": "structure", "required": ["slotTypeId", "slotTypeName", "botId", "botVersion", "localeId"], "members": {"slotTypeId": {"location": "uri", "locationName": "slotTypeId"}, "slotTypeName": {}, "description": {}, "slotTypeValues": {"shape": "S5y"}, "valueSelectionSetting": {"shape": "S63"}, "parentSlotTypeSignature": {}, "botId": {"location": "uri", "locationName": "botId"}, "botVersion": {"location": "uri", "locationName": "botVersion"}, "localeId": {"location": "uri", "locationName": "localeId"}, "externalSourceSetting": {"shape": "S6a"}, "compositeSlotTypeSetting": {"shape": "S6f"}}}, "output": {"type": "structure", "members": {"slotTypeId": {}, "slotTypeName": {}, "description": {}, "slotTypeValues": {"shape": "S5y"}, "valueSelectionSetting": {"shape": "S63"}, "parentSlotTypeSignature": {}, "botId": {}, "botVersion": {}, "localeId": {}, "creationDateTime": {"type": "timestamp"}, "lastUpdatedDateTime": {"type": "timestamp"}, "externalSourceSetting": {"shape": "S6a"}, "compositeSlotTypeSetting": {"shape": "S6f"}}}}}, "shapes": {"Sa": {"type": "list", "member": {"type": "structure", "members": {"itemId": {}, "errorMessage": {}, "errorCode": {}}}}, "Sf": {"type": "list", "member": {"shape": "Sg"}}, "Sg": {"type": "structure", "required": ["itemId", "phrase"], "members": {"itemId": {}, "phrase": {}, "weight": {"type": "integer"}, "displayAs": {}}}, "Sx": {"type": "structure", "required": ["childDirected"], "members": {"childDirected": {"type": "boolean"}}}, "S10": {"type": "map", "key": {}, "value": {}}, "S14": {"type": "list", "member": {"type": "structure", "required": ["botMemberId", "botMemberName", "botMemberAliasId", "botMemberAliasName", "botMemberVersion"], "members": {"botMemberId": {}, "botMemberName": {}, "botMemberAliasId": {}, "botMemberAliasName": {}, "botMemberVersion": {}}}}, "S1c": {"type": "map", "key": {}, "value": {"type": "structure", "required": ["enabled"], "members": {"enabled": {"type": "boolean"}, "codeHookSpecification": {"type": "structure", "required": ["lambdaCodeHook"], "members": {"lambdaCodeHook": {"type": "structure", "required": ["lambdaARN", "codeHookInterfaceVersion"], "members": {"lambdaARN": {}, "codeHookInterfaceVersion": {}}}}}}}}, "S1j": {"type": "structure", "members": {"textLogSettings": {"type": "list", "member": {"type": "structure", "required": ["enabled", "destination"], "members": {"enabled": {"type": "boolean"}, "destination": {"type": "structure", "required": ["cloudWatch"], "members": {"cloudWatch": {"type": "structure", "required": ["cloudWatchLogGroupArn", "logPrefix"], "members": {"cloudWatchLogGroupArn": {}, "logPrefix": {}}}}}}}}, "audioLogSettings": {"type": "list", "member": {"type": "structure", "required": ["enabled", "destination"], "members": {"enabled": {"type": "boolean"}, "destination": {"type": "structure", "required": ["s3Bucket"], "members": {"s3Bucket": {"type": "structure", "required": ["s3BucketArn", "logPrefix"], "members": {"kmsKeyArn": {}, "s3BucketArn": {}, "logPrefix": {}}}}}}}}}}, "S1w": {"type": "structure", "required": ["detectSentiment"], "members": {"detectSentiment": {"type": "boolean"}}}, "S21": {"type": "structure", "required": ["voiceId"], "members": {"voiceId": {}, "engine": {}}}, "S27": {"type": "map", "key": {}, "value": {"type": "structure", "required": ["sourceBotVersion"], "members": {"sourceBotVersion": {}}}}, "S2b": {"type": "structure", "members": {"botExportSpecification": {"type": "structure", "required": ["botId", "botVersion"], "members": {"botId": {}, "botVersion": {}}}, "botLocaleExportSpecification": {"type": "structure", "required": ["botId", "botVersion", "localeId"], "members": {"botId": {}, "botVersion": {}, "localeId": {}}}, "customVocabularyExportSpecification": {"type": "structure", "required": ["botId", "botVersion", "localeId"], "members": {"botId": {}, "botVersion": {}, "localeId": {}}}}}, "S2g": {"type": "string", "sensitive": true}, "S2l": {"type": "list", "member": {"type": "structure", "required": ["utterance"], "members": {"utterance": {}}}}, "S2o": {"type": "structure", "required": ["enabled"], "members": {"enabled": {"type": "boolean"}}}, "S2p": {"type": "structure", "required": ["enabled"], "members": {"enabled": {"type": "boolean"}, "postFulfillmentStatusSpecification": {"type": "structure", "members": {"successResponse": {"shape": "S2r"}, "failureResponse": {"shape": "S2r"}, "timeoutResponse": {"shape": "S2r"}, "successNextStep": {"shape": "S3a"}, "successConditional": {"shape": "S3m"}, "failureNextStep": {"shape": "S3a"}, "failureConditional": {"shape": "S3m"}, "timeoutNextStep": {"shape": "S3a"}, "timeoutConditional": {"shape": "S3m"}}}, "fulfillmentUpdatesSpecification": {"type": "structure", "required": ["active"], "members": {"active": {"type": "boolean"}, "startResponse": {"type": "structure", "required": ["delayInSeconds", "messageGroups"], "members": {"delayInSeconds": {"type": "integer"}, "messageGroups": {"shape": "S2s"}, "allowInterrupt": {"type": "boolean"}}}, "updateResponse": {"type": "structure", "required": ["frequencyInSeconds", "messageGroups"], "members": {"frequencyInSeconds": {"type": "integer"}, "messageGroups": {"shape": "S2s"}, "allowInterrupt": {"type": "boolean"}}}, "timeoutInSeconds": {"type": "integer"}}}, "active": {"type": "boolean"}}}, "S2r": {"type": "structure", "required": ["messageGroups"], "members": {"messageGroups": {"shape": "S2s"}, "allowInterrupt": {"type": "boolean"}}}, "S2s": {"type": "list", "member": {"type": "structure", "required": ["message"], "members": {"message": {"shape": "S2u"}, "variations": {"type": "list", "member": {"shape": "S2u"}}}}}, "S2u": {"type": "structure", "members": {"plainTextMessage": {"type": "structure", "required": ["value"], "members": {"value": {}}}, "customPayload": {"type": "structure", "required": ["value"], "members": {"value": {}}}, "ssmlMessage": {"type": "structure", "required": ["value"], "members": {"value": {}}}, "imageResponseCard": {"type": "structure", "required": ["title"], "members": {"title": {}, "subtitle": {}, "imageUrl": {}, "buttons": {"type": "list", "member": {"type": "structure", "required": ["text", "value"], "members": {"text": {}, "value": {}}}}}}}}, "S3a": {"type": "structure", "members": {"dialogAction": {"type": "structure", "required": ["type"], "members": {"type": {}, "slotToElicit": {}, "suppressNextMessage": {"type": "boolean"}}}, "intent": {"type": "structure", "members": {"name": {}, "slots": {"type": "map", "key": {}, "value": {"shape": "S3f"}}}}, "sessionAttributes": {"type": "map", "key": {}, "value": {}}}}, "S3f": {"type": "structure", "members": {"shape": {}, "value": {"type": "structure", "members": {"interpretedValue": {}}}, "values": {"type": "list", "member": {"shape": "S3f"}}}}, "S3m": {"type": "structure", "required": ["active", "conditionalBranches", "defaultBranch"], "members": {"active": {"type": "boolean"}, "conditionalBranches": {"type": "list", "member": {"type": "structure", "required": ["name", "condition", "nextStep"], "members": {"name": {}, "condition": {"type": "structure", "required": ["expressionString"], "members": {"expressionString": {}}}, "nextStep": {"shape": "S3a"}, "response": {"shape": "S2r"}}}}, "defaultBranch": {"type": "structure", "members": {"nextStep": {"shape": "S3a"}, "response": {"shape": "S2r"}}}}}, "S3y": {"type": "structure", "required": ["promptSpecification"], "members": {"promptSpecification": {"shape": "S3z"}, "declinationResponse": {"shape": "S2r"}, "active": {"type": "boolean"}, "confirmationResponse": {"shape": "S2r"}, "confirmationNextStep": {"shape": "S3a"}, "confirmationConditional": {"shape": "S3m"}, "declinationNextStep": {"shape": "S3a"}, "declinationConditional": {"shape": "S3m"}, "failureResponse": {"shape": "S2r"}, "failureNextStep": {"shape": "S3a"}, "failureConditional": {"shape": "S3m"}, "codeHook": {"shape": "S4d"}, "elicitationCodeHook": {"shape": "S4f"}}}, "S3z": {"type": "structure", "required": ["messageGroups", "maxRetries"], "members": {"messageGroups": {"shape": "S2s"}, "maxRetries": {"type": "integer"}, "allowInterrupt": {"type": "boolean"}, "messageSelectionStrategy": {}, "promptAttemptsSpecification": {"type": "map", "key": {}, "value": {"type": "structure", "required": ["allowedInputTypes"], "members": {"allowInterrupt": {"type": "boolean"}, "allowedInputTypes": {"type": "structure", "required": ["allowAudioInput", "allowDTMFInput"], "members": {"allowAudioInput": {"type": "boolean"}, "allowDTMFInput": {"type": "boolean"}}}, "audioAndDTMFInputSpecification": {"type": "structure", "required": ["startTimeoutMs"], "members": {"startTimeoutMs": {"type": "integer"}, "audioSpecification": {"type": "structure", "required": ["maxLength<PERSON>", "endTimeoutMs"], "members": {"maxLengthMs": {"type": "integer"}, "endTimeoutMs": {"type": "integer"}}}, "dtmfSpecification": {"type": "structure", "required": ["max<PERSON><PERSON><PERSON>", "endTimeoutMs", "deletionCharacter", "endCharacter"], "members": {"maxLength": {"type": "integer"}, "endTimeoutMs": {"type": "integer"}, "deletionCharacter": {}, "endCharacter": {}}}}}, "textInputSpecification": {"type": "structure", "required": ["startTimeoutMs"], "members": {"startTimeoutMs": {"type": "integer"}}}}}}}}, "S4d": {"type": "structure", "required": ["enableCodeHookInvocation", "active", "postCodeHookSpecification"], "members": {"enableCodeHookInvocation": {"type": "boolean"}, "active": {"type": "boolean"}, "invocationLabel": {}, "postCodeHookSpecification": {"type": "structure", "members": {"successResponse": {"shape": "S2r"}, "successNextStep": {"shape": "S3a"}, "successConditional": {"shape": "S3m"}, "failureResponse": {"shape": "S2r"}, "failureNextStep": {"shape": "S3a"}, "failureConditional": {"shape": "S3m"}, "timeoutResponse": {"shape": "S2r"}, "timeoutNextStep": {"shape": "S3a"}, "timeoutConditional": {"shape": "S3m"}}}}}, "S4f": {"type": "structure", "required": ["enableCodeHookInvocation"], "members": {"enableCodeHookInvocation": {"type": "boolean"}, "invocationLabel": {}}}, "S4g": {"type": "structure", "members": {"closingResponse": {"shape": "S2r"}, "active": {"type": "boolean"}, "nextStep": {"shape": "S3a"}, "conditional": {"shape": "S3m"}}}, "S4h": {"type": "list", "member": {"type": "structure", "required": ["name"], "members": {"name": {}}}}, "S4j": {"type": "list", "member": {"type": "structure", "required": ["name", "timeToLiveInSeconds", "turnsToLive"], "members": {"name": {}, "timeToLiveInSeconds": {"type": "integer"}, "turnsToLive": {"type": "integer"}}}}, "S4n": {"type": "structure", "required": ["kendraIndex"], "members": {"kendraIndex": {}, "queryFilterStringEnabled": {"type": "boolean"}, "queryFilterString": {}}}, "S4q": {"type": "structure", "members": {"initialResponse": {"shape": "S2r"}, "nextStep": {"shape": "S3a"}, "conditional": {"shape": "S3m"}, "codeHook": {"shape": "S4d"}}}, "S5d": {"type": "structure", "required": ["slotConstraint"], "members": {"defaultValueSpecification": {"shape": "S5e"}, "slotConstraint": {}, "promptSpecification": {"shape": "S3z"}, "sampleUtterances": {"shape": "S2l"}, "waitAndContinueSpecification": {"shape": "S5j"}, "slotCaptureSetting": {"type": "structure", "members": {"captureResponse": {"shape": "S2r"}, "captureNextStep": {"shape": "S3a"}, "captureConditional": {"shape": "S3m"}, "failureResponse": {"shape": "S2r"}, "failureNextStep": {"shape": "S3a"}, "failureConditional": {"shape": "S3m"}, "codeHook": {"shape": "S4d"}, "elicitationCodeHook": {"shape": "S4f"}}}}}, "S5e": {"type": "structure", "required": ["defaultValueList"], "members": {"defaultValueList": {"type": "list", "member": {"type": "structure", "required": ["defaultValue"], "members": {"defaultValue": {}}}}}}, "S5j": {"type": "structure", "required": ["waitingResponse", "continueResponse"], "members": {"waitingResponse": {"shape": "S2r"}, "continueResponse": {"shape": "S2r"}, "stillWaitingResponse": {"type": "structure", "required": ["messageGroups", "frequencyInSeconds", "timeoutInSeconds"], "members": {"messageGroups": {"shape": "S2s"}, "frequencyInSeconds": {"type": "integer"}, "timeoutInSeconds": {"type": "integer"}, "allowInterrupt": {"type": "boolean"}}}, "active": {"type": "boolean"}}}, "S5o": {"type": "structure", "required": ["obfuscationSettingType"], "members": {"obfuscationSettingType": {}}}, "S5q": {"type": "structure", "members": {"allowMultipleValues": {"type": "boolean"}}}, "S5r": {"type": "structure", "members": {"expression": {}, "slotSpecifications": {"type": "map", "key": {}, "value": {"type": "structure", "required": ["slotTypeId", "valueElicitationSetting"], "members": {"slotTypeId": {}, "valueElicitationSetting": {"type": "structure", "required": ["promptSpecification"], "members": {"defaultValueSpecification": {"shape": "S5e"}, "promptSpecification": {"shape": "S3z"}, "sampleUtterances": {"shape": "S2l"}, "waitAndContinueSpecification": {"shape": "S5j"}}}}}}}}, "S5y": {"type": "list", "member": {"type": "structure", "members": {"sampleValue": {"shape": "S60"}, "synonyms": {"type": "list", "member": {"shape": "S60"}}}}}, "S60": {"type": "structure", "required": ["value"], "members": {"value": {}}}, "S63": {"type": "structure", "required": ["resolutionStrategy"], "members": {"resolutionStrategy": {}, "regexFilter": {"type": "structure", "required": ["pattern"], "members": {"pattern": {}}}, "advancedRecognitionSetting": {"type": "structure", "members": {"audioRecognitionStrategy": {}}}}}, "S6a": {"type": "structure", "members": {"grammarSlotTypeSetting": {"type": "structure", "members": {"source": {"type": "structure", "required": ["s3BucketName", "s3ObjectKey"], "members": {"s3BucketName": {}, "s3ObjectKey": {}, "kmsKeyArn": {}}}}}}}, "S6f": {"type": "structure", "members": {"subSlots": {"type": "list", "member": {"type": "structure", "required": ["name", "slotTypeId"], "members": {"name": {}, "slotTypeId": {}}}}}}, "S7f": {"type": "list", "member": {}}, "S7l": {"type": "list", "member": {"type": "structure", "required": ["botId", "botVersion"], "members": {"botId": {}, "botVersion": {}}}}, "S7t": {"type": "list", "member": {}}, "S7y": {"type": "structure", "members": {"s3BucketTranscriptSource": {"type": "structure", "required": ["s3BucketName", "transcriptFormat"], "members": {"s3BucketName": {}, "pathFormat": {"type": "structure", "members": {"objectPrefixes": {"type": "list", "member": {}}}}, "transcriptFormat": {}, "transcriptFilter": {"type": "structure", "members": {"lexTranscriptFilter": {"type": "structure", "members": {"dateRangeFilter": {"type": "structure", "required": ["startDateTime", "endDateTime"], "members": {"startDateTime": {"type": "timestamp"}, "endDateTime": {"type": "timestamp"}}}}}}}, "kmsKeyArn": {}}}}}, "S87": {"type": "structure", "members": {"kmsKeyArn": {}, "botLocaleExportPassword": {"shape": "S88"}, "associatedTranscriptsPassword": {"shape": "S88"}}}, "S88": {"type": "string", "sensitive": true}, "S8m": {"type": "structure", "members": {"botImportSpecification": {"type": "structure", "required": ["botName", "roleArn", "dataPrivacy"], "members": {"botName": {}, "roleArn": {}, "dataPrivacy": {"shape": "Sx"}, "idleSessionTTLInSeconds": {"type": "integer"}, "botTags": {"shape": "S10"}, "testBotAliasTags": {"shape": "S10"}}}, "botLocaleImportSpecification": {"type": "structure", "required": ["botId", "botVersion", "localeId"], "members": {"botId": {}, "botVersion": {}, "localeId": {}, "nluIntentConfidenceThreshold": {"type": "double"}, "voiceSettings": {"shape": "S21"}}}, "customVocabularyImportSpecification": {"type": "structure", "required": ["botId", "botVersion", "localeId"], "members": {"botId": {}, "botVersion": {}, "localeId": {}}}}}, "S8u": {"type": "list", "member": {"type": "structure", "required": ["priority", "slotId"], "members": {"priority": {"type": "integer"}, "slotId": {}}}}, "S94": {"type": "structure", "required": ["relativeAggregationDuration"], "members": {"relativeAggregationDuration": {"type": "structure", "required": ["timeDimension", "timeValue"], "members": {"timeDimension": {}, "timeValue": {"type": "integer"}}}}}, "S9e": {"type": "list", "member": {}}}}