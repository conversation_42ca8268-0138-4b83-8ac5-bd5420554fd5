{"version": "2.0", "metadata": {"apiVersion": "2020-08-18", "endpointPrefix": "grafana", "jsonVersion": "1.1", "protocol": "rest-json", "serviceFullName": "Amazon Managed Grafana", "serviceId": "grafana", "signatureVersion": "v4", "signingName": "grafana", "uid": "grafana-2020-08-18"}, "operations": {"AssociateLicense": {"http": {"requestUri": "/workspaces/{workspaceId}/licenses/{licenseType}", "responseCode": 202}, "input": {"type": "structure", "required": ["licenseType", "workspaceId"], "members": {"licenseType": {"location": "uri", "locationName": "licenseType"}, "workspaceId": {"location": "uri", "locationName": "workspaceId"}}}, "output": {"type": "structure", "required": ["workspace"], "members": {"workspace": {"shape": "S5"}}}}, "CreateWorkspace": {"http": {"requestUri": "/workspaces", "responseCode": 202}, "input": {"type": "structure", "required": ["accountAccessType", "authenticationProviders", "permissionType"], "members": {"accountAccessType": {}, "authenticationProviders": {"shape": "S8"}, "clientToken": {"idempotencyToken": true}, "configuration": {"jsonvalue": true}, "organizationRoleName": {"shape": "Sl"}, "permissionType": {}, "stackSetName": {}, "tags": {"shape": "<PERSON>"}, "vpcConfiguration": {"shape": "Su"}, "workspaceDataSources": {"shape": "Sc"}, "workspaceDescription": {"shape": "Se"}, "workspaceName": {"shape": "Si"}, "workspaceNotificationDestinations": {"shape": "Sj"}, "workspaceOrganizationalUnits": {"shape": "Sm"}, "workspaceRoleArn": {"shape": "Sz"}}}, "output": {"type": "structure", "required": ["workspace"], "members": {"workspace": {"shape": "S5"}}}, "idempotent": true}, "CreateWorkspaceApiKey": {"http": {"requestUri": "/workspaces/{workspaceId}/apikeys", "responseCode": 200}, "input": {"type": "structure", "required": ["keyName", "keyRole", "secondsToLive", "workspaceId"], "members": {"keyName": {}, "keyRole": {}, "secondsToLive": {"type": "integer"}, "workspaceId": {"location": "uri", "locationName": "workspaceId"}}}, "output": {"type": "structure", "required": ["key", "keyName", "workspaceId"], "members": {"key": {"type": "string", "sensitive": true}, "keyName": {}, "workspaceId": {}}}}, "DeleteWorkspace": {"http": {"method": "DELETE", "requestUri": "/workspaces/{workspaceId}", "responseCode": 202}, "input": {"type": "structure", "required": ["workspaceId"], "members": {"workspaceId": {"location": "uri", "locationName": "workspaceId"}}}, "output": {"type": "structure", "required": ["workspace"], "members": {"workspace": {"shape": "S5"}}}, "idempotent": true}, "DeleteWorkspaceApiKey": {"http": {"method": "DELETE", "requestUri": "/workspaces/{workspaceId}/apikeys/{keyName}", "responseCode": 200}, "input": {"type": "structure", "required": ["keyName", "workspaceId"], "members": {"keyName": {"location": "uri", "locationName": "keyName"}, "workspaceId": {"location": "uri", "locationName": "workspaceId"}}}, "output": {"type": "structure", "required": ["keyName", "workspaceId"], "members": {"keyName": {}, "workspaceId": {}}}}, "DescribeWorkspace": {"http": {"method": "GET", "requestUri": "/workspaces/{workspaceId}", "responseCode": 200}, "input": {"type": "structure", "required": ["workspaceId"], "members": {"workspaceId": {"location": "uri", "locationName": "workspaceId"}}}, "output": {"type": "structure", "required": ["workspace"], "members": {"workspace": {"shape": "S5"}}}}, "DescribeWorkspaceAuthentication": {"http": {"method": "GET", "requestUri": "/workspaces/{workspaceId}/authentication", "responseCode": 200}, "input": {"type": "structure", "required": ["workspaceId"], "members": {"workspaceId": {"location": "uri", "locationName": "workspaceId"}}}, "output": {"type": "structure", "required": ["authentication"], "members": {"authentication": {"shape": "S1i"}}}}, "DescribeWorkspaceConfiguration": {"http": {"method": "GET", "requestUri": "/workspaces/{workspaceId}/configuration", "responseCode": 200}, "input": {"type": "structure", "required": ["workspaceId"], "members": {"workspaceId": {"location": "uri", "locationName": "workspaceId"}}}, "output": {"type": "structure", "required": ["configuration"], "members": {"configuration": {"jsonvalue": true}}}}, "DisassociateLicense": {"http": {"method": "DELETE", "requestUri": "/workspaces/{workspaceId}/licenses/{licenseType}", "responseCode": 202}, "input": {"type": "structure", "required": ["licenseType", "workspaceId"], "members": {"licenseType": {"location": "uri", "locationName": "licenseType"}, "workspaceId": {"location": "uri", "locationName": "workspaceId"}}}, "output": {"type": "structure", "required": ["workspace"], "members": {"workspace": {"shape": "S5"}}}}, "ListPermissions": {"http": {"method": "GET", "requestUri": "/workspaces/{workspaceId}/permissions", "responseCode": 200}, "input": {"type": "structure", "required": ["workspaceId"], "members": {"groupId": {"location": "querystring", "locationName": "groupId"}, "maxResults": {"location": "querystring", "locationName": "maxResults", "type": "integer"}, "nextToken": {"location": "querystring", "locationName": "nextToken"}, "userId": {"location": "querystring", "locationName": "userId"}, "userType": {"location": "querystring", "locationName": "userType"}, "workspaceId": {"location": "uri", "locationName": "workspaceId"}}}, "output": {"type": "structure", "required": ["permissions"], "members": {"nextToken": {}, "permissions": {"type": "list", "member": {"type": "structure", "required": ["role", "user"], "members": {"role": {}, "user": {"shape": "S2a"}}}}}}}, "ListTagsForResource": {"http": {"method": "GET", "requestUri": "/tags/{resourceArn}", "responseCode": 200}, "input": {"type": "structure", "required": ["resourceArn"], "members": {"resourceArn": {"location": "uri", "locationName": "resourceArn"}}}, "output": {"type": "structure", "members": {"tags": {"shape": "<PERSON>"}}}}, "ListWorkspaces": {"http": {"method": "GET", "requestUri": "/workspaces", "responseCode": 200}, "input": {"type": "structure", "members": {"maxResults": {"location": "querystring", "locationName": "maxResults", "type": "integer"}, "nextToken": {"location": "querystring", "locationName": "nextToken"}}}, "output": {"type": "structure", "required": ["workspaces"], "members": {"nextToken": {}, "workspaces": {"type": "list", "member": {"type": "structure", "required": ["authentication", "created", "endpoint", "grafanaVersion", "id", "modified", "status"], "members": {"authentication": {"shape": "S7"}, "created": {"type": "timestamp"}, "description": {"shape": "Se"}, "endpoint": {}, "grafanaVersion": {}, "id": {}, "modified": {"type": "timestamp"}, "name": {"shape": "Si"}, "notificationDestinations": {"shape": "Sj"}, "status": {}, "tags": {"shape": "<PERSON>"}}}}}}}, "TagResource": {"http": {"requestUri": "/tags/{resourceArn}", "responseCode": 200}, "input": {"type": "structure", "required": ["resourceArn", "tags"], "members": {"resourceArn": {"location": "uri", "locationName": "resourceArn"}, "tags": {"shape": "<PERSON>"}}}, "output": {"type": "structure", "members": {}}}, "UntagResource": {"http": {"method": "DELETE", "requestUri": "/tags/{resourceArn}", "responseCode": 200}, "input": {"type": "structure", "required": ["resourceArn", "tagKeys"], "members": {"resourceArn": {"location": "uri", "locationName": "resourceArn"}, "tagKeys": {"location": "querystring", "locationName": "tagKeys", "type": "list", "member": {}}}}, "output": {"type": "structure", "members": {}}, "idempotent": true}, "UpdatePermissions": {"http": {"method": "PATCH", "requestUri": "/workspaces/{workspaceId}/permissions", "responseCode": 200}, "input": {"type": "structure", "required": ["updateInstructionBatch", "workspaceId"], "members": {"updateInstructionBatch": {"type": "list", "member": {"shape": "S2p"}}, "workspaceId": {"location": "uri", "locationName": "workspaceId"}}}, "output": {"type": "structure", "required": ["errors"], "members": {"errors": {"type": "list", "member": {"type": "structure", "required": ["caused<PERSON>y", "code", "message"], "members": {"causedBy": {"shape": "S2p"}, "code": {"type": "integer"}, "message": {}}}}}}}, "UpdateWorkspace": {"http": {"method": "PUT", "requestUri": "/workspaces/{workspaceId}", "responseCode": 202}, "input": {"type": "structure", "required": ["workspaceId"], "members": {"accountAccessType": {}, "organizationRoleName": {"shape": "Sl"}, "permissionType": {}, "removeVpcConfiguration": {"type": "boolean"}, "stackSetName": {}, "vpcConfiguration": {"shape": "Su"}, "workspaceDataSources": {"shape": "Sc"}, "workspaceDescription": {"shape": "Se"}, "workspaceId": {"location": "uri", "locationName": "workspaceId"}, "workspaceName": {"shape": "Si"}, "workspaceNotificationDestinations": {"shape": "Sj"}, "workspaceOrganizationalUnits": {"shape": "Sm"}, "workspaceRoleArn": {"shape": "Sz"}}}, "output": {"type": "structure", "required": ["workspace"], "members": {"workspace": {"shape": "S5"}}}}, "UpdateWorkspaceAuthentication": {"http": {"requestUri": "/workspaces/{workspaceId}/authentication", "responseCode": 200}, "input": {"type": "structure", "required": ["authenticationProviders", "workspaceId"], "members": {"authenticationProviders": {"shape": "S8"}, "samlConfiguration": {"shape": "S1m"}, "workspaceId": {"location": "uri", "locationName": "workspaceId"}}}, "output": {"type": "structure", "required": ["authentication"], "members": {"authentication": {"shape": "S1i"}}}}, "UpdateWorkspaceConfiguration": {"http": {"method": "PUT", "requestUri": "/workspaces/{workspaceId}/configuration", "responseCode": 202}, "input": {"type": "structure", "required": ["configuration", "workspaceId"], "members": {"configuration": {"jsonvalue": true}, "workspaceId": {"location": "uri", "locationName": "workspaceId"}}}, "output": {"type": "structure", "members": {}}}}, "shapes": {"S5": {"type": "structure", "required": ["authentication", "created", "dataSources", "endpoint", "grafanaVersion", "id", "modified", "status"], "members": {"accountAccessType": {}, "authentication": {"shape": "S7"}, "created": {"type": "timestamp"}, "dataSources": {"shape": "Sc"}, "description": {"shape": "Se"}, "endpoint": {}, "freeTrialConsumed": {"type": "boolean"}, "freeTrialExpiration": {"type": "timestamp"}, "grafanaVersion": {}, "id": {}, "licenseExpiration": {"type": "timestamp"}, "licenseType": {}, "modified": {"type": "timestamp"}, "name": {"shape": "Si"}, "notificationDestinations": {"shape": "Sj"}, "organizationRoleName": {"shape": "Sl"}, "organizationalUnits": {"shape": "Sm"}, "permissionType": {}, "stackSetName": {}, "status": {}, "tags": {"shape": "<PERSON>"}, "vpcConfiguration": {"shape": "Su"}, "workspaceRoleArn": {"shape": "Sz"}}}, "S7": {"type": "structure", "required": ["providers"], "members": {"providers": {"shape": "S8"}, "samlConfigurationStatus": {}}}, "S8": {"type": "list", "member": {}}, "Sc": {"type": "list", "member": {}}, "Se": {"type": "string", "sensitive": true}, "Si": {"type": "string", "sensitive": true}, "Sj": {"type": "list", "member": {}}, "Sl": {"type": "string", "sensitive": true}, "Sm": {"type": "list", "member": {}, "sensitive": true}, "Sr": {"type": "map", "key": {}, "value": {}}, "Su": {"type": "structure", "required": ["securityGroupIds", "subnetIds"], "members": {"securityGroupIds": {"type": "list", "member": {}}, "subnetIds": {"type": "list", "member": {}}}}, "Sz": {"type": "string", "sensitive": true}, "S1i": {"type": "structure", "required": ["providers"], "members": {"awsSso": {"type": "structure", "members": {"ssoClientId": {}}}, "providers": {"shape": "S8"}, "saml": {"type": "structure", "required": ["status"], "members": {"configuration": {"shape": "S1m"}, "status": {}}}}}, "S1m": {"type": "structure", "required": ["idpMetadata"], "members": {"allowedOrganizations": {"type": "list", "member": {}}, "assertionAttributes": {"type": "structure", "members": {"email": {}, "groups": {}, "login": {}, "name": {}, "org": {}, "role": {}}}, "idpMetadata": {"type": "structure", "members": {"url": {}, "xml": {}}, "union": true}, "loginValidityDuration": {"type": "integer"}, "roleValues": {"type": "structure", "members": {"admin": {"shape": "S1v"}, "editor": {"shape": "S1v"}}}}}, "S1v": {"type": "list", "member": {}}, "S2a": {"type": "structure", "required": ["id", "type"], "members": {"id": {}, "type": {}}}, "S2p": {"type": "structure", "required": ["action", "role", "users"], "members": {"action": {}, "role": {}, "users": {"type": "list", "member": {"shape": "S2a"}}}}}}