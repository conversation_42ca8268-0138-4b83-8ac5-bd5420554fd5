{"version": "2.0", "metadata": {"apiVersion": "2018-04-01", "endpointPrefix": "quicksight", "jsonVersion": "1.0", "protocol": "rest-json", "serviceFullName": "Amazon QuickSight", "serviceId": "QuickSight", "signatureVersion": "v4", "uid": "quicksight-2018-04-01"}, "operations": {"CancelIngestion": {"http": {"method": "DELETE", "requestUri": "/accounts/{AwsAccountId}/data-sets/{DataSetId}/ingestions/{IngestionId}"}, "input": {"type": "structure", "required": ["AwsAccountId", "DataSetId", "IngestionId"], "members": {"AwsAccountId": {"location": "uri", "locationName": "AwsAccountId"}, "DataSetId": {"location": "uri", "locationName": "DataSetId"}, "IngestionId": {"location": "uri", "locationName": "IngestionId"}}}, "output": {"type": "structure", "members": {"Arn": {}, "IngestionId": {}, "RequestId": {}, "Status": {"location": "statusCode", "type": "integer"}}}}, "CreateAccountCustomization": {"http": {"requestUri": "/accounts/{AwsAccountId}/customizations"}, "input": {"type": "structure", "required": ["AwsAccountId", "AccountCustomization"], "members": {"AwsAccountId": {"location": "uri", "locationName": "AwsAccountId"}, "Namespace": {"location": "querystring", "locationName": "namespace"}, "AccountCustomization": {"shape": "Sa"}, "Tags": {"shape": "Sb"}}}, "output": {"type": "structure", "members": {"Arn": {}, "AwsAccountId": {}, "Namespace": {}, "AccountCustomization": {"shape": "Sa"}, "RequestId": {}, "Status": {"location": "statusCode", "type": "integer"}}}}, "CreateAccountSubscription": {"http": {"requestUri": "/account/{AwsAccountId}"}, "input": {"type": "structure", "required": ["Edition", "AuthenticationMethod", "AwsAccountId", "Account<PERSON><PERSON>", "NotificationEmail"], "members": {"Edition": {}, "AuthenticationMethod": {}, "AwsAccountId": {"location": "uri", "locationName": "AwsAccountId"}, "AccountName": {}, "NotificationEmail": {}, "ActiveDirectoryName": {}, "Realm": {}, "DirectoryId": {}, "AdminGroup": {"shape": "Sj"}, "AuthorGroup": {"shape": "Sj"}, "ReaderGroup": {"shape": "Sj"}, "FirstName": {}, "LastName": {}, "EmailAddress": {}, "ContactNumber": {}}}, "output": {"type": "structure", "members": {"SignupResponse": {"type": "structure", "members": {"IAMUser": {"type": "boolean"}, "userLoginName": {}, "accountName": {}, "directoryType": {}}}, "Status": {"location": "statusCode", "type": "integer"}, "RequestId": {}}}}, "CreateAnalysis": {"http": {"requestUri": "/accounts/{AwsAccountId}/analyses/{AnalysisId}"}, "input": {"type": "structure", "required": ["AwsAccountId", "AnalysisId", "Name"], "members": {"AwsAccountId": {"location": "uri", "locationName": "AwsAccountId"}, "AnalysisId": {"location": "uri", "locationName": "AnalysisId"}, "Name": {}, "Parameters": {"shape": "Sq"}, "Permissions": {"shape": "S18"}, "SourceEntity": {"shape": "S1c"}, "ThemeArn": {}, "Tags": {"shape": "Sb"}, "Definition": {"shape": "S1g"}}}, "output": {"type": "structure", "members": {"Arn": {}, "AnalysisId": {}, "CreationStatus": {}, "Status": {"location": "statusCode", "type": "integer"}, "RequestId": {}}}}, "CreateDashboard": {"http": {"requestUri": "/accounts/{AwsAccountId}/dashboards/{DashboardId}"}, "input": {"type": "structure", "required": ["AwsAccountId", "DashboardId", "Name"], "members": {"AwsAccountId": {"location": "uri", "locationName": "AwsAccountId"}, "DashboardId": {"location": "uri", "locationName": "DashboardId"}, "Name": {}, "Parameters": {"shape": "Sq"}, "Permissions": {"shape": "S18"}, "SourceEntity": {"shape": "Skq"}, "Tags": {"shape": "Sb"}, "VersionDescription": {}, "DashboardPublishOptions": {"shape": "Skt"}, "ThemeArn": {}, "Definition": {"shape": "Sl8"}}}, "output": {"type": "structure", "members": {"Arn": {}, "VersionArn": {}, "DashboardId": {}, "CreationStatus": {}, "Status": {"location": "statusCode", "type": "integer"}, "RequestId": {}}}}, "CreateDataSet": {"http": {"requestUri": "/accounts/{AwsAccountId}/data-sets"}, "input": {"type": "structure", "required": ["AwsAccountId", "DataSetId", "Name", "PhysicalTableMap", "ImportMode"], "members": {"AwsAccountId": {"location": "uri", "locationName": "AwsAccountId"}, "DataSetId": {}, "Name": {}, "PhysicalTableMap": {"shape": "Sld"}, "LogicalTableMap": {"shape": "Slw"}, "ImportMode": {}, "ColumnGroups": {"shape": "Sms"}, "FieldFolders": {"shape": "Smy"}, "Permissions": {"shape": "S18"}, "RowLevelPermissionDataSet": {"shape": "Sn3"}, "RowLevelPermissionTagConfiguration": {"shape": "Sn7"}, "ColumnLevelPermissionRules": {"shape": "Snd"}, "Tags": {"shape": "Sb"}, "DataSetUsageConfiguration": {"shape": "Snh"}}}, "output": {"type": "structure", "members": {"Arn": {}, "DataSetId": {}, "IngestionArn": {}, "IngestionId": {}, "RequestId": {}, "Status": {"location": "statusCode", "type": "integer"}}}}, "CreateDataSource": {"http": {"requestUri": "/accounts/{AwsAccountId}/data-sources"}, "input": {"type": "structure", "required": ["AwsAccountId", "DataSourceId", "Name", "Type"], "members": {"AwsAccountId": {"location": "uri", "locationName": "AwsAccountId"}, "DataSourceId": {}, "Name": {}, "Type": {}, "DataSourceParameters": {"shape": "Snl"}, "Credentials": {"shape": "Sor"}, "Permissions": {"shape": "S18"}, "VpcConnectionProperties": {"shape": "Soy"}, "SslProperties": {"shape": "Soz"}, "Tags": {"shape": "Sb"}}}, "output": {"type": "structure", "members": {"Arn": {}, "DataSourceId": {}, "CreationStatus": {}, "RequestId": {}, "Status": {"location": "statusCode", "type": "integer"}}}}, "CreateFolder": {"http": {"requestUri": "/accounts/{AwsAccountId}/folders/{FolderId}"}, "input": {"type": "structure", "required": ["AwsAccountId", "FolderId"], "members": {"AwsAccountId": {"location": "uri", "locationName": "AwsAccountId"}, "FolderId": {"location": "uri", "locationName": "FolderId"}, "Name": {}, "FolderType": {}, "ParentFolderArn": {}, "Permissions": {"shape": "S18"}, "Tags": {"shape": "Sb"}}}, "output": {"type": "structure", "members": {"Status": {"location": "statusCode", "type": "integer"}, "Arn": {}, "FolderId": {}, "RequestId": {}}}}, "CreateFolderMembership": {"http": {"method": "PUT", "requestUri": "/accounts/{AwsAccountId}/folders/{FolderId}/members/{MemberType}/{MemberId}"}, "input": {"type": "structure", "required": ["AwsAccountId", "FolderId", "MemberId", "MemberType"], "members": {"AwsAccountId": {"location": "uri", "locationName": "AwsAccountId"}, "FolderId": {"location": "uri", "locationName": "FolderId"}, "MemberId": {"location": "uri", "locationName": "MemberId"}, "MemberType": {"location": "uri", "locationName": "MemberType"}}}, "output": {"type": "structure", "members": {"Status": {"type": "integer"}, "FolderMember": {"type": "structure", "members": {"MemberId": {}, "MemberType": {}}}, "RequestId": {}}}}, "CreateGroup": {"http": {"requestUri": "/accounts/{AwsAccountId}/namespaces/{Namespace}/groups"}, "input": {"type": "structure", "required": ["GroupName", "AwsAccountId", "Namespace"], "members": {"GroupName": {}, "Description": {}, "AwsAccountId": {"location": "uri", "locationName": "AwsAccountId"}, "Namespace": {"location": "uri", "locationName": "Namespace"}}}, "output": {"type": "structure", "members": {"Group": {"shape": "Spe"}, "RequestId": {}, "Status": {"location": "statusCode", "type": "integer"}}}}, "CreateGroupMembership": {"http": {"method": "PUT", "requestUri": "/accounts/{AwsAccountId}/namespaces/{Namespace}/groups/{GroupName}/members/{MemberName}"}, "input": {"type": "structure", "required": ["MemberName", "GroupName", "AwsAccountId", "Namespace"], "members": {"MemberName": {"location": "uri", "locationName": "MemberName"}, "GroupName": {"location": "uri", "locationName": "GroupName"}, "AwsAccountId": {"location": "uri", "locationName": "AwsAccountId"}, "Namespace": {"location": "uri", "locationName": "Namespace"}}}, "output": {"type": "structure", "members": {"GroupMember": {"shape": "Spi"}, "RequestId": {}, "Status": {"location": "statusCode", "type": "integer"}}}}, "CreateIAMPolicyAssignment": {"http": {"requestUri": "/accounts/{AwsAccountId}/namespaces/{Namespace}/iam-policy-assignments/"}, "input": {"type": "structure", "required": ["AwsAccountId", "AssignmentName", "AssignmentStatus", "Namespace"], "members": {"AwsAccountId": {"location": "uri", "locationName": "AwsAccountId"}, "AssignmentName": {}, "AssignmentStatus": {}, "PolicyArn": {}, "Identities": {"shape": "Spm"}, "Namespace": {"location": "uri", "locationName": "Namespace"}}}, "output": {"type": "structure", "members": {"AssignmentName": {}, "AssignmentId": {}, "AssignmentStatus": {}, "PolicyArn": {}, "Identities": {"shape": "Spm"}, "RequestId": {}, "Status": {"location": "statusCode", "type": "integer"}}}}, "CreateIngestion": {"http": {"method": "PUT", "requestUri": "/accounts/{AwsAccountId}/data-sets/{DataSetId}/ingestions/{IngestionId}"}, "input": {"type": "structure", "required": ["DataSetId", "IngestionId", "AwsAccountId"], "members": {"DataSetId": {"location": "uri", "locationName": "DataSetId"}, "IngestionId": {"location": "uri", "locationName": "IngestionId"}, "AwsAccountId": {"location": "uri", "locationName": "AwsAccountId"}, "IngestionType": {}}}, "output": {"type": "structure", "members": {"Arn": {}, "IngestionId": {}, "IngestionStatus": {}, "RequestId": {}, "Status": {"location": "statusCode", "type": "integer"}}}}, "CreateNamespace": {"http": {"requestUri": "/accounts/{AwsAccountId}"}, "input": {"type": "structure", "required": ["AwsAccountId", "Namespace", "IdentityStore"], "members": {"AwsAccountId": {"location": "uri", "locationName": "AwsAccountId"}, "Namespace": {}, "IdentityStore": {}, "Tags": {"shape": "Sb"}}}, "output": {"type": "structure", "members": {"Arn": {}, "Name": {}, "CapacityRegion": {}, "CreationStatus": {}, "IdentityStore": {}, "RequestId": {}, "Status": {"location": "statusCode", "type": "integer"}}}}, "CreateTemplate": {"http": {"requestUri": "/accounts/{AwsAccountId}/templates/{TemplateId}"}, "input": {"type": "structure", "required": ["AwsAccountId", "TemplateId"], "members": {"AwsAccountId": {"location": "uri", "locationName": "AwsAccountId"}, "TemplateId": {"location": "uri", "locationName": "TemplateId"}, "Name": {}, "Permissions": {"shape": "S18"}, "SourceEntity": {"shape": "Sq0"}, "Tags": {"shape": "Sb"}, "VersionDescription": {}, "Definition": {"shape": "Sq3"}}}, "output": {"type": "structure", "members": {"Arn": {}, "VersionArn": {}, "TemplateId": {}, "CreationStatus": {}, "Status": {"location": "statusCode", "type": "integer"}, "RequestId": {}}}}, "CreateTemplateAlias": {"http": {"requestUri": "/accounts/{AwsAccountId}/templates/{TemplateId}/aliases/{AliasName}"}, "input": {"type": "structure", "required": ["AwsAccountId", "TemplateId", "AliasName", "TemplateVersionNumber"], "members": {"AwsAccountId": {"location": "uri", "locationName": "AwsAccountId"}, "TemplateId": {"location": "uri", "locationName": "TemplateId"}, "AliasName": {"location": "uri", "locationName": "AliasName"}, "TemplateVersionNumber": {"type": "long"}}}, "output": {"type": "structure", "members": {"TemplateAlias": {"shape": "Sqi"}, "Status": {"location": "statusCode", "type": "integer"}, "RequestId": {}}}}, "CreateTheme": {"http": {"requestUri": "/accounts/{AwsAccountId}/themes/{ThemeId}"}, "input": {"type": "structure", "required": ["AwsAccountId", "ThemeId", "Name", "BaseThemeId", "Configuration"], "members": {"AwsAccountId": {"location": "uri", "locationName": "AwsAccountId"}, "ThemeId": {"location": "uri", "locationName": "ThemeId"}, "Name": {}, "BaseThemeId": {}, "VersionDescription": {}, "Configuration": {"shape": "Sql"}, "Permissions": {"shape": "S18"}, "Tags": {"shape": "Sb"}}}, "output": {"type": "structure", "members": {"Arn": {}, "VersionArn": {}, "ThemeId": {}, "CreationStatus": {}, "Status": {"location": "statusCode", "type": "integer"}, "RequestId": {}}}}, "CreateThemeAlias": {"http": {"requestUri": "/accounts/{AwsAccountId}/themes/{ThemeId}/aliases/{AliasName}"}, "input": {"type": "structure", "required": ["AwsAccountId", "ThemeId", "AliasName", "ThemeVersionNumber"], "members": {"AwsAccountId": {"location": "uri", "locationName": "AwsAccountId"}, "ThemeId": {"location": "uri", "locationName": "ThemeId"}, "AliasName": {"location": "uri", "locationName": "AliasName"}, "ThemeVersionNumber": {"type": "long"}}}, "output": {"type": "structure", "members": {"ThemeAlias": {"shape": "Sr1"}, "Status": {"location": "statusCode", "type": "integer"}, "RequestId": {}}}}, "DeleteAccountCustomization": {"http": {"method": "DELETE", "requestUri": "/accounts/{AwsAccountId}/customizations"}, "input": {"type": "structure", "required": ["AwsAccountId"], "members": {"AwsAccountId": {"location": "uri", "locationName": "AwsAccountId"}, "Namespace": {"location": "querystring", "locationName": "namespace"}}}, "output": {"type": "structure", "members": {"RequestId": {}, "Status": {"location": "statusCode", "type": "integer"}}}}, "DeleteAccountSubscription": {"http": {"method": "DELETE", "requestUri": "/account/{AwsAccountId}"}, "input": {"type": "structure", "required": ["AwsAccountId"], "members": {"AwsAccountId": {"location": "uri", "locationName": "AwsAccountId"}}}, "output": {"type": "structure", "members": {"RequestId": {}, "Status": {"location": "statusCode", "type": "integer"}}}}, "DeleteAnalysis": {"http": {"method": "DELETE", "requestUri": "/accounts/{AwsAccountId}/analyses/{AnalysisId}"}, "input": {"type": "structure", "required": ["AwsAccountId", "AnalysisId"], "members": {"AwsAccountId": {"location": "uri", "locationName": "AwsAccountId"}, "AnalysisId": {"location": "uri", "locationName": "AnalysisId"}, "RecoveryWindowInDays": {"location": "querystring", "locationName": "recovery-window-in-days", "type": "long"}, "ForceDeleteWithoutRecovery": {"location": "querystring", "locationName": "force-delete-without-recovery", "type": "boolean"}}}, "output": {"type": "structure", "members": {"Status": {"location": "statusCode", "type": "integer"}, "Arn": {}, "AnalysisId": {}, "DeletionTime": {"type": "timestamp"}, "RequestId": {}}}}, "DeleteDashboard": {"http": {"method": "DELETE", "requestUri": "/accounts/{AwsAccountId}/dashboards/{DashboardId}"}, "input": {"type": "structure", "required": ["AwsAccountId", "DashboardId"], "members": {"AwsAccountId": {"location": "uri", "locationName": "AwsAccountId"}, "DashboardId": {"location": "uri", "locationName": "DashboardId"}, "VersionNumber": {"location": "querystring", "locationName": "version-number", "type": "long"}}}, "output": {"type": "structure", "members": {"Status": {"location": "statusCode", "type": "integer"}, "Arn": {}, "DashboardId": {}, "RequestId": {}}}}, "DeleteDataSet": {"http": {"method": "DELETE", "requestUri": "/accounts/{AwsAccountId}/data-sets/{DataSetId}"}, "input": {"type": "structure", "required": ["AwsAccountId", "DataSetId"], "members": {"AwsAccountId": {"location": "uri", "locationName": "AwsAccountId"}, "DataSetId": {"location": "uri", "locationName": "DataSetId"}}}, "output": {"type": "structure", "members": {"Arn": {}, "DataSetId": {}, "RequestId": {}, "Status": {"location": "statusCode", "type": "integer"}}}}, "DeleteDataSource": {"http": {"method": "DELETE", "requestUri": "/accounts/{AwsAccountId}/data-sources/{DataSourceId}"}, "input": {"type": "structure", "required": ["AwsAccountId", "DataSourceId"], "members": {"AwsAccountId": {"location": "uri", "locationName": "AwsAccountId"}, "DataSourceId": {"location": "uri", "locationName": "DataSourceId"}}}, "output": {"type": "structure", "members": {"Arn": {}, "DataSourceId": {}, "RequestId": {}, "Status": {"location": "statusCode", "type": "integer"}}}}, "DeleteFolder": {"http": {"method": "DELETE", "requestUri": "/accounts/{AwsAccountId}/folders/{FolderId}"}, "input": {"type": "structure", "required": ["AwsAccountId", "FolderId"], "members": {"AwsAccountId": {"location": "uri", "locationName": "AwsAccountId"}, "FolderId": {"location": "uri", "locationName": "FolderId"}}}, "output": {"type": "structure", "members": {"Status": {"location": "statusCode", "type": "integer"}, "Arn": {}, "FolderId": {}, "RequestId": {}}}}, "DeleteFolderMembership": {"http": {"method": "DELETE", "requestUri": "/accounts/{AwsAccountId}/folders/{FolderId}/members/{MemberType}/{MemberId}"}, "input": {"type": "structure", "required": ["AwsAccountId", "FolderId", "MemberId", "MemberType"], "members": {"AwsAccountId": {"location": "uri", "locationName": "AwsAccountId"}, "FolderId": {"location": "uri", "locationName": "FolderId"}, "MemberId": {"location": "uri", "locationName": "MemberId"}, "MemberType": {"location": "uri", "locationName": "MemberType"}}}, "output": {"type": "structure", "members": {"Status": {"type": "integer"}, "RequestId": {}}}}, "DeleteGroup": {"http": {"method": "DELETE", "requestUri": "/accounts/{AwsAccountId}/namespaces/{Namespace}/groups/{GroupName}"}, "input": {"type": "structure", "required": ["GroupName", "AwsAccountId", "Namespace"], "members": {"GroupName": {"location": "uri", "locationName": "GroupName"}, "AwsAccountId": {"location": "uri", "locationName": "AwsAccountId"}, "Namespace": {"location": "uri", "locationName": "Namespace"}}}, "output": {"type": "structure", "members": {"RequestId": {}, "Status": {"location": "statusCode", "type": "integer"}}}}, "DeleteGroupMembership": {"http": {"method": "DELETE", "requestUri": "/accounts/{AwsAccountId}/namespaces/{Namespace}/groups/{GroupName}/members/{MemberName}"}, "input": {"type": "structure", "required": ["MemberName", "GroupName", "AwsAccountId", "Namespace"], "members": {"MemberName": {"location": "uri", "locationName": "MemberName"}, "GroupName": {"location": "uri", "locationName": "GroupName"}, "AwsAccountId": {"location": "uri", "locationName": "AwsAccountId"}, "Namespace": {"location": "uri", "locationName": "Namespace"}}}, "output": {"type": "structure", "members": {"RequestId": {}, "Status": {"location": "statusCode", "type": "integer"}}}}, "DeleteIAMPolicyAssignment": {"http": {"method": "DELETE", "requestUri": "/accounts/{AwsAccountId}/namespace/{Namespace}/iam-policy-assignments/{AssignmentName}"}, "input": {"type": "structure", "required": ["AwsAccountId", "AssignmentName", "Namespace"], "members": {"AwsAccountId": {"location": "uri", "locationName": "AwsAccountId"}, "AssignmentName": {"location": "uri", "locationName": "AssignmentName"}, "Namespace": {"location": "uri", "locationName": "Namespace"}}}, "output": {"type": "structure", "members": {"AssignmentName": {}, "RequestId": {}, "Status": {"location": "statusCode", "type": "integer"}}}}, "DeleteNamespace": {"http": {"method": "DELETE", "requestUri": "/accounts/{AwsAccountId}/namespaces/{Namespace}"}, "input": {"type": "structure", "required": ["AwsAccountId", "Namespace"], "members": {"AwsAccountId": {"location": "uri", "locationName": "AwsAccountId"}, "Namespace": {"location": "uri", "locationName": "Namespace"}}}, "output": {"type": "structure", "members": {"RequestId": {}, "Status": {"location": "statusCode", "type": "integer"}}}}, "DeleteTemplate": {"http": {"method": "DELETE", "requestUri": "/accounts/{AwsAccountId}/templates/{TemplateId}"}, "input": {"type": "structure", "required": ["AwsAccountId", "TemplateId"], "members": {"AwsAccountId": {"location": "uri", "locationName": "AwsAccountId"}, "TemplateId": {"location": "uri", "locationName": "TemplateId"}, "VersionNumber": {"location": "querystring", "locationName": "version-number", "type": "long"}}}, "output": {"type": "structure", "members": {"RequestId": {}, "Arn": {}, "TemplateId": {}, "Status": {"location": "statusCode", "type": "integer"}}}}, "DeleteTemplateAlias": {"http": {"method": "DELETE", "requestUri": "/accounts/{AwsAccountId}/templates/{TemplateId}/aliases/{AliasName}"}, "input": {"type": "structure", "required": ["AwsAccountId", "TemplateId", "AliasName"], "members": {"AwsAccountId": {"location": "uri", "locationName": "AwsAccountId"}, "TemplateId": {"location": "uri", "locationName": "TemplateId"}, "AliasName": {"location": "uri", "locationName": "AliasName"}}}, "output": {"type": "structure", "members": {"Status": {"location": "statusCode", "type": "integer"}, "TemplateId": {}, "AliasName": {}, "Arn": {}, "RequestId": {}}}}, "DeleteTheme": {"http": {"method": "DELETE", "requestUri": "/accounts/{AwsAccountId}/themes/{ThemeId}"}, "input": {"type": "structure", "required": ["AwsAccountId", "ThemeId"], "members": {"AwsAccountId": {"location": "uri", "locationName": "AwsAccountId"}, "ThemeId": {"location": "uri", "locationName": "ThemeId"}, "VersionNumber": {"location": "querystring", "locationName": "version-number", "type": "long"}}}, "output": {"type": "structure", "members": {"Arn": {}, "RequestId": {}, "Status": {"location": "statusCode", "type": "integer"}, "ThemeId": {}}}}, "DeleteThemeAlias": {"http": {"method": "DELETE", "requestUri": "/accounts/{AwsAccountId}/themes/{ThemeId}/aliases/{AliasName}"}, "input": {"type": "structure", "required": ["AwsAccountId", "ThemeId", "AliasName"], "members": {"AwsAccountId": {"location": "uri", "locationName": "AwsAccountId"}, "ThemeId": {"location": "uri", "locationName": "ThemeId"}, "AliasName": {"location": "uri", "locationName": "AliasName"}}}, "output": {"type": "structure", "members": {"AliasName": {}, "Arn": {}, "RequestId": {}, "Status": {"location": "statusCode", "type": "integer"}, "ThemeId": {}}}}, "DeleteUser": {"http": {"method": "DELETE", "requestUri": "/accounts/{AwsAccountId}/namespaces/{Namespace}/users/{UserName}"}, "input": {"type": "structure", "required": ["UserName", "AwsAccountId", "Namespace"], "members": {"UserName": {"location": "uri", "locationName": "UserName"}, "AwsAccountId": {"location": "uri", "locationName": "AwsAccountId"}, "Namespace": {"location": "uri", "locationName": "Namespace"}}}, "output": {"type": "structure", "members": {"RequestId": {}, "Status": {"location": "statusCode", "type": "integer"}}}}, "DeleteUserByPrincipalId": {"http": {"method": "DELETE", "requestUri": "/accounts/{AwsAccountId}/namespaces/{Namespace}/user-principals/{PrincipalId}"}, "input": {"type": "structure", "required": ["PrincipalId", "AwsAccountId", "Namespace"], "members": {"PrincipalId": {"location": "uri", "locationName": "PrincipalId"}, "AwsAccountId": {"location": "uri", "locationName": "AwsAccountId"}, "Namespace": {"location": "uri", "locationName": "Namespace"}}}, "output": {"type": "structure", "members": {"RequestId": {}, "Status": {"location": "statusCode", "type": "integer"}}}}, "DescribeAccountCustomization": {"http": {"method": "GET", "requestUri": "/accounts/{AwsAccountId}/customizations"}, "input": {"type": "structure", "required": ["AwsAccountId"], "members": {"AwsAccountId": {"location": "uri", "locationName": "AwsAccountId"}, "Namespace": {"location": "querystring", "locationName": "namespace"}, "Resolved": {"location": "querystring", "locationName": "resolved", "type": "boolean"}}}, "output": {"type": "structure", "members": {"Arn": {}, "AwsAccountId": {}, "Namespace": {}, "AccountCustomization": {"shape": "Sa"}, "RequestId": {}, "Status": {"location": "statusCode", "type": "integer"}}}}, "DescribeAccountSettings": {"http": {"method": "GET", "requestUri": "/accounts/{AwsAccountId}/settings"}, "input": {"type": "structure", "required": ["AwsAccountId"], "members": {"AwsAccountId": {"location": "uri", "locationName": "AwsAccountId"}}}, "output": {"type": "structure", "members": {"AccountSettings": {"type": "structure", "members": {"AccountName": {}, "Edition": {}, "DefaultNamespace": {}, "NotificationEmail": {}, "PublicSharingEnabled": {"type": "boolean"}, "TerminationProtectionEnabled": {"type": "boolean"}}}, "RequestId": {}, "Status": {"location": "statusCode", "type": "integer"}}}}, "DescribeAccountSubscription": {"http": {"method": "GET", "requestUri": "/account/{AwsAccountId}"}, "input": {"type": "structure", "required": ["AwsAccountId"], "members": {"AwsAccountId": {"location": "uri", "locationName": "AwsAccountId"}}}, "output": {"type": "structure", "members": {"AccountInfo": {"type": "structure", "members": {"AccountName": {}, "Edition": {}, "NotificationEmail": {}, "AuthenticationType": {}, "AccountSubscriptionStatus": {}}}, "Status": {"location": "statusCode", "type": "integer"}, "RequestId": {}}}}, "DescribeAnalysis": {"http": {"method": "GET", "requestUri": "/accounts/{AwsAccountId}/analyses/{AnalysisId}"}, "input": {"type": "structure", "required": ["AwsAccountId", "AnalysisId"], "members": {"AwsAccountId": {"location": "uri", "locationName": "AwsAccountId"}, "AnalysisId": {"location": "uri", "locationName": "AnalysisId"}}}, "output": {"type": "structure", "members": {"Analysis": {"type": "structure", "members": {"AnalysisId": {}, "Arn": {}, "Name": {}, "Status": {}, "Errors": {"shape": "Ssg"}, "DataSetArns": {"shape": "Ssl"}, "ThemeArn": {}, "CreatedTime": {"type": "timestamp"}, "LastUpdatedTime": {"type": "timestamp"}, "Sheets": {"shape": "Ssm"}}}, "Status": {"location": "statusCode", "type": "integer"}, "RequestId": {}}}}, "DescribeAnalysisDefinition": {"http": {"method": "GET", "requestUri": "/accounts/{AwsAccountId}/analyses/{AnalysisId}/definition"}, "input": {"type": "structure", "required": ["AwsAccountId", "AnalysisId"], "members": {"AwsAccountId": {"location": "uri", "locationName": "AwsAccountId"}, "AnalysisId": {"location": "uri", "locationName": "AnalysisId"}}}, "output": {"type": "structure", "members": {"AnalysisId": {}, "Name": {}, "Errors": {"shape": "Ssg"}, "ResourceStatus": {}, "ThemeArn": {}, "Definition": {"shape": "S1g"}, "Status": {"location": "statusCode", "type": "integer"}, "RequestId": {}}}}, "DescribeAnalysisPermissions": {"http": {"method": "GET", "requestUri": "/accounts/{AwsAccountId}/analyses/{AnalysisId}/permissions"}, "input": {"type": "structure", "required": ["AwsAccountId", "AnalysisId"], "members": {"AwsAccountId": {"location": "uri", "locationName": "AwsAccountId"}, "AnalysisId": {"location": "uri", "locationName": "AnalysisId"}}}, "output": {"type": "structure", "members": {"AnalysisId": {}, "AnalysisArn": {}, "Permissions": {"shape": "S18"}, "Status": {"location": "statusCode", "type": "integer"}, "RequestId": {}}}}, "DescribeDashboard": {"http": {"method": "GET", "requestUri": "/accounts/{AwsAccountId}/dashboards/{DashboardId}"}, "input": {"type": "structure", "required": ["AwsAccountId", "DashboardId"], "members": {"AwsAccountId": {"location": "uri", "locationName": "AwsAccountId"}, "DashboardId": {"location": "uri", "locationName": "DashboardId"}, "VersionNumber": {"location": "querystring", "locationName": "version-number", "type": "long"}, "AliasName": {"location": "querystring", "locationName": "alias-name"}}}, "output": {"type": "structure", "members": {"Dashboard": {"type": "structure", "members": {"DashboardId": {}, "Arn": {}, "Name": {}, "Version": {"type": "structure", "members": {"CreatedTime": {"type": "timestamp"}, "Errors": {"shape": "Ssw"}, "VersionNumber": {"type": "long"}, "Status": {}, "Arn": {}, "SourceEntityArn": {}, "DataSetArns": {"shape": "Ssl"}, "Description": {}, "ThemeArn": {}, "Sheets": {"shape": "Ssm"}}}, "CreatedTime": {"type": "timestamp"}, "LastPublishedTime": {"type": "timestamp"}, "LastUpdatedTime": {"type": "timestamp"}}}, "Status": {"location": "statusCode", "type": "integer"}, "RequestId": {}}}}, "DescribeDashboardDefinition": {"http": {"method": "GET", "requestUri": "/accounts/{AwsAccountId}/dashboards/{DashboardId}/definition"}, "input": {"type": "structure", "required": ["AwsAccountId", "DashboardId"], "members": {"AwsAccountId": {"location": "uri", "locationName": "AwsAccountId"}, "DashboardId": {"location": "uri", "locationName": "DashboardId"}, "VersionNumber": {"location": "querystring", "locationName": "version-number", "type": "long"}, "AliasName": {"location": "querystring", "locationName": "alias-name"}}}, "output": {"type": "structure", "members": {"DashboardId": {}, "Errors": {"shape": "Ssw"}, "Name": {}, "ResourceStatus": {}, "ThemeArn": {}, "Definition": {"shape": "Sl8"}, "Status": {"location": "statusCode", "type": "integer"}, "RequestId": {}, "DashboardPublishOptions": {"shape": "Skt"}}}}, "DescribeDashboardPermissions": {"http": {"method": "GET", "requestUri": "/accounts/{AwsAccountId}/dashboards/{DashboardId}/permissions"}, "input": {"type": "structure", "required": ["AwsAccountId", "DashboardId"], "members": {"AwsAccountId": {"location": "uri", "locationName": "AwsAccountId"}, "DashboardId": {"location": "uri", "locationName": "DashboardId"}}}, "output": {"type": "structure", "members": {"DashboardId": {}, "DashboardArn": {}, "Permissions": {"shape": "S18"}, "Status": {"location": "statusCode", "type": "integer"}, "RequestId": {}, "LinkSharingConfiguration": {"shape": "St3"}}}}, "DescribeDataSet": {"http": {"method": "GET", "requestUri": "/accounts/{AwsAccountId}/data-sets/{DataSetId}"}, "input": {"type": "structure", "required": ["AwsAccountId", "DataSetId"], "members": {"AwsAccountId": {"location": "uri", "locationName": "AwsAccountId"}, "DataSetId": {"location": "uri", "locationName": "DataSetId"}}}, "output": {"type": "structure", "members": {"DataSet": {"type": "structure", "members": {"Arn": {}, "DataSetId": {}, "Name": {}, "CreatedTime": {"type": "timestamp"}, "LastUpdatedTime": {"type": "timestamp"}, "PhysicalTableMap": {"shape": "Sld"}, "LogicalTableMap": {"shape": "Slw"}, "OutputColumns": {"type": "list", "member": {"type": "structure", "members": {"Name": {}, "Description": {}, "Type": {}}}}, "ImportMode": {}, "ConsumedSpiceCapacityInBytes": {"type": "long"}, "ColumnGroups": {"shape": "Sms"}, "FieldFolders": {"shape": "Smy"}, "RowLevelPermissionDataSet": {"shape": "Sn3"}, "RowLevelPermissionTagConfiguration": {"shape": "Sn7"}, "ColumnLevelPermissionRules": {"shape": "Snd"}, "DataSetUsageConfiguration": {"shape": "Snh"}}}, "RequestId": {}, "Status": {"location": "statusCode", "type": "integer"}}}}, "DescribeDataSetPermissions": {"http": {"method": "GET", "requestUri": "/accounts/{AwsAccountId}/data-sets/{DataSetId}/permissions"}, "input": {"type": "structure", "required": ["AwsAccountId", "DataSetId"], "members": {"AwsAccountId": {"location": "uri", "locationName": "AwsAccountId"}, "DataSetId": {"location": "uri", "locationName": "DataSetId"}}}, "output": {"type": "structure", "members": {"DataSetArn": {}, "DataSetId": {}, "Permissions": {"shape": "S18"}, "RequestId": {}, "Status": {"location": "statusCode", "type": "integer"}}}}, "DescribeDataSource": {"http": {"method": "GET", "requestUri": "/accounts/{AwsAccountId}/data-sources/{DataSourceId}"}, "input": {"type": "structure", "required": ["AwsAccountId", "DataSourceId"], "members": {"AwsAccountId": {"location": "uri", "locationName": "AwsAccountId"}, "DataSourceId": {"location": "uri", "locationName": "DataSourceId"}}}, "output": {"type": "structure", "members": {"DataSource": {"shape": "Std"}, "RequestId": {}, "Status": {"location": "statusCode", "type": "integer"}}}}, "DescribeDataSourcePermissions": {"http": {"method": "GET", "requestUri": "/accounts/{AwsAccountId}/data-sources/{DataSourceId}/permissions"}, "input": {"type": "structure", "required": ["AwsAccountId", "DataSourceId"], "members": {"AwsAccountId": {"location": "uri", "locationName": "AwsAccountId"}, "DataSourceId": {"location": "uri", "locationName": "DataSourceId"}}}, "output": {"type": "structure", "members": {"DataSourceArn": {}, "DataSourceId": {}, "Permissions": {"shape": "S18"}, "RequestId": {}, "Status": {"location": "statusCode", "type": "integer"}}}}, "DescribeFolder": {"http": {"method": "GET", "requestUri": "/accounts/{AwsAccountId}/folders/{FolderId}"}, "input": {"type": "structure", "required": ["AwsAccountId", "FolderId"], "members": {"AwsAccountId": {"location": "uri", "locationName": "AwsAccountId"}, "FolderId": {"location": "uri", "locationName": "FolderId"}}}, "output": {"type": "structure", "members": {"Status": {"location": "statusCode", "type": "integer"}, "Folder": {"type": "structure", "members": {"FolderId": {}, "Arn": {}, "Name": {}, "FolderType": {}, "FolderPath": {"type": "list", "member": {}}, "CreatedTime": {"type": "timestamp"}, "LastUpdatedTime": {"type": "timestamp"}}}, "RequestId": {}}}}, "DescribeFolderPermissions": {"http": {"method": "GET", "requestUri": "/accounts/{AwsAccountId}/folders/{FolderId}/permissions"}, "input": {"type": "structure", "required": ["AwsAccountId", "FolderId"], "members": {"AwsAccountId": {"location": "uri", "locationName": "AwsAccountId"}, "FolderId": {"location": "uri", "locationName": "FolderId"}}}, "output": {"type": "structure", "members": {"Status": {"location": "statusCode", "type": "integer"}, "FolderId": {}, "Arn": {}, "Permissions": {"shape": "S18"}, "RequestId": {}}}}, "DescribeFolderResolvedPermissions": {"http": {"method": "GET", "requestUri": "/accounts/{AwsAccountId}/folders/{FolderId}/resolved-permissions"}, "input": {"type": "structure", "required": ["AwsAccountId", "FolderId"], "members": {"AwsAccountId": {"location": "uri", "locationName": "AwsAccountId"}, "FolderId": {"location": "uri", "locationName": "FolderId"}}}, "output": {"type": "structure", "members": {"Status": {"location": "statusCode", "type": "integer"}, "FolderId": {}, "Arn": {}, "Permissions": {"shape": "S18"}, "RequestId": {}}}}, "DescribeGroup": {"http": {"method": "GET", "requestUri": "/accounts/{AwsAccountId}/namespaces/{Namespace}/groups/{GroupName}"}, "input": {"type": "structure", "required": ["GroupName", "AwsAccountId", "Namespace"], "members": {"GroupName": {"location": "uri", "locationName": "GroupName"}, "AwsAccountId": {"location": "uri", "locationName": "AwsAccountId"}, "Namespace": {"location": "uri", "locationName": "Namespace"}}}, "output": {"type": "structure", "members": {"Group": {"shape": "Spe"}, "RequestId": {}, "Status": {"location": "statusCode", "type": "integer"}}}}, "DescribeGroupMembership": {"http": {"method": "GET", "requestUri": "/accounts/{AwsAccountId}/namespaces/{Namespace}/groups/{GroupName}/members/{MemberName}"}, "input": {"type": "structure", "required": ["MemberName", "GroupName", "AwsAccountId", "Namespace"], "members": {"MemberName": {"location": "uri", "locationName": "MemberName"}, "GroupName": {"location": "uri", "locationName": "GroupName"}, "AwsAccountId": {"location": "uri", "locationName": "AwsAccountId"}, "Namespace": {"location": "uri", "locationName": "Namespace"}}}, "output": {"type": "structure", "members": {"GroupMember": {"shape": "Spi"}, "RequestId": {}, "Status": {"location": "statusCode", "type": "integer"}}}}, "DescribeIAMPolicyAssignment": {"http": {"method": "GET", "requestUri": "/accounts/{AwsAccountId}/namespaces/{Namespace}/iam-policy-assignments/{AssignmentName}"}, "input": {"type": "structure", "required": ["AwsAccountId", "AssignmentName", "Namespace"], "members": {"AwsAccountId": {"location": "uri", "locationName": "AwsAccountId"}, "AssignmentName": {"location": "uri", "locationName": "AssignmentName"}, "Namespace": {"location": "uri", "locationName": "Namespace"}}}, "output": {"type": "structure", "members": {"IAMPolicyAssignment": {"type": "structure", "members": {"AwsAccountId": {}, "AssignmentId": {}, "AssignmentName": {}, "PolicyArn": {}, "Identities": {"shape": "Spm"}, "AssignmentStatus": {}}}, "RequestId": {}, "Status": {"location": "statusCode", "type": "integer"}}}}, "DescribeIngestion": {"http": {"method": "GET", "requestUri": "/accounts/{AwsAccountId}/data-sets/{DataSetId}/ingestions/{IngestionId}"}, "input": {"type": "structure", "required": ["AwsAccountId", "DataSetId", "IngestionId"], "members": {"AwsAccountId": {"location": "uri", "locationName": "AwsAccountId"}, "DataSetId": {"location": "uri", "locationName": "DataSetId"}, "IngestionId": {"location": "uri", "locationName": "IngestionId"}}}, "output": {"type": "structure", "members": {"Ingestion": {"shape": "Stz"}, "RequestId": {}, "Status": {"location": "statusCode", "type": "integer"}}}}, "DescribeIpRestriction": {"http": {"method": "GET", "requestUri": "/accounts/{AwsAccountId}/ip-restriction"}, "input": {"type": "structure", "required": ["AwsAccountId"], "members": {"AwsAccountId": {"location": "uri", "locationName": "AwsAccountId"}}}, "output": {"type": "structure", "members": {"AwsAccountId": {}, "IpRestrictionRuleMap": {"shape": "Su8"}, "Enabled": {"type": "boolean"}, "RequestId": {}, "Status": {"location": "statusCode", "type": "integer"}}}}, "DescribeNamespace": {"http": {"method": "GET", "requestUri": "/accounts/{AwsAccountId}/namespaces/{Namespace}"}, "input": {"type": "structure", "required": ["AwsAccountId", "Namespace"], "members": {"AwsAccountId": {"location": "uri", "locationName": "AwsAccountId"}, "Namespace": {"location": "uri", "locationName": "Namespace"}}}, "output": {"type": "structure", "members": {"Namespace": {"shape": "<PERSON>"}, "RequestId": {}, "Status": {"location": "statusCode", "type": "integer"}}}}, "DescribeTemplate": {"http": {"method": "GET", "requestUri": "/accounts/{AwsAccountId}/templates/{TemplateId}"}, "input": {"type": "structure", "required": ["AwsAccountId", "TemplateId"], "members": {"AwsAccountId": {"location": "uri", "locationName": "AwsAccountId"}, "TemplateId": {"location": "uri", "locationName": "TemplateId"}, "VersionNumber": {"location": "querystring", "locationName": "version-number", "type": "long"}, "AliasName": {"location": "querystring", "locationName": "alias-name"}}}, "output": {"type": "structure", "members": {"Template": {"type": "structure", "members": {"Arn": {}, "Name": {}, "Version": {"type": "structure", "members": {"CreatedTime": {"type": "timestamp"}, "Errors": {"shape": "Sul"}, "VersionNumber": {"type": "long"}, "Status": {}, "DataSetConfigurations": {"shape": "Sq4"}, "Description": {}, "SourceEntityArn": {}, "ThemeArn": {}, "Sheets": {"shape": "Ssm"}}}, "TemplateId": {}, "LastUpdatedTime": {"type": "timestamp"}, "CreatedTime": {"type": "timestamp"}}}, "Status": {"location": "statusCode", "type": "integer"}, "RequestId": {}}}}, "DescribeTemplateAlias": {"http": {"method": "GET", "requestUri": "/accounts/{AwsAccountId}/templates/{TemplateId}/aliases/{AliasName}"}, "input": {"type": "structure", "required": ["AwsAccountId", "TemplateId", "AliasName"], "members": {"AwsAccountId": {"location": "uri", "locationName": "AwsAccountId"}, "TemplateId": {"location": "uri", "locationName": "TemplateId"}, "AliasName": {"location": "uri", "locationName": "AliasName"}}}, "output": {"type": "structure", "members": {"TemplateAlias": {"shape": "Sqi"}, "Status": {"location": "statusCode", "type": "integer"}, "RequestId": {}}}}, "DescribeTemplateDefinition": {"http": {"method": "GET", "requestUri": "/accounts/{AwsAccountId}/templates/{TemplateId}/definition"}, "input": {"type": "structure", "required": ["AwsAccountId", "TemplateId"], "members": {"AwsAccountId": {"location": "uri", "locationName": "AwsAccountId"}, "TemplateId": {"location": "uri", "locationName": "TemplateId"}, "VersionNumber": {"location": "querystring", "locationName": "version-number", "type": "long"}, "AliasName": {"location": "querystring", "locationName": "alias-name"}}}, "output": {"type": "structure", "members": {"Name": {}, "TemplateId": {}, "Errors": {"shape": "Sul"}, "ResourceStatus": {}, "ThemeArn": {}, "Definition": {"shape": "Sq3"}, "Status": {"location": "statusCode", "type": "integer"}, "RequestId": {}}}}, "DescribeTemplatePermissions": {"http": {"method": "GET", "requestUri": "/accounts/{AwsAccountId}/templates/{TemplateId}/permissions"}, "input": {"type": "structure", "required": ["AwsAccountId", "TemplateId"], "members": {"AwsAccountId": {"location": "uri", "locationName": "AwsAccountId"}, "TemplateId": {"location": "uri", "locationName": "TemplateId"}}}, "output": {"type": "structure", "members": {"TemplateId": {}, "TemplateArn": {}, "Permissions": {"shape": "S18"}, "RequestId": {}, "Status": {"location": "statusCode", "type": "integer"}}}}, "DescribeTheme": {"http": {"method": "GET", "requestUri": "/accounts/{AwsAccountId}/themes/{ThemeId}"}, "input": {"type": "structure", "required": ["AwsAccountId", "ThemeId"], "members": {"AwsAccountId": {"location": "uri", "locationName": "AwsAccountId"}, "ThemeId": {"location": "uri", "locationName": "ThemeId"}, "VersionNumber": {"location": "querystring", "locationName": "version-number", "type": "long"}, "AliasName": {"location": "querystring", "locationName": "alias-name"}}}, "output": {"type": "structure", "members": {"Theme": {"type": "structure", "members": {"Arn": {}, "Name": {}, "ThemeId": {}, "Version": {"type": "structure", "members": {"VersionNumber": {"type": "long"}, "Arn": {}, "Description": {}, "BaseThemeId": {}, "CreatedTime": {"type": "timestamp"}, "Configuration": {"shape": "Sql"}, "Errors": {"type": "list", "member": {"type": "structure", "members": {"Type": {}, "Message": {}}}}, "Status": {}}}, "CreatedTime": {"type": "timestamp"}, "LastUpdatedTime": {"type": "timestamp"}, "Type": {}}}, "Status": {"location": "statusCode", "type": "integer"}, "RequestId": {}}}}, "DescribeThemeAlias": {"http": {"method": "GET", "requestUri": "/accounts/{AwsAccountId}/themes/{ThemeId}/aliases/{AliasName}"}, "input": {"type": "structure", "required": ["AwsAccountId", "ThemeId", "AliasName"], "members": {"AwsAccountId": {"location": "uri", "locationName": "AwsAccountId"}, "ThemeId": {"location": "uri", "locationName": "ThemeId"}, "AliasName": {"location": "uri", "locationName": "AliasName"}}}, "output": {"type": "structure", "members": {"ThemeAlias": {"shape": "Sr1"}, "Status": {"location": "statusCode", "type": "integer"}, "RequestId": {}}}}, "DescribeThemePermissions": {"http": {"method": "GET", "requestUri": "/accounts/{AwsAccountId}/themes/{ThemeId}/permissions"}, "input": {"type": "structure", "required": ["AwsAccountId", "ThemeId"], "members": {"AwsAccountId": {"location": "uri", "locationName": "AwsAccountId"}, "ThemeId": {"location": "uri", "locationName": "ThemeId"}}}, "output": {"type": "structure", "members": {"ThemeId": {}, "ThemeArn": {}, "Permissions": {"shape": "S18"}, "RequestId": {}, "Status": {"location": "statusCode", "type": "integer"}}}}, "DescribeUser": {"http": {"method": "GET", "requestUri": "/accounts/{AwsAccountId}/namespaces/{Namespace}/users/{UserName}"}, "input": {"type": "structure", "required": ["UserName", "AwsAccountId", "Namespace"], "members": {"UserName": {"location": "uri", "locationName": "UserName"}, "AwsAccountId": {"location": "uri", "locationName": "AwsAccountId"}, "Namespace": {"location": "uri", "locationName": "Namespace"}}}, "output": {"type": "structure", "members": {"User": {"shape": "Sv9"}, "RequestId": {}, "Status": {"location": "statusCode", "type": "integer"}}}}, "GenerateEmbedUrlForAnonymousUser": {"http": {"requestUri": "/accounts/{AwsAccountId}/embed-url/anonymous-user"}, "input": {"type": "structure", "required": ["AwsAccountId", "Namespace", "AuthorizedResourceArns", "ExperienceConfiguration"], "members": {"AwsAccountId": {"location": "uri", "locationName": "AwsAccountId"}, "SessionLifetimeInMinutes": {"type": "long"}, "Namespace": {}, "SessionTags": {"type": "list", "member": {"type": "structure", "required": ["Key", "Value"], "members": {"Key": {}, "Value": {"shape": "Snc"}}}}, "AuthorizedResourceArns": {"type": "list", "member": {}}, "ExperienceConfiguration": {"type": "structure", "members": {"Dashboard": {"type": "structure", "required": ["InitialDashboardId"], "members": {"InitialDashboardId": {}}}, "DashboardVisual": {"type": "structure", "required": ["InitialDashboardVisualId"], "members": {"InitialDashboardVisualId": {"shape": "Svl"}}}, "QSearchBar": {"type": "structure", "required": ["InitialTopicId"], "members": {"InitialTopicId": {}}}}}, "AllowedDomains": {"shape": "Svn"}}}, "output": {"type": "structure", "required": ["EmbedUrl", "Status", "RequestId", "AnonymousUserArn"], "members": {"EmbedUrl": {"shape": "Svp"}, "Status": {"location": "statusCode", "type": "integer"}, "RequestId": {}, "AnonymousUserArn": {}}}}, "GenerateEmbedUrlForRegisteredUser": {"http": {"requestUri": "/accounts/{AwsAccountId}/embed-url/registered-user"}, "input": {"type": "structure", "required": ["AwsAccountId", "UserArn", "ExperienceConfiguration"], "members": {"AwsAccountId": {"location": "uri", "locationName": "AwsAccountId"}, "SessionLifetimeInMinutes": {"type": "long"}, "UserArn": {}, "ExperienceConfiguration": {"type": "structure", "members": {"Dashboard": {"type": "structure", "required": ["InitialDashboardId"], "members": {"InitialDashboardId": {}}}, "QuickSightConsole": {"type": "structure", "members": {"InitialPath": {}}}, "QSearchBar": {"type": "structure", "members": {"InitialTopicId": {}}}, "DashboardVisual": {"type": "structure", "required": ["InitialDashboardVisualId"], "members": {"InitialDashboardVisualId": {"shape": "Svl"}}}}}, "AllowedDomains": {"shape": "Svn"}}}, "output": {"type": "structure", "required": ["EmbedUrl", "Status", "RequestId"], "members": {"EmbedUrl": {"shape": "Svp"}, "Status": {"location": "statusCode", "type": "integer"}, "RequestId": {}}}}, "GetDashboardEmbedUrl": {"http": {"method": "GET", "requestUri": "/accounts/{AwsAccountId}/dashboards/{DashboardId}/embed-url"}, "input": {"type": "structure", "required": ["AwsAccountId", "DashboardId", "IdentityType"], "members": {"AwsAccountId": {"location": "uri", "locationName": "AwsAccountId"}, "DashboardId": {"location": "uri", "locationName": "DashboardId"}, "IdentityType": {"location": "querystring", "locationName": "creds-type"}, "SessionLifetimeInMinutes": {"location": "querystring", "locationName": "session-lifetime", "type": "long"}, "UndoRedoDisabled": {"location": "querystring", "locationName": "undo-redo-disabled", "type": "boolean"}, "ResetDisabled": {"location": "querystring", "locationName": "reset-disabled", "type": "boolean"}, "StatePersistenceEnabled": {"location": "querystring", "locationName": "state-persistence-enabled", "type": "boolean"}, "UserArn": {"location": "querystring", "locationName": "user-arn"}, "Namespace": {"location": "querystring", "locationName": "namespace"}, "AdditionalDashboardIds": {"location": "querystring", "locationName": "additional-dashboard-ids", "type": "list", "member": {}}}}, "output": {"type": "structure", "members": {"EmbedUrl": {"shape": "Svp"}, "Status": {"location": "statusCode", "type": "integer"}, "RequestId": {}}}}, "GetSessionEmbedUrl": {"http": {"method": "GET", "requestUri": "/accounts/{AwsAccountId}/session-embed-url"}, "input": {"type": "structure", "required": ["AwsAccountId"], "members": {"AwsAccountId": {"location": "uri", "locationName": "AwsAccountId"}, "EntryPoint": {"location": "querystring", "locationName": "entry-point"}, "SessionLifetimeInMinutes": {"location": "querystring", "locationName": "session-lifetime", "type": "long"}, "UserArn": {"location": "querystring", "locationName": "user-arn"}}}, "output": {"type": "structure", "members": {"EmbedUrl": {"shape": "Svp"}, "Status": {"location": "statusCode", "type": "integer"}, "RequestId": {}}}}, "ListAnalyses": {"http": {"method": "GET", "requestUri": "/accounts/{AwsAccountId}/analyses"}, "input": {"type": "structure", "required": ["AwsAccountId"], "members": {"AwsAccountId": {"location": "uri", "locationName": "AwsAccountId"}, "NextToken": {"location": "querystring", "locationName": "next-token"}, "MaxResults": {"location": "querystring", "locationName": "max-results", "type": "integer"}}}, "output": {"type": "structure", "members": {"AnalysisSummaryList": {"shape": "Sw8"}, "NextToken": {}, "Status": {"location": "statusCode", "type": "integer"}, "RequestId": {}}}}, "ListDashboardVersions": {"http": {"method": "GET", "requestUri": "/accounts/{AwsAccountId}/dashboards/{DashboardId}/versions"}, "input": {"type": "structure", "required": ["AwsAccountId", "DashboardId"], "members": {"AwsAccountId": {"location": "uri", "locationName": "AwsAccountId"}, "DashboardId": {"location": "uri", "locationName": "DashboardId"}, "NextToken": {"location": "querystring", "locationName": "next-token"}, "MaxResults": {"location": "querystring", "locationName": "max-results", "type": "integer"}}}, "output": {"type": "structure", "members": {"DashboardVersionSummaryList": {"type": "list", "member": {"type": "structure", "members": {"Arn": {}, "CreatedTime": {"type": "timestamp"}, "VersionNumber": {"type": "long"}, "Status": {}, "SourceEntityArn": {}, "Description": {}}}}, "NextToken": {}, "Status": {"location": "statusCode", "type": "integer"}, "RequestId": {}}}}, "ListDashboards": {"http": {"method": "GET", "requestUri": "/accounts/{AwsAccountId}/dashboards"}, "input": {"type": "structure", "required": ["AwsAccountId"], "members": {"AwsAccountId": {"location": "uri", "locationName": "AwsAccountId"}, "NextToken": {"location": "querystring", "locationName": "next-token"}, "MaxResults": {"location": "querystring", "locationName": "max-results", "type": "integer"}}}, "output": {"type": "structure", "members": {"DashboardSummaryList": {"shape": "Swg"}, "NextToken": {}, "Status": {"location": "statusCode", "type": "integer"}, "RequestId": {}}}}, "ListDataSets": {"http": {"method": "GET", "requestUri": "/accounts/{AwsAccountId}/data-sets"}, "input": {"type": "structure", "required": ["AwsAccountId"], "members": {"AwsAccountId": {"location": "uri", "locationName": "AwsAccountId"}, "NextToken": {"location": "querystring", "locationName": "next-token"}, "MaxResults": {"location": "querystring", "locationName": "max-results", "type": "integer"}}}, "output": {"type": "structure", "members": {"DataSetSummaries": {"shape": "Swk"}, "NextToken": {}, "RequestId": {}, "Status": {"location": "statusCode", "type": "integer"}}}}, "ListDataSources": {"http": {"method": "GET", "requestUri": "/accounts/{AwsAccountId}/data-sources"}, "input": {"type": "structure", "required": ["AwsAccountId"], "members": {"AwsAccountId": {"location": "uri", "locationName": "AwsAccountId"}, "NextToken": {"location": "querystring", "locationName": "next-token"}, "MaxResults": {"location": "querystring", "locationName": "max-results", "type": "integer"}}}, "output": {"type": "structure", "members": {"DataSources": {"type": "list", "member": {"shape": "Std"}}, "NextToken": {}, "RequestId": {}, "Status": {"location": "statusCode", "type": "integer"}}}}, "ListFolderMembers": {"http": {"method": "GET", "requestUri": "/accounts/{AwsAccountId}/folders/{FolderId}/members"}, "input": {"type": "structure", "required": ["AwsAccountId", "FolderId"], "members": {"AwsAccountId": {"location": "uri", "locationName": "AwsAccountId"}, "FolderId": {"location": "uri", "locationName": "FolderId"}, "NextToken": {"location": "querystring", "locationName": "next-token"}, "MaxResults": {"location": "querystring", "locationName": "max-results", "type": "integer"}}}, "output": {"type": "structure", "members": {"Status": {"location": "statusCode", "type": "integer"}, "FolderMemberList": {"type": "list", "member": {"type": "structure", "members": {"MemberId": {}, "MemberArn": {}}}}, "NextToken": {}, "RequestId": {}}}}, "ListFolders": {"http": {"method": "GET", "requestUri": "/accounts/{AwsAccountId}/folders"}, "input": {"type": "structure", "required": ["AwsAccountId"], "members": {"AwsAccountId": {"location": "uri", "locationName": "AwsAccountId"}, "NextToken": {"location": "querystring", "locationName": "next-token"}, "MaxResults": {"location": "querystring", "locationName": "max-results", "type": "integer"}}}, "output": {"type": "structure", "members": {"Status": {"location": "statusCode", "type": "integer"}, "FolderSummaryList": {"shape": "Swv"}, "NextToken": {}, "RequestId": {}}}}, "ListGroupMemberships": {"http": {"method": "GET", "requestUri": "/accounts/{AwsAccountId}/namespaces/{Namespace}/groups/{GroupName}/members"}, "input": {"type": "structure", "required": ["GroupName", "AwsAccountId", "Namespace"], "members": {"GroupName": {"location": "uri", "locationName": "GroupName"}, "NextToken": {"location": "querystring", "locationName": "next-token"}, "MaxResults": {"location": "querystring", "locationName": "max-results", "type": "integer"}, "AwsAccountId": {"location": "uri", "locationName": "AwsAccountId"}, "Namespace": {"location": "uri", "locationName": "Namespace"}}}, "output": {"type": "structure", "members": {"GroupMemberList": {"type": "list", "member": {"shape": "Spi"}}, "NextToken": {}, "RequestId": {}, "Status": {"location": "statusCode", "type": "integer"}}}}, "ListGroups": {"http": {"method": "GET", "requestUri": "/accounts/{AwsAccountId}/namespaces/{Namespace}/groups"}, "input": {"type": "structure", "required": ["AwsAccountId", "Namespace"], "members": {"AwsAccountId": {"location": "uri", "locationName": "AwsAccountId"}, "NextToken": {"location": "querystring", "locationName": "next-token"}, "MaxResults": {"location": "querystring", "locationName": "max-results", "type": "integer"}, "Namespace": {"location": "uri", "locationName": "Namespace"}}}, "output": {"type": "structure", "members": {"GroupList": {"shape": "Sx2"}, "NextToken": {}, "RequestId": {}, "Status": {"location": "statusCode", "type": "integer"}}}}, "ListIAMPolicyAssignments": {"http": {"method": "GET", "requestUri": "/accounts/{AwsAccountId}/namespaces/{Namespace}/iam-policy-assignments"}, "input": {"type": "structure", "required": ["AwsAccountId", "Namespace"], "members": {"AwsAccountId": {"location": "uri", "locationName": "AwsAccountId"}, "AssignmentStatus": {}, "Namespace": {"location": "uri", "locationName": "Namespace"}, "NextToken": {"location": "querystring", "locationName": "next-token"}, "MaxResults": {"location": "querystring", "locationName": "max-results", "type": "integer"}}}, "output": {"type": "structure", "members": {"IAMPolicyAssignments": {"type": "list", "member": {"type": "structure", "members": {"AssignmentName": {}, "AssignmentStatus": {}}}}, "NextToken": {}, "RequestId": {}, "Status": {"location": "statusCode", "type": "integer"}}}}, "ListIAMPolicyAssignmentsForUser": {"http": {"method": "GET", "requestUri": "/accounts/{AwsAccountId}/namespaces/{Namespace}/users/{UserName}/iam-policy-assignments"}, "input": {"type": "structure", "required": ["AwsAccountId", "UserName", "Namespace"], "members": {"AwsAccountId": {"location": "uri", "locationName": "AwsAccountId"}, "UserName": {"location": "uri", "locationName": "UserName"}, "NextToken": {"location": "querystring", "locationName": "next-token"}, "MaxResults": {"location": "querystring", "locationName": "max-results", "type": "integer"}, "Namespace": {"location": "uri", "locationName": "Namespace"}}}, "output": {"type": "structure", "members": {"ActiveAssignments": {"type": "list", "member": {"type": "structure", "members": {"AssignmentName": {}, "PolicyArn": {}}}}, "RequestId": {}, "NextToken": {}, "Status": {"location": "statusCode", "type": "integer"}}}}, "ListIngestions": {"http": {"method": "GET", "requestUri": "/accounts/{AwsAccountId}/data-sets/{DataSetId}/ingestions"}, "input": {"type": "structure", "required": ["DataSetId", "AwsAccountId"], "members": {"DataSetId": {"location": "uri", "locationName": "DataSetId"}, "NextToken": {"location": "querystring", "locationName": "next-token"}, "AwsAccountId": {"location": "uri", "locationName": "AwsAccountId"}, "MaxResults": {"location": "querystring", "locationName": "max-results", "type": "integer"}}}, "output": {"type": "structure", "members": {"Ingestions": {"type": "list", "member": {"shape": "Stz"}}, "NextToken": {}, "RequestId": {}, "Status": {"location": "statusCode", "type": "integer"}}}}, "ListNamespaces": {"http": {"method": "GET", "requestUri": "/accounts/{AwsAccountId}/namespaces"}, "input": {"type": "structure", "required": ["AwsAccountId"], "members": {"AwsAccountId": {"location": "uri", "locationName": "AwsAccountId"}, "NextToken": {"location": "querystring", "locationName": "next-token"}, "MaxResults": {"location": "querystring", "locationName": "max-results", "type": "integer"}}}, "output": {"type": "structure", "members": {"Namespaces": {"type": "list", "member": {"shape": "<PERSON>"}}, "NextToken": {}, "RequestId": {}, "Status": {"location": "statusCode", "type": "integer"}}}}, "ListTagsForResource": {"http": {"method": "GET", "requestUri": "/resources/{ResourceArn}/tags"}, "input": {"type": "structure", "required": ["ResourceArn"], "members": {"ResourceArn": {"location": "uri", "locationName": "ResourceArn"}}}, "output": {"type": "structure", "members": {"Tags": {"shape": "Sb"}, "RequestId": {}, "Status": {"location": "statusCode", "type": "integer"}}}}, "ListTemplateAliases": {"http": {"method": "GET", "requestUri": "/accounts/{AwsAccountId}/templates/{TemplateId}/aliases"}, "input": {"type": "structure", "required": ["AwsAccountId", "TemplateId"], "members": {"AwsAccountId": {"location": "uri", "locationName": "AwsAccountId"}, "TemplateId": {"location": "uri", "locationName": "TemplateId"}, "NextToken": {"location": "querystring", "locationName": "next-token"}, "MaxResults": {"location": "querystring", "locationName": "max-result", "type": "integer"}}}, "output": {"type": "structure", "members": {"TemplateAliasList": {"type": "list", "member": {"shape": "Sqi"}}, "Status": {"location": "statusCode", "type": "integer"}, "RequestId": {}, "NextToken": {}}}}, "ListTemplateVersions": {"http": {"method": "GET", "requestUri": "/accounts/{AwsAccountId}/templates/{TemplateId}/versions"}, "input": {"type": "structure", "required": ["AwsAccountId", "TemplateId"], "members": {"AwsAccountId": {"location": "uri", "locationName": "AwsAccountId"}, "TemplateId": {"location": "uri", "locationName": "TemplateId"}, "NextToken": {"location": "querystring", "locationName": "next-token"}, "MaxResults": {"location": "querystring", "locationName": "max-results", "type": "integer"}}}, "output": {"type": "structure", "members": {"TemplateVersionSummaryList": {"type": "list", "member": {"type": "structure", "members": {"Arn": {}, "VersionNumber": {"type": "long"}, "CreatedTime": {"type": "timestamp"}, "Status": {}, "Description": {}}}}, "NextToken": {}, "Status": {"location": "statusCode", "type": "integer"}, "RequestId": {}}}}, "ListTemplates": {"http": {"method": "GET", "requestUri": "/accounts/{AwsAccountId}/templates"}, "input": {"type": "structure", "required": ["AwsAccountId"], "members": {"AwsAccountId": {"location": "uri", "locationName": "AwsAccountId"}, "NextToken": {"location": "querystring", "locationName": "next-token"}, "MaxResults": {"location": "querystring", "locationName": "max-result", "type": "integer"}}}, "output": {"type": "structure", "members": {"TemplateSummaryList": {"type": "list", "member": {"type": "structure", "members": {"Arn": {}, "TemplateId": {}, "Name": {}, "LatestVersionNumber": {"type": "long"}, "CreatedTime": {"type": "timestamp"}, "LastUpdatedTime": {"type": "timestamp"}}}}, "NextToken": {}, "Status": {"location": "statusCode", "type": "integer"}, "RequestId": {}}}}, "ListThemeAliases": {"http": {"method": "GET", "requestUri": "/accounts/{AwsAccountId}/themes/{ThemeId}/aliases"}, "input": {"type": "structure", "required": ["AwsAccountId", "ThemeId"], "members": {"AwsAccountId": {"location": "uri", "locationName": "AwsAccountId"}, "ThemeId": {"location": "uri", "locationName": "ThemeId"}, "NextToken": {"location": "querystring", "locationName": "next-token"}, "MaxResults": {"location": "querystring", "locationName": "max-result", "type": "integer"}}}, "output": {"type": "structure", "members": {"ThemeAliasList": {"type": "list", "member": {"shape": "Sr1"}}, "Status": {"location": "statusCode", "type": "integer"}, "RequestId": {}, "NextToken": {}}}}, "ListThemeVersions": {"http": {"method": "GET", "requestUri": "/accounts/{AwsAccountId}/themes/{ThemeId}/versions"}, "input": {"type": "structure", "required": ["AwsAccountId", "ThemeId"], "members": {"AwsAccountId": {"location": "uri", "locationName": "AwsAccountId"}, "ThemeId": {"location": "uri", "locationName": "ThemeId"}, "NextToken": {"location": "querystring", "locationName": "next-token"}, "MaxResults": {"location": "querystring", "locationName": "max-results", "type": "integer"}}}, "output": {"type": "structure", "members": {"ThemeVersionSummaryList": {"type": "list", "member": {"type": "structure", "members": {"VersionNumber": {"type": "long"}, "Arn": {}, "Description": {}, "CreatedTime": {"type": "timestamp"}, "Status": {}}}}, "NextToken": {}, "Status": {"location": "statusCode", "type": "integer"}, "RequestId": {}}}}, "ListThemes": {"http": {"method": "GET", "requestUri": "/accounts/{AwsAccountId}/themes"}, "input": {"type": "structure", "required": ["AwsAccountId"], "members": {"AwsAccountId": {"location": "uri", "locationName": "AwsAccountId"}, "NextToken": {"location": "querystring", "locationName": "next-token"}, "MaxResults": {"location": "querystring", "locationName": "max-results", "type": "integer"}, "Type": {"location": "querystring", "locationName": "type"}}}, "output": {"type": "structure", "members": {"ThemeSummaryList": {"type": "list", "member": {"type": "structure", "members": {"Arn": {}, "Name": {}, "ThemeId": {}, "LatestVersionNumber": {"type": "long"}, "CreatedTime": {"type": "timestamp"}, "LastUpdatedTime": {"type": "timestamp"}}}}, "NextToken": {}, "Status": {"location": "statusCode", "type": "integer"}, "RequestId": {}}}}, "ListUserGroups": {"http": {"method": "GET", "requestUri": "/accounts/{AwsAccountId}/namespaces/{Namespace}/users/{UserName}/groups"}, "input": {"type": "structure", "required": ["UserName", "AwsAccountId", "Namespace"], "members": {"UserName": {"location": "uri", "locationName": "UserName"}, "AwsAccountId": {"location": "uri", "locationName": "AwsAccountId"}, "Namespace": {"location": "uri", "locationName": "Namespace"}, "NextToken": {"location": "querystring", "locationName": "next-token"}, "MaxResults": {"location": "querystring", "locationName": "max-results", "type": "integer"}}}, "output": {"type": "structure", "members": {"GroupList": {"shape": "Sx2"}, "NextToken": {}, "RequestId": {}, "Status": {"location": "statusCode", "type": "integer"}}}}, "ListUsers": {"http": {"method": "GET", "requestUri": "/accounts/{AwsAccountId}/namespaces/{Namespace}/users"}, "input": {"type": "structure", "required": ["AwsAccountId", "Namespace"], "members": {"AwsAccountId": {"location": "uri", "locationName": "AwsAccountId"}, "NextToken": {"location": "querystring", "locationName": "next-token"}, "MaxResults": {"location": "querystring", "locationName": "max-results", "type": "integer"}, "Namespace": {"location": "uri", "locationName": "Namespace"}}}, "output": {"type": "structure", "members": {"UserList": {"type": "list", "member": {"shape": "Sv9"}}, "NextToken": {}, "RequestId": {}, "Status": {"location": "statusCode", "type": "integer"}}}}, "RegisterUser": {"http": {"requestUri": "/accounts/{AwsAccountId}/namespaces/{Namespace}/users"}, "input": {"type": "structure", "required": ["IdentityType", "Email", "UserRole", "AwsAccountId", "Namespace"], "members": {"IdentityType": {}, "Email": {}, "UserRole": {}, "IamArn": {}, "SessionName": {}, "AwsAccountId": {"location": "uri", "locationName": "AwsAccountId"}, "Namespace": {"location": "uri", "locationName": "Namespace"}, "UserName": {}, "CustomPermissionsName": {}, "ExternalLoginFederationProviderType": {}, "CustomFederationProviderUrl": {}, "ExternalLoginId": {}}}, "output": {"type": "structure", "members": {"User": {"shape": "Sv9"}, "UserInvitationUrl": {}, "RequestId": {}, "Status": {"location": "statusCode", "type": "integer"}}}}, "RestoreAnalysis": {"http": {"requestUri": "/accounts/{AwsAccountId}/restore/analyses/{AnalysisId}"}, "input": {"type": "structure", "required": ["AwsAccountId", "AnalysisId"], "members": {"AwsAccountId": {"location": "uri", "locationName": "AwsAccountId"}, "AnalysisId": {"location": "uri", "locationName": "AnalysisId"}}}, "output": {"type": "structure", "members": {"Status": {"location": "statusCode", "type": "integer"}, "Arn": {}, "AnalysisId": {}, "RequestId": {}}}}, "SearchAnalyses": {"http": {"requestUri": "/accounts/{AwsAccountId}/search/analyses"}, "input": {"type": "structure", "required": ["AwsAccountId", "Filters"], "members": {"AwsAccountId": {"location": "uri", "locationName": "AwsAccountId"}, "Filters": {"type": "list", "member": {"type": "structure", "members": {"Operator": {}, "Name": {}, "Value": {}}}}, "NextToken": {}, "MaxResults": {"type": "integer"}}}, "output": {"type": "structure", "members": {"AnalysisSummaryList": {"shape": "Sw8"}, "NextToken": {}, "Status": {"location": "statusCode", "type": "integer"}, "RequestId": {}}}}, "SearchDashboards": {"http": {"requestUri": "/accounts/{AwsAccountId}/search/dashboards"}, "input": {"type": "structure", "required": ["AwsAccountId", "Filters"], "members": {"AwsAccountId": {"location": "uri", "locationName": "AwsAccountId"}, "Filters": {"type": "list", "member": {"type": "structure", "required": ["Operator"], "members": {"Operator": {}, "Name": {}, "Value": {}}}}, "NextToken": {}, "MaxResults": {"type": "integer"}}}, "output": {"type": "structure", "members": {"DashboardSummaryList": {"shape": "Swg"}, "NextToken": {}, "Status": {"location": "statusCode", "type": "integer"}, "RequestId": {}}}}, "SearchDataSets": {"http": {"requestUri": "/accounts/{AwsAccountId}/search/data-sets"}, "input": {"type": "structure", "required": ["AwsAccountId", "Filters"], "members": {"AwsAccountId": {"location": "uri", "locationName": "AwsAccountId"}, "Filters": {"type": "list", "member": {"type": "structure", "required": ["Operator", "Name", "Value"], "members": {"Operator": {}, "Name": {}, "Value": {}}}}, "NextToken": {}, "MaxResults": {"type": "integer"}}}, "output": {"type": "structure", "members": {"DataSetSummaries": {"shape": "Swk"}, "NextToken": {}, "Status": {"location": "statusCode", "type": "integer"}, "RequestId": {}}}}, "SearchDataSources": {"http": {"requestUri": "/accounts/{AwsAccountId}/search/data-sources"}, "input": {"type": "structure", "required": ["AwsAccountId", "Filters"], "members": {"AwsAccountId": {"location": "uri", "locationName": "AwsAccountId"}, "Filters": {"type": "list", "member": {"type": "structure", "required": ["Operator", "Name", "Value"], "members": {"Operator": {}, "Name": {}, "Value": {}}}}, "NextToken": {}, "MaxResults": {"type": "integer"}}}, "output": {"type": "structure", "members": {"DataSourceSummaries": {"type": "list", "member": {"type": "structure", "members": {"Arn": {}, "DataSourceId": {}, "Name": {}, "Type": {}, "CreatedTime": {"type": "timestamp"}, "LastUpdatedTime": {"type": "timestamp"}}}}, "NextToken": {}, "Status": {"location": "statusCode", "type": "integer"}, "RequestId": {}}}}, "SearchFolders": {"http": {"requestUri": "/accounts/{AwsAccountId}/search/folders"}, "input": {"type": "structure", "required": ["AwsAccountId", "Filters"], "members": {"AwsAccountId": {"location": "uri", "locationName": "AwsAccountId"}, "Filters": {"type": "list", "member": {"type": "structure", "members": {"Operator": {}, "Name": {}, "Value": {}}}}, "NextToken": {}, "MaxResults": {"type": "integer"}}}, "output": {"type": "structure", "members": {"Status": {"location": "statusCode", "type": "integer"}, "FolderSummaryList": {"shape": "Swv"}, "NextToken": {}, "RequestId": {}}}}, "SearchGroups": {"http": {"requestUri": "/accounts/{AwsAccountId}/namespaces/{Namespace}/groups-search"}, "input": {"type": "structure", "required": ["AwsAccountId", "Namespace", "Filters"], "members": {"AwsAccountId": {"location": "uri", "locationName": "AwsAccountId"}, "NextToken": {"location": "querystring", "locationName": "next-token"}, "MaxResults": {"location": "querystring", "locationName": "max-results", "type": "integer"}, "Namespace": {"location": "uri", "locationName": "Namespace"}, "Filters": {"type": "list", "member": {"type": "structure", "required": ["Operator", "Name", "Value"], "members": {"Operator": {}, "Name": {}, "Value": {}}}}}}, "output": {"type": "structure", "members": {"GroupList": {"shape": "Sx2"}, "NextToken": {}, "RequestId": {}, "Status": {"location": "statusCode", "type": "integer"}}}}, "TagResource": {"http": {"requestUri": "/resources/{ResourceArn}/tags"}, "input": {"type": "structure", "required": ["ResourceArn", "Tags"], "members": {"ResourceArn": {"location": "uri", "locationName": "ResourceArn"}, "Tags": {"shape": "Sb"}}}, "output": {"type": "structure", "members": {"RequestId": {}, "Status": {"location": "statusCode", "type": "integer"}}}}, "UntagResource": {"http": {"method": "DELETE", "requestUri": "/resources/{ResourceArn}/tags"}, "input": {"type": "structure", "required": ["ResourceArn", "TagKeys"], "members": {"ResourceArn": {"location": "uri", "locationName": "ResourceArn"}, "TagKeys": {"location": "querystring", "locationName": "keys", "type": "list", "member": {}}}}, "output": {"type": "structure", "members": {"RequestId": {}, "Status": {"location": "statusCode", "type": "integer"}}}}, "UpdateAccountCustomization": {"http": {"method": "PUT", "requestUri": "/accounts/{AwsAccountId}/customizations"}, "input": {"type": "structure", "required": ["AwsAccountId", "AccountCustomization"], "members": {"AwsAccountId": {"location": "uri", "locationName": "AwsAccountId"}, "Namespace": {"location": "querystring", "locationName": "namespace"}, "AccountCustomization": {"shape": "Sa"}}}, "output": {"type": "structure", "members": {"Arn": {}, "AwsAccountId": {}, "Namespace": {}, "AccountCustomization": {"shape": "Sa"}, "RequestId": {}, "Status": {"location": "statusCode", "type": "integer"}}}}, "UpdateAccountSettings": {"http": {"method": "PUT", "requestUri": "/accounts/{AwsAccountId}/settings"}, "input": {"type": "structure", "required": ["AwsAccountId", "DefaultNamespace"], "members": {"AwsAccountId": {"location": "uri", "locationName": "AwsAccountId"}, "DefaultNamespace": {}, "NotificationEmail": {}, "TerminationProtectionEnabled": {"type": "boolean"}}}, "output": {"type": "structure", "members": {"RequestId": {}, "Status": {"location": "statusCode", "type": "integer"}}}}, "UpdateAnalysis": {"http": {"method": "PUT", "requestUri": "/accounts/{AwsAccountId}/analyses/{AnalysisId}"}, "input": {"type": "structure", "required": ["AwsAccountId", "AnalysisId", "Name"], "members": {"AwsAccountId": {"location": "uri", "locationName": "AwsAccountId"}, "AnalysisId": {"location": "uri", "locationName": "AnalysisId"}, "Name": {}, "Parameters": {"shape": "Sq"}, "SourceEntity": {"shape": "S1c"}, "ThemeArn": {}, "Definition": {"shape": "S1g"}}}, "output": {"type": "structure", "members": {"Arn": {}, "AnalysisId": {}, "UpdateStatus": {}, "Status": {"location": "statusCode", "type": "integer"}, "RequestId": {}}}}, "UpdateAnalysisPermissions": {"http": {"method": "PUT", "requestUri": "/accounts/{AwsAccountId}/analyses/{AnalysisId}/permissions"}, "input": {"type": "structure", "required": ["AwsAccountId", "AnalysisId"], "members": {"AwsAccountId": {"location": "uri", "locationName": "AwsAccountId"}, "AnalysisId": {"location": "uri", "locationName": "AnalysisId"}, "GrantPermissions": {"shape": "Szq"}, "RevokePermissions": {"shape": "Szq"}}}, "output": {"type": "structure", "members": {"AnalysisArn": {}, "AnalysisId": {}, "Permissions": {"shape": "S18"}, "RequestId": {}, "Status": {"location": "statusCode", "type": "integer"}}}}, "UpdateDashboard": {"http": {"method": "PUT", "requestUri": "/accounts/{AwsAccountId}/dashboards/{DashboardId}"}, "input": {"type": "structure", "required": ["AwsAccountId", "DashboardId", "Name"], "members": {"AwsAccountId": {"location": "uri", "locationName": "AwsAccountId"}, "DashboardId": {"location": "uri", "locationName": "DashboardId"}, "Name": {}, "SourceEntity": {"shape": "Skq"}, "Parameters": {"shape": "Sq"}, "VersionDescription": {}, "DashboardPublishOptions": {"shape": "Skt"}, "ThemeArn": {}, "Definition": {"shape": "Sl8"}}}, "output": {"type": "structure", "members": {"Arn": {}, "VersionArn": {}, "DashboardId": {}, "CreationStatus": {}, "Status": {"type": "integer"}, "RequestId": {}}}}, "UpdateDashboardPermissions": {"http": {"method": "PUT", "requestUri": "/accounts/{AwsAccountId}/dashboards/{DashboardId}/permissions"}, "input": {"type": "structure", "required": ["AwsAccountId", "DashboardId"], "members": {"AwsAccountId": {"location": "uri", "locationName": "AwsAccountId"}, "DashboardId": {"location": "uri", "locationName": "DashboardId"}, "GrantPermissions": {"shape": "Szq"}, "RevokePermissions": {"shape": "Szq"}, "GrantLinkPermissions": {"shape": "Szv"}, "RevokeLinkPermissions": {"shape": "Szv"}}}, "output": {"type": "structure", "members": {"DashboardArn": {}, "DashboardId": {}, "Permissions": {"shape": "S18"}, "RequestId": {}, "Status": {"location": "statusCode", "type": "integer"}, "LinkSharingConfiguration": {"shape": "St3"}}}}, "UpdateDashboardPublishedVersion": {"http": {"method": "PUT", "requestUri": "/accounts/{AwsAccountId}/dashboards/{DashboardId}/versions/{VersionNumber}"}, "input": {"type": "structure", "required": ["AwsAccountId", "DashboardId", "VersionNumber"], "members": {"AwsAccountId": {"location": "uri", "locationName": "AwsAccountId"}, "DashboardId": {"location": "uri", "locationName": "DashboardId"}, "VersionNumber": {"location": "uri", "locationName": "VersionNumber", "type": "long"}}}, "output": {"type": "structure", "members": {"DashboardId": {}, "DashboardArn": {}, "Status": {"location": "statusCode", "type": "integer"}, "RequestId": {}}}}, "UpdateDataSet": {"http": {"method": "PUT", "requestUri": "/accounts/{AwsAccountId}/data-sets/{DataSetId}"}, "input": {"type": "structure", "required": ["AwsAccountId", "DataSetId", "Name", "PhysicalTableMap", "ImportMode"], "members": {"AwsAccountId": {"location": "uri", "locationName": "AwsAccountId"}, "DataSetId": {"location": "uri", "locationName": "DataSetId"}, "Name": {}, "PhysicalTableMap": {"shape": "Sld"}, "LogicalTableMap": {"shape": "Slw"}, "ImportMode": {}, "ColumnGroups": {"shape": "Sms"}, "FieldFolders": {"shape": "Smy"}, "RowLevelPermissionDataSet": {"shape": "Sn3"}, "RowLevelPermissionTagConfiguration": {"shape": "Sn7"}, "ColumnLevelPermissionRules": {"shape": "Snd"}, "DataSetUsageConfiguration": {"shape": "Snh"}}}, "output": {"type": "structure", "members": {"Arn": {}, "DataSetId": {}, "IngestionArn": {}, "IngestionId": {}, "RequestId": {}, "Status": {"location": "statusCode", "type": "integer"}}}}, "UpdateDataSetPermissions": {"http": {"requestUri": "/accounts/{AwsAccountId}/data-sets/{DataSetId}/permissions"}, "input": {"type": "structure", "required": ["AwsAccountId", "DataSetId"], "members": {"AwsAccountId": {"location": "uri", "locationName": "AwsAccountId"}, "DataSetId": {"location": "uri", "locationName": "DataSetId"}, "GrantPermissions": {"shape": "S18"}, "RevokePermissions": {"shape": "S18"}}}, "output": {"type": "structure", "members": {"DataSetArn": {}, "DataSetId": {}, "RequestId": {}, "Status": {"location": "statusCode", "type": "integer"}}}}, "UpdateDataSource": {"http": {"method": "PUT", "requestUri": "/accounts/{AwsAccountId}/data-sources/{DataSourceId}"}, "input": {"type": "structure", "required": ["AwsAccountId", "DataSourceId", "Name"], "members": {"AwsAccountId": {"location": "uri", "locationName": "AwsAccountId"}, "DataSourceId": {"location": "uri", "locationName": "DataSourceId"}, "Name": {}, "DataSourceParameters": {"shape": "Snl"}, "Credentials": {"shape": "Sor"}, "VpcConnectionProperties": {"shape": "Soy"}, "SslProperties": {"shape": "Soz"}}}, "output": {"type": "structure", "members": {"Arn": {}, "DataSourceId": {}, "UpdateStatus": {}, "RequestId": {}, "Status": {"location": "statusCode", "type": "integer"}}}}, "UpdateDataSourcePermissions": {"http": {"requestUri": "/accounts/{AwsAccountId}/data-sources/{DataSourceId}/permissions"}, "input": {"type": "structure", "required": ["AwsAccountId", "DataSourceId"], "members": {"AwsAccountId": {"location": "uri", "locationName": "AwsAccountId"}, "DataSourceId": {"location": "uri", "locationName": "DataSourceId"}, "GrantPermissions": {"shape": "S18"}, "RevokePermissions": {"shape": "S18"}}}, "output": {"type": "structure", "members": {"DataSourceArn": {}, "DataSourceId": {}, "RequestId": {}, "Status": {"location": "statusCode", "type": "integer"}}}}, "UpdateFolder": {"http": {"method": "PUT", "requestUri": "/accounts/{AwsAccountId}/folders/{FolderId}"}, "input": {"type": "structure", "required": ["AwsAccountId", "FolderId", "Name"], "members": {"AwsAccountId": {"location": "uri", "locationName": "AwsAccountId"}, "FolderId": {"location": "uri", "locationName": "FolderId"}, "Name": {}}}, "output": {"type": "structure", "members": {"Status": {"location": "statusCode", "type": "integer"}, "Arn": {}, "FolderId": {}, "RequestId": {}}}}, "UpdateFolderPermissions": {"http": {"method": "PUT", "requestUri": "/accounts/{AwsAccountId}/folders/{FolderId}/permissions"}, "input": {"type": "structure", "required": ["AwsAccountId", "FolderId"], "members": {"AwsAccountId": {"location": "uri", "locationName": "AwsAccountId"}, "FolderId": {"location": "uri", "locationName": "FolderId"}, "GrantPermissions": {"shape": "S18"}, "RevokePermissions": {"shape": "S18"}}}, "output": {"type": "structure", "members": {"Status": {"type": "integer"}, "Arn": {}, "FolderId": {}, "Permissions": {"shape": "S18"}, "RequestId": {}}}}, "UpdateGroup": {"http": {"method": "PUT", "requestUri": "/accounts/{AwsAccountId}/namespaces/{Namespace}/groups/{GroupName}"}, "input": {"type": "structure", "required": ["GroupName", "AwsAccountId", "Namespace"], "members": {"GroupName": {"location": "uri", "locationName": "GroupName"}, "Description": {}, "AwsAccountId": {"location": "uri", "locationName": "AwsAccountId"}, "Namespace": {"location": "uri", "locationName": "Namespace"}}}, "output": {"type": "structure", "members": {"Group": {"shape": "Spe"}, "RequestId": {}, "Status": {"location": "statusCode", "type": "integer"}}}}, "UpdateIAMPolicyAssignment": {"http": {"method": "PUT", "requestUri": "/accounts/{AwsAccountId}/namespaces/{Namespace}/iam-policy-assignments/{AssignmentName}"}, "input": {"type": "structure", "required": ["AwsAccountId", "AssignmentName", "Namespace"], "members": {"AwsAccountId": {"location": "uri", "locationName": "AwsAccountId"}, "AssignmentName": {"location": "uri", "locationName": "AssignmentName"}, "Namespace": {"location": "uri", "locationName": "Namespace"}, "AssignmentStatus": {}, "PolicyArn": {}, "Identities": {"shape": "Spm"}}}, "output": {"type": "structure", "members": {"AssignmentName": {}, "AssignmentId": {}, "PolicyArn": {}, "Identities": {"shape": "Spm"}, "AssignmentStatus": {}, "RequestId": {}, "Status": {"location": "statusCode", "type": "integer"}}}}, "UpdateIpRestriction": {"http": {"requestUri": "/accounts/{AwsAccountId}/ip-restriction"}, "input": {"type": "structure", "required": ["AwsAccountId"], "members": {"AwsAccountId": {"location": "uri", "locationName": "AwsAccountId"}, "IpRestrictionRuleMap": {"shape": "Su8"}, "Enabled": {"type": "boolean"}}}, "output": {"type": "structure", "members": {"AwsAccountId": {}, "RequestId": {}, "Status": {"location": "statusCode", "type": "integer"}}}}, "UpdatePublicSharingSettings": {"http": {"method": "PUT", "requestUri": "/accounts/{AwsAccountId}/public-sharing-settings"}, "input": {"type": "structure", "required": ["AwsAccountId"], "members": {"AwsAccountId": {"location": "uri", "locationName": "AwsAccountId"}, "PublicSharingEnabled": {"type": "boolean"}}}, "output": {"type": "structure", "members": {"RequestId": {}, "Status": {"location": "statusCode", "type": "integer"}}}}, "UpdateTemplate": {"http": {"method": "PUT", "requestUri": "/accounts/{AwsAccountId}/templates/{TemplateId}"}, "input": {"type": "structure", "required": ["AwsAccountId", "TemplateId"], "members": {"AwsAccountId": {"location": "uri", "locationName": "AwsAccountId"}, "TemplateId": {"location": "uri", "locationName": "TemplateId"}, "SourceEntity": {"shape": "Sq0"}, "VersionDescription": {}, "Name": {}, "Definition": {"shape": "Sq3"}}}, "output": {"type": "structure", "members": {"TemplateId": {}, "Arn": {}, "VersionArn": {}, "CreationStatus": {}, "Status": {"location": "statusCode", "type": "integer"}, "RequestId": {}}}}, "UpdateTemplateAlias": {"http": {"method": "PUT", "requestUri": "/accounts/{AwsAccountId}/templates/{TemplateId}/aliases/{AliasName}"}, "input": {"type": "structure", "required": ["AwsAccountId", "TemplateId", "AliasName", "TemplateVersionNumber"], "members": {"AwsAccountId": {"location": "uri", "locationName": "AwsAccountId"}, "TemplateId": {"location": "uri", "locationName": "TemplateId"}, "AliasName": {"location": "uri", "locationName": "AliasName"}, "TemplateVersionNumber": {"type": "long"}}}, "output": {"type": "structure", "members": {"TemplateAlias": {"shape": "Sqi"}, "Status": {"location": "statusCode", "type": "integer"}, "RequestId": {}}}}, "UpdateTemplatePermissions": {"http": {"method": "PUT", "requestUri": "/accounts/{AwsAccountId}/templates/{TemplateId}/permissions"}, "input": {"type": "structure", "required": ["AwsAccountId", "TemplateId"], "members": {"AwsAccountId": {"location": "uri", "locationName": "AwsAccountId"}, "TemplateId": {"location": "uri", "locationName": "TemplateId"}, "GrantPermissions": {"shape": "Szq"}, "RevokePermissions": {"shape": "Szq"}}}, "output": {"type": "structure", "members": {"TemplateId": {}, "TemplateArn": {}, "Permissions": {"shape": "S18"}, "RequestId": {}, "Status": {"location": "statusCode", "type": "integer"}}}}, "UpdateTheme": {"http": {"method": "PUT", "requestUri": "/accounts/{AwsAccountId}/themes/{ThemeId}"}, "input": {"type": "structure", "required": ["AwsAccountId", "ThemeId", "BaseThemeId"], "members": {"AwsAccountId": {"location": "uri", "locationName": "AwsAccountId"}, "ThemeId": {"location": "uri", "locationName": "ThemeId"}, "Name": {}, "BaseThemeId": {}, "VersionDescription": {}, "Configuration": {"shape": "Sql"}}}, "output": {"type": "structure", "members": {"ThemeId": {}, "Arn": {}, "VersionArn": {}, "CreationStatus": {}, "Status": {"location": "statusCode", "type": "integer"}, "RequestId": {}}}}, "UpdateThemeAlias": {"http": {"method": "PUT", "requestUri": "/accounts/{AwsAccountId}/themes/{ThemeId}/aliases/{AliasName}"}, "input": {"type": "structure", "required": ["AwsAccountId", "ThemeId", "AliasName", "ThemeVersionNumber"], "members": {"AwsAccountId": {"location": "uri", "locationName": "AwsAccountId"}, "ThemeId": {"location": "uri", "locationName": "ThemeId"}, "AliasName": {"location": "uri", "locationName": "AliasName"}, "ThemeVersionNumber": {"type": "long"}}}, "output": {"type": "structure", "members": {"ThemeAlias": {"shape": "Sr1"}, "Status": {"location": "statusCode", "type": "integer"}, "RequestId": {}}}}, "UpdateThemePermissions": {"http": {"method": "PUT", "requestUri": "/accounts/{AwsAccountId}/themes/{ThemeId}/permissions"}, "input": {"type": "structure", "required": ["AwsAccountId", "ThemeId"], "members": {"AwsAccountId": {"location": "uri", "locationName": "AwsAccountId"}, "ThemeId": {"location": "uri", "locationName": "ThemeId"}, "GrantPermissions": {"shape": "Szq"}, "RevokePermissions": {"shape": "Szq"}}}, "output": {"type": "structure", "members": {"ThemeId": {}, "ThemeArn": {}, "Permissions": {"shape": "S18"}, "RequestId": {}, "Status": {"location": "statusCode", "type": "integer"}}}}, "UpdateUser": {"http": {"method": "PUT", "requestUri": "/accounts/{AwsAccountId}/namespaces/{Namespace}/users/{UserName}"}, "input": {"type": "structure", "required": ["UserName", "AwsAccountId", "Namespace", "Email", "Role"], "members": {"UserName": {"location": "uri", "locationName": "UserName"}, "AwsAccountId": {"location": "uri", "locationName": "AwsAccountId"}, "Namespace": {"location": "uri", "locationName": "Namespace"}, "Email": {}, "Role": {}, "CustomPermissionsName": {}, "UnapplyCustomPermissions": {"type": "boolean"}, "ExternalLoginFederationProviderType": {}, "CustomFederationProviderUrl": {}, "ExternalLoginId": {}}}, "output": {"type": "structure", "members": {"User": {"shape": "Sv9"}, "RequestId": {}, "Status": {"location": "statusCode", "type": "integer"}}}}}, "shapes": {"Sa": {"type": "structure", "members": {"DefaultTheme": {}, "DefaultEmailCustomizationTemplate": {}}}, "Sb": {"type": "list", "member": {"type": "structure", "required": ["Key", "Value"], "members": {"Key": {}, "Value": {}}}}, "Sj": {"type": "list", "member": {}}, "Sq": {"type": "structure", "members": {"StringParameters": {"type": "list", "member": {"type": "structure", "required": ["Name", "Values"], "members": {"Name": {}, "Values": {"type": "list", "member": {"shape": "Sv"}}}}}, "IntegerParameters": {"type": "list", "member": {"type": "structure", "required": ["Name", "Values"], "members": {"Name": {}, "Values": {"type": "list", "member": {"shape": "Sz"}}}}}, "DecimalParameters": {"type": "list", "member": {"type": "structure", "required": ["Name", "Values"], "members": {"Name": {}, "Values": {"type": "list", "member": {"shape": "S13"}}}}}, "DateTimeParameters": {"type": "list", "member": {"type": "structure", "required": ["Name", "Values"], "members": {"Name": {}, "Values": {"type": "list", "member": {"shape": "S17"}}}}}}}, "Sv": {"type": "string", "sensitive": true}, "Sz": {"type": "long", "sensitive": true}, "S13": {"type": "double", "sensitive": true}, "S17": {"type": "timestamp", "sensitive": true}, "S18": {"type": "list", "member": {"shape": "S19"}}, "S19": {"type": "structure", "required": ["Principal", "Actions"], "members": {"Principal": {}, "Actions": {"type": "list", "member": {}}}}, "S1c": {"type": "structure", "members": {"SourceTemplate": {"type": "structure", "required": ["DataSetReferences", "<PERSON><PERSON>"], "members": {"DataSetReferences": {"shape": "S1e"}, "Arn": {}}}}}, "S1e": {"type": "list", "member": {"type": "structure", "required": ["DataSetPlaceholder", "DataSetArn"], "members": {"DataSetPlaceholder": {}, "DataSetArn": {}}}}, "S1g": {"type": "structure", "required": ["DataSetIdentifierDeclarations"], "members": {"DataSetIdentifierDeclarations": {"shape": "S1h"}, "Sheets": {"shape": "S1k"}, "CalculatedFields": {"shape": "Sit"}, "ParameterDeclarations": {"shape": "Siv"}, "FilterGroups": {"shape": "Sjd"}, "ColumnConfigurations": {"shape": "Skc"}, "AnalysisDefaults": {"shape": "Skf"}}}, "S1h": {"type": "list", "member": {"type": "structure", "required": ["Identifier", "DataSetArn"], "members": {"Identifier": {}, "DataSetArn": {}}}}, "S1k": {"type": "list", "member": {"type": "structure", "required": ["SheetId"], "members": {"SheetId": {}, "Title": {}, "Description": {}, "Name": {}, "ParameterControls": {"type": "list", "member": {"type": "structure", "members": {"DateTimePicker": {"type": "structure", "required": ["ParameterControlId", "Title", "SourceParameterName"], "members": {"ParameterControlId": {}, "Title": {}, "SourceParameterName": {}, "DisplayOptions": {"shape": "S1u"}}}, "List": {"type": "structure", "required": ["ParameterControlId", "Title", "SourceParameterName"], "members": {"ParameterControlId": {}, "Title": {}, "SourceParameterName": {}, "DisplayOptions": {"shape": "S27"}, "Type": {}, "SelectableValues": {"shape": "S2b"}, "CascadingControlConfiguration": {"shape": "S2f"}}}, "Dropdown": {"type": "structure", "required": ["ParameterControlId", "Title", "SourceParameterName"], "members": {"ParameterControlId": {}, "Title": {}, "SourceParameterName": {}, "DisplayOptions": {"shape": "S2j"}, "Type": {}, "SelectableValues": {"shape": "S2b"}, "CascadingControlConfiguration": {"shape": "S2f"}}}, "TextField": {"type": "structure", "required": ["ParameterControlId", "Title", "SourceParameterName"], "members": {"ParameterControlId": {}, "Title": {}, "SourceParameterName": {}, "DisplayOptions": {"shape": "S2l"}}}, "TextArea": {"type": "structure", "required": ["ParameterControlId", "Title", "SourceParameterName"], "members": {"ParameterControlId": {}, "Title": {}, "SourceParameterName": {}, "Delimiter": {}, "DisplayOptions": {"shape": "S2p"}}}, "Slider": {"type": "structure", "required": ["ParameterControlId", "Title", "SourceParameterName", "MaximumValue", "MinimumValue", "StepSize"], "members": {"ParameterControlId": {}, "Title": {}, "SourceParameterName": {}, "DisplayOptions": {"shape": "S2r"}, "MaximumValue": {"type": "double"}, "MinimumValue": {"type": "double"}, "StepSize": {"type": "double"}}}}}}, "FilterControls": {"type": "list", "member": {"type": "structure", "members": {"DateTimePicker": {"type": "structure", "required": ["FilterControlId", "Title", "SourceFilterId"], "members": {"FilterControlId": {}, "Title": {}, "SourceFilterId": {}, "DisplayOptions": {"shape": "S1u"}, "Type": {}}}, "List": {"type": "structure", "required": ["FilterControlId", "Title", "SourceFilterId"], "members": {"FilterControlId": {}, "Title": {}, "SourceFilterId": {}, "DisplayOptions": {"shape": "S27"}, "Type": {}, "SelectableValues": {"shape": "S2y"}, "CascadingControlConfiguration": {"shape": "S2f"}}}, "Dropdown": {"type": "structure", "required": ["FilterControlId", "Title", "SourceFilterId"], "members": {"FilterControlId": {}, "Title": {}, "SourceFilterId": {}, "DisplayOptions": {"shape": "S2j"}, "Type": {}, "SelectableValues": {"shape": "S2y"}, "CascadingControlConfiguration": {"shape": "S2f"}}}, "TextField": {"type": "structure", "required": ["FilterControlId", "Title", "SourceFilterId"], "members": {"FilterControlId": {}, "Title": {}, "SourceFilterId": {}, "DisplayOptions": {"shape": "S2l"}}}, "TextArea": {"type": "structure", "required": ["FilterControlId", "Title", "SourceFilterId"], "members": {"FilterControlId": {}, "Title": {}, "SourceFilterId": {}, "Delimiter": {}, "DisplayOptions": {"shape": "S2p"}}}, "Slider": {"type": "structure", "required": ["FilterControlId", "Title", "SourceFilterId", "MaximumValue", "MinimumValue", "StepSize"], "members": {"FilterControlId": {}, "Title": {}, "SourceFilterId": {}, "DisplayOptions": {"shape": "S2r"}, "Type": {}, "MaximumValue": {"type": "double"}, "MinimumValue": {"type": "double"}, "StepSize": {"type": "double"}}}, "RelativeDateTime": {"type": "structure", "required": ["FilterControlId", "Title", "SourceFilterId"], "members": {"FilterControlId": {}, "Title": {}, "SourceFilterId": {}, "DisplayOptions": {"type": "structure", "members": {"TitleOptions": {"shape": "S1v"}, "DateTimeFormat": {}}}}}}}}, "Visuals": {"type": "list", "member": {"type": "structure", "members": {"TableVisual": {"type": "structure", "required": ["VisualId"], "members": {"VisualId": {}, "Title": {"shape": "S39"}, "Subtitle": {"shape": "S3d"}, "ChartConfiguration": {"type": "structure", "members": {"FieldWells": {"type": "structure", "members": {"TableAggregatedFieldWells": {"type": "structure", "members": {"GroupBy": {"shape": "S3k"}, "Values": {"shape": "S4c"}}}, "TableUnaggregatedFieldWells": {"type": "structure", "members": {"Values": {"type": "list", "member": {"type": "structure", "required": ["FieldId", "Column"], "members": {"FieldId": {}, "Column": {"shape": "S2d"}, "FormatConfiguration": {"shape": "S4s"}}}}}}}}, "SortConfiguration": {"type": "structure", "members": {"RowSort": {"type": "list", "member": {"shape": "S4v"}}, "PaginationConfiguration": {"shape": "S50"}}}, "TableOptions": {"type": "structure", "members": {"Orientation": {}, "HeaderStyle": {"shape": "S55"}, "CellStyle": {"shape": "S55"}, "RowAlternateColorOptions": {"shape": "S5f"}}}, "TotalOptions": {"type": "structure", "members": {"TotalsVisibility": {}, "Placement": {}, "ScrollStatus": {}, "CustomLabel": {}, "TotalCellStyle": {"shape": "S55"}}}, "FieldOptions": {"type": "structure", "members": {"SelectedFieldOptions": {"type": "list", "member": {"type": "structure", "required": ["FieldId"], "members": {"FieldId": {}, "Width": {}, "CustomLabel": {}, "Visibility": {}, "URLStyling": {"type": "structure", "members": {"LinkConfiguration": {"type": "structure", "required": ["Target", "Content"], "members": {"Target": {}, "Content": {"type": "structure", "members": {"CustomTextContent": {"type": "structure", "required": ["FontConfiguration"], "members": {"Value": {}, "FontConfiguration": {"shape": "S1x"}}}, "CustomIconContent": {"type": "structure", "members": {"Icon": {}}}}}}}, "ImageConfiguration": {"type": "structure", "members": {"SizingOptions": {"type": "structure", "members": {"TableCellImageScalingConfiguration": {}}}}}}}}}}, "Order": {"type": "list", "member": {}}}}, "PaginatedReportOptions": {"type": "structure", "members": {"VerticalOverflowVisibility": {}, "OverflowColumnHeaderVisibility": {}}}, "TableInlineVisualizations": {"type": "list", "member": {"type": "structure", "members": {"DataBars": {"type": "structure", "required": ["FieldId"], "members": {"FieldId": {}, "PositiveColor": {}, "NegativeColor": {}}}}}}}}, "ConditionalFormatting": {"type": "structure", "members": {"ConditionalFormattingOptions": {"type": "list", "member": {"type": "structure", "members": {"Cell": {"type": "structure", "required": ["FieldId"], "members": {"FieldId": {}, "TextFormat": {"shape": "S69"}}}, "Row": {"type": "structure", "members": {"BackgroundColor": {"shape": "S6a"}, "TextColor": {"shape": "S6a"}}}}}}}}, "Actions": {"shape": "S6q"}}}, "PivotTableVisual": {"type": "structure", "required": ["VisualId"], "members": {"VisualId": {}, "Title": {"shape": "S39"}, "Subtitle": {"shape": "S3d"}, "ChartConfiguration": {"type": "structure", "members": {"FieldWells": {"type": "structure", "members": {"PivotTableAggregatedFieldWells": {"type": "structure", "members": {"Rows": {"shape": "S7r"}, "Columns": {"shape": "S7r"}, "Values": {"type": "list", "member": {"shape": "S4d"}}}}}}, "SortConfiguration": {"type": "structure", "members": {"FieldSortOptions": {"type": "list", "member": {"type": "structure", "required": ["FieldId", "SortBy"], "members": {"FieldId": {}, "SortBy": {"type": "structure", "members": {"Field": {"shape": "S4w"}, "Column": {"shape": "S4y"}, "DataPath": {"type": "structure", "required": ["Direction", "SortPaths"], "members": {"Direction": {}, "SortPaths": {"shape": "S7y"}}}}}}}}}}, "TableOptions": {"type": "structure", "members": {"MetricPlacement": {}, "SingleMetricVisibility": {}, "ColumnNamesVisibility": {}, "ToggleButtonsVisibility": {}, "ColumnHeaderStyle": {"shape": "S55"}, "RowHeaderStyle": {"shape": "S55"}, "CellStyle": {"shape": "S55"}, "RowFieldNamesStyle": {"shape": "S55"}, "RowAlternateColorOptions": {"shape": "S5f"}}}, "TotalOptions": {"type": "structure", "members": {"RowSubtotalOptions": {"shape": "S84"}, "ColumnSubtotalOptions": {"shape": "S84"}, "RowTotalOptions": {"shape": "S88"}, "ColumnTotalOptions": {"shape": "S88"}}}, "FieldOptions": {"type": "structure", "members": {"SelectedFieldOptions": {"type": "list", "member": {"type": "structure", "required": ["FieldId"], "members": {"FieldId": {}, "CustomLabel": {}, "Visibility": {}}}}, "DataPathOptions": {"type": "list", "member": {"type": "structure", "required": ["DataPathList"], "members": {"DataPathList": {"shape": "S7y"}, "Width": {}}}}}}, "PaginatedReportOptions": {"type": "structure", "members": {"VerticalOverflowVisibility": {}, "OverflowColumnHeaderVisibility": {}}}}}, "ConditionalFormatting": {"type": "structure", "members": {"ConditionalFormattingOptions": {"type": "list", "member": {"type": "structure", "members": {"Cell": {"type": "structure", "required": ["FieldId"], "members": {"FieldId": {}, "TextFormat": {"shape": "S69"}, "Scope": {"type": "structure", "members": {"Role": {}}}}}}}}}}, "Actions": {"shape": "S6q"}}}, "BarChartVisual": {"type": "structure", "required": ["VisualId"], "members": {"VisualId": {}, "Title": {"shape": "S39"}, "Subtitle": {"shape": "S3d"}, "ChartConfiguration": {"type": "structure", "members": {"FieldWells": {"type": "structure", "members": {"BarChartAggregatedFieldWells": {"type": "structure", "members": {"Category": {"shape": "S3k"}, "Values": {"shape": "S4c"}, "Colors": {"shape": "S3k"}, "SmallMultiples": {"shape": "S8p"}}}}}, "SortConfiguration": {"type": "structure", "members": {"CategorySort": {"shape": "S8r"}, "CategoryItemsLimit": {"shape": "S8s"}, "ColorSort": {"shape": "S8r"}, "ColorItemsLimit": {"shape": "S8s"}, "SmallMultiplesSort": {"shape": "S8r"}, "SmallMultiplesLimitConfiguration": {"shape": "S8s"}}}, "Orientation": {}, "BarsArrangement": {}, "VisualPalette": {"shape": "S8w"}, "SmallMultiplesOptions": {"shape": "S8z"}, "CategoryAxis": {"shape": "S96"}, "CategoryLabelOptions": {"shape": "S9m"}, "ValueAxis": {"shape": "S96"}, "ValueLabelOptions": {"shape": "S9m"}, "ColorLabelOptions": {"shape": "S9m"}, "Legend": {"shape": "S9q"}, "DataLabels": {"shape": "S9s"}, "Tooltip": {"shape": "Sa3"}, "ReferenceLines": {"shape": "<PERSON>b"}, "ContributionAnalysisDefaults": {"shape": "<PERSON>p"}}}, "Actions": {"shape": "S6q"}, "ColumnHierarchies": {"shape": "Sas"}}}, "KPIVisual": {"type": "structure", "required": ["VisualId"], "members": {"VisualId": {}, "Title": {"shape": "S39"}, "Subtitle": {"shape": "S3d"}, "ChartConfiguration": {"type": "structure", "members": {"FieldWells": {"type": "structure", "members": {"Values": {"shape": "S4c"}, "TargetValues": {"shape": "S4c"}, "TrendGroups": {"shape": "S3k"}}}, "SortConfiguration": {"type": "structure", "members": {"TrendGroupSort": {"shape": "S8r"}}}, "KPIOptions": {"type": "structure", "members": {"ProgressBar": {"type": "structure", "members": {"Visibility": {}}}, "TrendArrows": {"type": "structure", "members": {"Visibility": {}}}, "SecondaryValue": {"type": "structure", "members": {"Visibility": {}}}, "Comparison": {"shape": "Sbf"}, "PrimaryValueDisplayType": {}, "PrimaryValueFontConfiguration": {"shape": "S1x"}, "SecondaryValueFontConfiguration": {"shape": "S1x"}}}}}, "ConditionalFormatting": {"type": "structure", "members": {"ConditionalFormattingOptions": {"type": "list", "member": {"type": "structure", "members": {"PrimaryValue": {"type": "structure", "members": {"TextColor": {"shape": "S6a"}, "Icon": {"shape": "S6g"}}}, "ProgressBar": {"type": "structure", "members": {"ForegroundColor": {"shape": "S6a"}}}}}}}}, "Actions": {"shape": "S6q"}, "ColumnHierarchies": {"shape": "Sas"}}}, "PieChartVisual": {"type": "structure", "required": ["VisualId"], "members": {"VisualId": {}, "Title": {"shape": "S39"}, "Subtitle": {"shape": "S3d"}, "ChartConfiguration": {"type": "structure", "members": {"FieldWells": {"type": "structure", "members": {"PieChartAggregatedFieldWells": {"type": "structure", "members": {"Category": {"shape": "S3k"}, "Values": {"shape": "S4c"}, "SmallMultiples": {"shape": "S8p"}}}}}, "SortConfiguration": {"type": "structure", "members": {"CategorySort": {"shape": "S8r"}, "CategoryItemsLimit": {"shape": "S8s"}, "SmallMultiplesSort": {"shape": "S8r"}, "SmallMultiplesLimitConfiguration": {"shape": "S8s"}}}, "DonutOptions": {"type": "structure", "members": {"ArcOptions": {"type": "structure", "members": {"ArcThickness": {}}}, "DonutCenterOptions": {"type": "structure", "members": {"LabelVisibility": {}}}}}, "SmallMultiplesOptions": {"shape": "S8z"}, "CategoryLabelOptions": {"shape": "S9m"}, "ValueLabelOptions": {"shape": "S9m"}, "Legend": {"shape": "S9q"}, "DataLabels": {"shape": "S9s"}, "Tooltip": {"shape": "Sa3"}, "VisualPalette": {"shape": "S8w"}, "ContributionAnalysisDefaults": {"shape": "<PERSON>p"}}}, "Actions": {"shape": "S6q"}, "ColumnHierarchies": {"shape": "Sas"}}}, "GaugeChartVisual": {"type": "structure", "required": ["VisualId"], "members": {"VisualId": {}, "Title": {"shape": "S39"}, "Subtitle": {"shape": "S3d"}, "ChartConfiguration": {"type": "structure", "members": {"FieldWells": {"type": "structure", "members": {"Values": {"shape": "S4c"}, "TargetValues": {"shape": "S4c"}}}, "GaugeChartOptions": {"type": "structure", "members": {"PrimaryValueDisplayType": {}, "Comparison": {"shape": "Sbf"}, "ArcAxis": {"type": "structure", "members": {"Range": {"type": "structure", "members": {"Min": {"type": "double"}, "Max": {"type": "double"}}}, "ReserveRange": {"type": "integer"}}}, "Arc": {"type": "structure", "members": {"ArcAngle": {"type": "double"}, "ArcThickness": {}}}, "PrimaryValueFontConfiguration": {"shape": "S1x"}}}, "DataLabels": {"shape": "S9s"}, "TooltipOptions": {"shape": "Sa3"}, "VisualPalette": {"shape": "S8w"}}}, "ConditionalFormatting": {"type": "structure", "members": {"ConditionalFormattingOptions": {"type": "list", "member": {"type": "structure", "members": {"PrimaryValue": {"type": "structure", "members": {"TextColor": {"shape": "S6a"}, "Icon": {"shape": "S6g"}}}, "Arc": {"type": "structure", "members": {"ForegroundColor": {"shape": "S6a"}}}}}}}}, "Actions": {"shape": "S6q"}}}, "LineChartVisual": {"type": "structure", "required": ["VisualId"], "members": {"VisualId": {}, "Title": {"shape": "S39"}, "Subtitle": {"shape": "S3d"}, "ChartConfiguration": {"type": "structure", "members": {"FieldWells": {"type": "structure", "members": {"LineChartAggregatedFieldWells": {"type": "structure", "members": {"Category": {"shape": "S3k"}, "Values": {"shape": "S4c"}, "Colors": {"shape": "S3k"}, "SmallMultiples": {"shape": "S8p"}}}}}, "SortConfiguration": {"type": "structure", "members": {"CategorySort": {"shape": "S8r"}, "CategoryItemsLimitConfiguration": {"shape": "S8s"}, "ColorItemsLimitConfiguration": {"shape": "S8s"}, "SmallMultiplesSort": {"shape": "S8r"}, "SmallMultiplesLimitConfiguration": {"shape": "S8s"}}}, "ForecastConfigurations": {"type": "list", "member": {"type": "structure", "members": {"ForecastProperties": {"type": "structure", "members": {"PeriodsForward": {"type": "integer"}, "PeriodsBackward": {"type": "integer"}, "UpperBoundary": {"type": "double"}, "LowerBoundary": {"type": "double"}, "PredictionInterval": {"type": "integer"}, "Seasonality": {"type": "integer"}}}, "Scenario": {"type": "structure", "members": {"WhatIfPointScenario": {"type": "structure", "required": ["Date", "Value"], "members": {"Date": {"type": "timestamp"}, "Value": {"type": "double"}}}, "WhatIfRangeScenario": {"type": "structure", "required": ["StartDate", "EndDate", "Value"], "members": {"StartDate": {"type": "timestamp"}, "EndDate": {"type": "timestamp"}, "Value": {"type": "double"}}}}}}}}, "Type": {}, "SmallMultiplesOptions": {"shape": "S8z"}, "XAxisDisplayOptions": {"shape": "S96"}, "XAxisLabelOptions": {"shape": "S9m"}, "PrimaryYAxisDisplayOptions": {"shape": "Scq"}, "PrimaryYAxisLabelOptions": {"shape": "S9m"}, "SecondaryYAxisDisplayOptions": {"shape": "Scq"}, "SecondaryYAxisLabelOptions": {"shape": "S9m"}, "DefaultSeriesSettings": {"type": "structure", "members": {"AxisBinding": {}, "LineStyleSettings": {"shape": "Scv"}, "MarkerStyleSettings": {"shape": "<PERSON><PERSON>"}}}, "Series": {"type": "list", "member": {"type": "structure", "members": {"FieldSeriesItem": {"type": "structure", "required": ["FieldId", "AxisBinding"], "members": {"FieldId": {}, "AxisBinding": {}, "Settings": {"shape": "Sd3"}}}, "DataFieldSeriesItem": {"type": "structure", "required": ["FieldId", "AxisBinding"], "members": {"FieldId": {}, "FieldValue": {"shape": "Sv"}, "AxisBinding": {}, "Settings": {"shape": "Sd3"}}}}}}, "Legend": {"shape": "S9q"}, "DataLabels": {"shape": "S9s"}, "ReferenceLines": {"shape": "<PERSON>b"}, "Tooltip": {"shape": "Sa3"}, "ContributionAnalysisDefaults": {"shape": "<PERSON>p"}, "VisualPalette": {"shape": "S8w"}}}, "Actions": {"shape": "S6q"}, "ColumnHierarchies": {"shape": "Sas"}}}, "HeatMapVisual": {"type": "structure", "required": ["VisualId"], "members": {"VisualId": {}, "Title": {"shape": "S39"}, "Subtitle": {"shape": "S3d"}, "ChartConfiguration": {"type": "structure", "members": {"FieldWells": {"type": "structure", "members": {"HeatMapAggregatedFieldWells": {"type": "structure", "members": {"Rows": {"shape": "Sd9"}, "Columns": {"shape": "Sd9"}, "Values": {"type": "list", "member": {"shape": "S4d"}}}}}}, "SortConfiguration": {"type": "structure", "members": {"HeatMapRowSort": {"shape": "S8r"}, "HeatMapColumnSort": {"shape": "S8r"}, "HeatMapRowItemsLimitConfiguration": {"shape": "S8s"}, "HeatMapColumnItemsLimitConfiguration": {"shape": "S8s"}}}, "RowLabelOptions": {"shape": "S9m"}, "ColumnLabelOptions": {"shape": "S9m"}, "ColorScale": {"shape": "Sdc"}, "Legend": {"shape": "S9q"}, "DataLabels": {"shape": "S9s"}, "Tooltip": {"shape": "Sa3"}}}, "ColumnHierarchies": {"shape": "Sas"}, "Actions": {"shape": "S6q"}}}, "TreeMapVisual": {"type": "structure", "required": ["VisualId"], "members": {"VisualId": {}, "Title": {"shape": "S39"}, "Subtitle": {"shape": "S3d"}, "ChartConfiguration": {"type": "structure", "members": {"FieldWells": {"type": "structure", "members": {"TreeMapAggregatedFieldWells": {"type": "structure", "members": {"Groups": {"type": "list", "member": {"shape": "S3l"}}, "Sizes": {"shape": "Sdl"}, "Colors": {"shape": "Sdl"}}}}}, "SortConfiguration": {"type": "structure", "members": {"TreeMapSort": {"shape": "S8r"}, "TreeMapGroupItemsLimitConfiguration": {"shape": "S8s"}}}, "GroupLabelOptions": {"shape": "S9m"}, "SizeLabelOptions": {"shape": "S9m"}, "ColorLabelOptions": {"shape": "S9m"}, "ColorScale": {"shape": "Sdc"}, "Legend": {"shape": "S9q"}, "DataLabels": {"shape": "S9s"}, "Tooltip": {"shape": "Sa3"}}}, "Actions": {"shape": "S6q"}, "ColumnHierarchies": {"shape": "Sas"}}}, "GeospatialMapVisual": {"type": "structure", "required": ["VisualId"], "members": {"VisualId": {}, "Title": {"shape": "S39"}, "Subtitle": {"shape": "S3d"}, "ChartConfiguration": {"type": "structure", "members": {"FieldWells": {"type": "structure", "members": {"GeospatialMapAggregatedFieldWells": {"type": "structure", "members": {"Geospatial": {"shape": "S3k"}, "Values": {"shape": "S4c"}, "Colors": {"shape": "S3k"}}}}}, "Legend": {"shape": "S9q"}, "Tooltip": {"shape": "Sa3"}, "WindowOptions": {"shape": "Sdr"}, "MapStyleOptions": {"shape": "Sdw"}, "PointStyleOptions": {"type": "structure", "members": {"SelectedPointStyle": {}, "ClusterMarkerConfiguration": {"type": "structure", "members": {"ClusterMarker": {"type": "structure", "members": {"SimpleClusterMarker": {"type": "structure", "members": {"Color": {}}}}}}}}}, "VisualPalette": {"shape": "S8w"}}}, "ColumnHierarchies": {"shape": "Sas"}, "Actions": {"shape": "S6q"}}}, "FilledMapVisual": {"type": "structure", "required": ["VisualId"], "members": {"VisualId": {}, "Title": {"shape": "S39"}, "Subtitle": {"shape": "S3d"}, "ChartConfiguration": {"type": "structure", "members": {"FieldWells": {"type": "structure", "members": {"FilledMapAggregatedFieldWells": {"type": "structure", "members": {"Geospatial": {"type": "list", "member": {"shape": "S3l"}}, "Values": {"type": "list", "member": {"shape": "S4d"}}}}}}, "SortConfiguration": {"type": "structure", "members": {"CategorySort": {"shape": "S8r"}}}, "Legend": {"shape": "S9q"}, "Tooltip": {"shape": "Sa3"}, "WindowOptions": {"shape": "Sdr"}, "MapStyleOptions": {"shape": "Sdw"}}}, "ConditionalFormatting": {"type": "structure", "required": ["ConditionalFormattingOptions"], "members": {"ConditionalFormattingOptions": {"type": "list", "member": {"type": "structure", "required": ["<PERSON><PERSON><PERSON>"], "members": {"Shape": {"type": "structure", "required": ["FieldId"], "members": {"FieldId": {}, "Format": {"type": "structure", "required": ["BackgroundColor"], "members": {"BackgroundColor": {"shape": "S6a"}}}}}}}}}}, "ColumnHierarchies": {"shape": "Sas"}, "Actions": {"shape": "S6q"}}}, "FunnelChartVisual": {"type": "structure", "required": ["VisualId"], "members": {"VisualId": {}, "Title": {"shape": "S39"}, "Subtitle": {"shape": "S3d"}, "ChartConfiguration": {"type": "structure", "members": {"FieldWells": {"type": "structure", "members": {"FunnelChartAggregatedFieldWells": {"type": "structure", "members": {"Category": {"type": "list", "member": {"shape": "S3l"}}, "Values": {"type": "list", "member": {"shape": "S4d"}}}}}}, "SortConfiguration": {"type": "structure", "members": {"CategorySort": {"shape": "S8r"}, "CategoryItemsLimit": {"shape": "S8s"}}}, "CategoryLabelOptions": {"shape": "S9m"}, "ValueLabelOptions": {"shape": "S9m"}, "Tooltip": {"shape": "Sa3"}, "DataLabelOptions": {"type": "structure", "members": {"Visibility": {}, "CategoryLabelVisibility": {}, "MeasureLabelVisibility": {}, "Position": {}, "LabelFontConfiguration": {"shape": "S1x"}, "LabelColor": {}, "MeasureDataLabelStyle": {}}}, "VisualPalette": {"shape": "S8w"}}}, "Actions": {"shape": "S6q"}, "ColumnHierarchies": {"shape": "Sas"}}}, "ScatterPlotVisual": {"type": "structure", "required": ["VisualId"], "members": {"VisualId": {}, "Title": {"shape": "S39"}, "Subtitle": {"shape": "S3d"}, "ChartConfiguration": {"type": "structure", "members": {"FieldWells": {"type": "structure", "members": {"ScatterPlotCategoricallyAggregatedFieldWells": {"type": "structure", "members": {"XAxis": {"shape": "S4c"}, "YAxis": {"shape": "S4c"}, "Category": {"shape": "S3k"}, "Size": {"shape": "S4c"}}}, "ScatterPlotUnaggregatedFieldWells": {"type": "structure", "members": {"XAxis": {"shape": "S3k"}, "YAxis": {"shape": "S3k"}, "Size": {"shape": "S4c"}}}}}, "XAxisLabelOptions": {"shape": "S9m"}, "XAxisDisplayOptions": {"shape": "S96"}, "YAxisLabelOptions": {"shape": "S9m"}, "YAxisDisplayOptions": {"shape": "S96"}, "Legend": {"shape": "S9q"}, "DataLabels": {"shape": "S9s"}, "Tooltip": {"shape": "Sa3"}, "VisualPalette": {"shape": "S8w"}}}, "Actions": {"shape": "S6q"}, "ColumnHierarchies": {"shape": "Sas"}}}, "ComboChartVisual": {"type": "structure", "required": ["VisualId"], "members": {"VisualId": {}, "Title": {"shape": "S39"}, "Subtitle": {"shape": "S3d"}, "ChartConfiguration": {"type": "structure", "members": {"FieldWells": {"type": "structure", "members": {"ComboChartAggregatedFieldWells": {"type": "structure", "members": {"Category": {"shape": "S3k"}, "BarValues": {"shape": "S4c"}, "Colors": {"shape": "S3k"}, "LineValues": {"shape": "S4c"}}}}}, "SortConfiguration": {"type": "structure", "members": {"CategorySort": {"shape": "S8r"}, "CategoryItemsLimit": {"shape": "S8s"}, "ColorSort": {"shape": "S8r"}, "ColorItemsLimit": {"shape": "S8s"}}}, "BarsArrangement": {}, "CategoryAxis": {"shape": "S96"}, "CategoryLabelOptions": {"shape": "S9m"}, "PrimaryYAxisDisplayOptions": {"shape": "S96"}, "PrimaryYAxisLabelOptions": {"shape": "S9m"}, "SecondaryYAxisDisplayOptions": {"shape": "S96"}, "SecondaryYAxisLabelOptions": {"shape": "S9m"}, "ColorLabelOptions": {"shape": "S9m"}, "Legend": {"shape": "S9q"}, "BarDataLabels": {"shape": "S9s"}, "LineDataLabels": {"shape": "S9s"}, "Tooltip": {"shape": "Sa3"}, "ReferenceLines": {"shape": "<PERSON>b"}, "VisualPalette": {"shape": "S8w"}}}, "Actions": {"shape": "S6q"}, "ColumnHierarchies": {"shape": "Sas"}}}, "BoxPlotVisual": {"type": "structure", "required": ["VisualId"], "members": {"VisualId": {}, "Title": {"shape": "S39"}, "Subtitle": {"shape": "S3d"}, "ChartConfiguration": {"type": "structure", "members": {"FieldWells": {"type": "structure", "members": {"BoxPlotAggregatedFieldWells": {"type": "structure", "members": {"GroupBy": {"type": "list", "member": {"shape": "S3l"}}, "Values": {"type": "list", "member": {"shape": "S4d"}}}}}}, "SortConfiguration": {"type": "structure", "members": {"CategorySort": {"shape": "S8r"}, "PaginationConfiguration": {"shape": "S50"}}}, "BoxPlotOptions": {"type": "structure", "members": {"StyleOptions": {"type": "structure", "members": {"FillStyle": {}}}, "OutlierVisibility": {}, "AllDataPointsVisibility": {}}}, "CategoryAxis": {"shape": "S96"}, "CategoryLabelOptions": {"shape": "S9m"}, "PrimaryYAxisDisplayOptions": {"shape": "S96"}, "PrimaryYAxisLabelOptions": {"shape": "S9m"}, "Legend": {"shape": "S9q"}, "Tooltip": {"shape": "Sa3"}, "ReferenceLines": {"shape": "<PERSON>b"}, "VisualPalette": {"shape": "S8w"}}}, "Actions": {"shape": "S6q"}, "ColumnHierarchies": {"shape": "Sas"}}}, "WaterfallVisual": {"type": "structure", "required": ["VisualId"], "members": {"VisualId": {}, "Title": {"shape": "S39"}, "Subtitle": {"shape": "S3d"}, "ChartConfiguration": {"type": "structure", "members": {"FieldWells": {"type": "structure", "members": {"WaterfallChartAggregatedFieldWells": {"type": "structure", "members": {"Categories": {"shape": "S3k"}, "Values": {"shape": "S4c"}, "Breakdowns": {"shape": "S3k"}}}}}, "SortConfiguration": {"type": "structure", "members": {"CategorySort": {"shape": "S8r"}, "BreakdownItemsLimit": {"shape": "S8s"}}}, "WaterfallChartOptions": {"type": "structure", "members": {"TotalBarLabel": {}}}, "CategoryAxisLabelOptions": {"shape": "S9m"}, "CategoryAxisDisplayOptions": {"shape": "S96"}, "PrimaryYAxisLabelOptions": {"shape": "S9m"}, "PrimaryYAxisDisplayOptions": {"shape": "S96"}, "Legend": {"shape": "S9q"}, "DataLabels": {"shape": "S9s"}, "VisualPalette": {"shape": "S8w"}}}, "Actions": {"shape": "S6q"}, "ColumnHierarchies": {"shape": "Sas"}}}, "HistogramVisual": {"type": "structure", "required": ["VisualId"], "members": {"VisualId": {}, "Title": {"shape": "S39"}, "Subtitle": {"shape": "S3d"}, "ChartConfiguration": {"type": "structure", "members": {"FieldWells": {"type": "structure", "members": {"HistogramAggregatedFieldWells": {"type": "structure", "members": {"Values": {"type": "list", "member": {"shape": "S4d"}}}}}}, "XAxisDisplayOptions": {"shape": "S96"}, "XAxisLabelOptions": {"shape": "S9m"}, "YAxisDisplayOptions": {"shape": "S96"}, "BinOptions": {"type": "structure", "members": {"SelectedBinType": {}, "BinCount": {"type": "structure", "members": {"Value": {"type": "integer"}}}, "BinWidth": {"type": "structure", "members": {"Value": {"type": "double"}, "BinCountLimit": {"type": "long"}}}, "StartValue": {"type": "double"}}}, "DataLabels": {"shape": "S9s"}, "Tooltip": {"shape": "Sa3"}, "VisualPalette": {"shape": "S8w"}}}, "Actions": {"shape": "S6q"}}}, "WordCloudVisual": {"type": "structure", "required": ["VisualId"], "members": {"VisualId": {}, "Title": {"shape": "S39"}, "Subtitle": {"shape": "S3d"}, "ChartConfiguration": {"type": "structure", "members": {"FieldWells": {"type": "structure", "members": {"WordCloudAggregatedFieldWells": {"type": "structure", "members": {"GroupBy": {"type": "list", "member": {"shape": "S3l"}}, "Size": {"type": "list", "member": {"shape": "S4d"}}}}}}, "SortConfiguration": {"type": "structure", "members": {"CategoryItemsLimit": {"shape": "S8s"}, "CategorySort": {"shape": "S8r"}}}, "CategoryLabelOptions": {"shape": "S9m"}, "WordCloudOptions": {"type": "structure", "members": {"WordOrientation": {}, "WordScaling": {}, "CloudLayout": {}, "WordCasing": {}, "WordPadding": {}, "MaximumStringLength": {"type": "integer"}}}}}, "Actions": {"shape": "S6q"}, "ColumnHierarchies": {"shape": "Sas"}}}, "InsightVisual": {"type": "structure", "required": ["VisualId", "DataSetIdentifier"], "members": {"VisualId": {}, "Title": {"shape": "S39"}, "Subtitle": {"shape": "S3d"}, "InsightConfiguration": {"type": "structure", "members": {"Computations": {"type": "list", "member": {"type": "structure", "members": {"TopBottomRanked": {"type": "structure", "required": ["ComputationId", "Category", "Type"], "members": {"ComputationId": {}, "Name": {}, "Category": {"shape": "S3l"}, "Value": {"shape": "S4d"}, "ResultSize": {"type": "integer"}, "Type": {}}}, "TopBottomMovers": {"type": "structure", "required": ["ComputationId", "Time", "Category", "Type"], "members": {"ComputationId": {}, "Name": {}, "Time": {"shape": "S3l"}, "Category": {"shape": "S3l"}, "Value": {"shape": "S4d"}, "MoverSize": {"type": "integer"}, "SortOrder": {}, "Type": {}}}, "TotalAggregation": {"type": "structure", "required": ["ComputationId", "Value"], "members": {"ComputationId": {}, "Name": {}, "Value": {"shape": "S4d"}}}, "MaximumMinimum": {"type": "structure", "required": ["ComputationId", "Time", "Type"], "members": {"ComputationId": {}, "Name": {}, "Time": {"shape": "S3l"}, "Value": {"shape": "S4d"}, "Type": {}}}, "MetricComparison": {"type": "structure", "required": ["ComputationId", "Time", "FromValue", "TargetValue"], "members": {"ComputationId": {}, "Name": {}, "Time": {"shape": "S3l"}, "FromValue": {"shape": "S4d"}, "TargetValue": {"shape": "S4d"}}}, "PeriodOverPeriod": {"type": "structure", "required": ["ComputationId", "Time"], "members": {"ComputationId": {}, "Name": {}, "Time": {"shape": "S3l"}, "Value": {"shape": "S4d"}}}, "PeriodToDate": {"type": "structure", "required": ["ComputationId", "Time"], "members": {"ComputationId": {}, "Name": {}, "Time": {"shape": "S3l"}, "Value": {"shape": "S4d"}, "PeriodTimeGranularity": {}}}, "GrowthRate": {"type": "structure", "required": ["ComputationId", "Time"], "members": {"ComputationId": {}, "Name": {}, "Time": {"shape": "S3l"}, "Value": {"shape": "S4d"}, "PeriodSize": {"type": "integer"}}}, "UniqueValues": {"type": "structure", "required": ["ComputationId", "Category"], "members": {"ComputationId": {}, "Name": {}, "Category": {"shape": "S3l"}}}, "Forecast": {"type": "structure", "required": ["ComputationId", "Time"], "members": {"ComputationId": {}, "Name": {}, "Time": {"shape": "S3l"}, "Value": {"shape": "S4d"}, "PeriodsForward": {"type": "integer"}, "PeriodsBackward": {"type": "integer"}, "UpperBoundary": {"type": "double"}, "LowerBoundary": {"type": "double"}, "PredictionInterval": {"type": "integer"}, "Seasonality": {}, "CustomSeasonalityValue": {"type": "integer"}}}}}}, "CustomNarrative": {"type": "structure", "required": ["Narrative"], "members": {"Narrative": {}}}}}, "Actions": {"shape": "S6q"}, "DataSetIdentifier": {}}}, "SankeyDiagramVisual": {"type": "structure", "required": ["VisualId"], "members": {"VisualId": {}, "Title": {"shape": "S39"}, "Subtitle": {"shape": "S3d"}, "ChartConfiguration": {"type": "structure", "members": {"FieldWells": {"type": "structure", "members": {"SankeyDiagramAggregatedFieldWells": {"type": "structure", "members": {"Source": {"shape": "S3k"}, "Destination": {"shape": "S3k"}, "Weight": {"shape": "S4c"}}}}}, "SortConfiguration": {"type": "structure", "members": {"WeightSort": {"shape": "S8r"}, "SourceItemsLimit": {"shape": "S8s"}, "DestinationItemsLimit": {"shape": "S8s"}}}, "DataLabels": {"shape": "S9s"}}}, "Actions": {"shape": "S6q"}}}, "CustomContentVisual": {"type": "structure", "required": ["VisualId", "DataSetIdentifier"], "members": {"VisualId": {}, "Title": {"shape": "S39"}, "Subtitle": {"shape": "S3d"}, "ChartConfiguration": {"type": "structure", "members": {"ContentUrl": {}, "ContentType": {}, "ImageScaling": {}}}, "Actions": {"shape": "S6q"}, "DataSetIdentifier": {}}}, "EmptyVisual": {"type": "structure", "required": ["VisualId", "DataSetIdentifier"], "members": {"VisualId": {}, "DataSetIdentifier": {}, "Actions": {"shape": "S6q"}}}, "RadarChartVisual": {"type": "structure", "required": ["VisualId"], "members": {"VisualId": {}, "Title": {"shape": "S39"}, "Subtitle": {"shape": "S3d"}, "ChartConfiguration": {"type": "structure", "members": {"FieldWells": {"type": "structure", "members": {"RadarChartAggregatedFieldWells": {"type": "structure", "members": {"Category": {"type": "list", "member": {"shape": "S3l"}}, "Color": {"type": "list", "member": {"shape": "S3l"}}, "Values": {"type": "list", "member": {"shape": "S4d"}}}}}}, "SortConfiguration": {"type": "structure", "members": {"CategorySort": {"shape": "S8r"}, "CategoryItemsLimit": {"shape": "S8s"}, "ColorSort": {"shape": "S8r"}, "ColorItemsLimit": {"shape": "S8s"}}}, "Shape": {}, "BaseSeriesSettings": {"type": "structure", "members": {"AreaStyleSettings": {"type": "structure", "members": {"Visibility": {}}}}}, "StartAngle": {"type": "double"}, "VisualPalette": {"shape": "S8w"}, "AlternateBandColorsVisibility": {}, "AlternateBandEvenColor": {}, "AlternateBandOddColor": {}, "CategoryAxis": {"shape": "S96"}, "CategoryLabelOptions": {"shape": "S9m"}, "ColorAxis": {"shape": "S96"}, "ColorLabelOptions": {"shape": "S9m"}, "Legend": {"shape": "S9q"}}}, "Actions": {"shape": "S6q"}, "ColumnHierarchies": {"shape": "Sas"}}}}}}, "TextBoxes": {"type": "list", "member": {"type": "structure", "required": ["SheetTextBoxId"], "members": {"SheetTextBoxId": {}, "Content": {}}}}, "Layouts": {"type": "list", "member": {"type": "structure", "required": ["Configuration"], "members": {"Configuration": {"type": "structure", "members": {"GridLayout": {"shape": "Shk"}, "FreeFormLayout": {"type": "structure", "required": ["Elements"], "members": {"Elements": {"shape": "Shw"}, "CanvasSizeOptions": {"shape": "Si5"}}}, "SectionBasedLayout": {"type": "structure", "required": ["HeaderSections", "BodySections", "FooterSections", "CanvasSizeOptions"], "members": {"HeaderSections": {"shape": "Si8"}, "BodySections": {"type": "list", "member": {"type": "structure", "required": ["SectionId", "Content"], "members": {"SectionId": {}, "Content": {"type": "structure", "members": {"Layout": {"shape": "Sia"}}}, "Style": {"shape": "Sic"}, "PageBreakConfiguration": {"type": "structure", "members": {"After": {"type": "structure", "members": {"Status": {}}}}}}}}, "FooterSections": {"shape": "Si8"}, "CanvasSizeOptions": {"shape": "Sil"}}}}}}}}, "SheetControlLayouts": {"type": "list", "member": {"type": "structure", "required": ["Configuration"], "members": {"Configuration": {"type": "structure", "members": {"GridLayout": {"shape": "Shk"}}}}}}, "ContentType": {}}}}, "S1u": {"type": "structure", "members": {"TitleOptions": {"shape": "S1v"}, "DateTimeFormat": {}}}, "S1v": {"type": "structure", "members": {"Visibility": {}, "FontConfiguration": {"shape": "S1x"}, "CustomLabel": {}}}, "S1x": {"type": "structure", "members": {"FontSize": {"type": "structure", "members": {"Relative": {}}}, "FontDecoration": {}, "FontColor": {}, "FontWeight": {"type": "structure", "members": {"Name": {}}}, "FontStyle": {}}}, "S27": {"type": "structure", "members": {"SearchOptions": {"type": "structure", "members": {"Visibility": {}}}, "SelectAllOptions": {"shape": "S29"}, "TitleOptions": {"shape": "S1v"}}}, "S29": {"type": "structure", "members": {"Visibility": {}}}, "S2b": {"type": "structure", "members": {"Values": {"shape": "S2c"}, "LinkToDataSetColumn": {"shape": "S2d"}}}, "S2c": {"type": "list", "member": {}}, "S2d": {"type": "structure", "required": ["DataSetIdentifier", "ColumnName"], "members": {"DataSetIdentifier": {}, "ColumnName": {}}}, "S2f": {"type": "structure", "members": {"SourceControls": {"type": "list", "member": {"type": "structure", "members": {"SourceSheetControlId": {}, "ColumnToMatch": {"shape": "S2d"}}}}}}, "S2j": {"type": "structure", "members": {"SelectAllOptions": {"shape": "S29"}, "TitleOptions": {"shape": "S1v"}}}, "S2l": {"type": "structure", "members": {"TitleOptions": {"shape": "S1v"}, "PlaceholderOptions": {"shape": "S2m"}}}, "S2m": {"type": "structure", "members": {"Visibility": {}}}, "S2p": {"type": "structure", "members": {"TitleOptions": {"shape": "S1v"}, "PlaceholderOptions": {"shape": "S2m"}}}, "S2r": {"type": "structure", "members": {"TitleOptions": {"shape": "S1v"}}}, "S2y": {"type": "structure", "members": {"Values": {"shape": "S2c"}}}, "S39": {"type": "structure", "members": {"Visibility": {}, "FormatText": {"type": "structure", "members": {"PlainText": {}, "RichText": {}}}}}, "S3d": {"type": "structure", "members": {"Visibility": {}, "FormatText": {"type": "structure", "members": {"PlainText": {}, "RichText": {}}}}}, "S3k": {"type": "list", "member": {"shape": "S3l"}}, "S3l": {"type": "structure", "members": {"NumericalDimensionField": {"type": "structure", "required": ["FieldId", "Column"], "members": {"FieldId": {}, "Column": {"shape": "S2d"}, "HierarchyId": {}, "FormatConfiguration": {"shape": "S3p"}}}, "CategoricalDimensionField": {"type": "structure", "required": ["FieldId", "Column"], "members": {"FieldId": {}, "Column": {"shape": "S2d"}, "HierarchyId": {}, "FormatConfiguration": {"shape": "S48"}}}, "DateDimensionField": {"type": "structure", "required": ["FieldId", "Column"], "members": {"FieldId": {}, "Column": {"shape": "S2d"}, "DateGranularity": {}, "HierarchyId": {}, "FormatConfiguration": {"shape": "S4b"}}}}}, "S3p": {"type": "structure", "members": {"FormatConfiguration": {"shape": "S3q"}}}, "S3q": {"type": "structure", "members": {"NumberDisplayFormatConfiguration": {"shape": "S3r"}, "CurrencyDisplayFormatConfiguration": {"type": "structure", "members": {"Prefix": {"shape": "S3s"}, "Suffix": {"shape": "S3t"}, "SeparatorConfiguration": {"shape": "S3u"}, "Symbol": {}, "DecimalPlacesConfiguration": {"shape": "S3x"}, "NumberScale": {}, "NegativeValueConfiguration": {"shape": "S40"}, "NullValueFormatConfiguration": {"shape": "S42"}}}, "PercentageDisplayFormatConfiguration": {"shape": "S46"}}}, "S3r": {"type": "structure", "members": {"Prefix": {"shape": "S3s"}, "Suffix": {"shape": "S3t"}, "SeparatorConfiguration": {"shape": "S3u"}, "DecimalPlacesConfiguration": {"shape": "S3x"}, "NumberScale": {}, "NegativeValueConfiguration": {"shape": "S40"}, "NullValueFormatConfiguration": {"shape": "S42"}}}, "S3s": {"type": "string", "sensitive": true}, "S3t": {"type": "string", "sensitive": true}, "S3u": {"type": "structure", "members": {"DecimalSeparator": {}, "ThousandsSeparator": {"type": "structure", "members": {"Symbol": {}, "Visibility": {}}}}}, "S3x": {"type": "structure", "required": ["DecimalPlaces"], "members": {"DecimalPlaces": {"type": "long"}}}, "S40": {"type": "structure", "required": ["DisplayMode"], "members": {"DisplayMode": {}}}, "S42": {"type": "structure", "required": ["NullString"], "members": {"NullString": {"type": "string", "sensitive": true}}}, "S46": {"type": "structure", "members": {"Prefix": {"shape": "S3s"}, "Suffix": {"shape": "S3t"}, "SeparatorConfiguration": {"shape": "S3u"}, "DecimalPlacesConfiguration": {"shape": "S3x"}, "NegativeValueConfiguration": {"shape": "S40"}, "NullValueFormatConfiguration": {"shape": "S42"}}}, "S48": {"type": "structure", "members": {"NullValueFormatConfiguration": {"shape": "S42"}, "NumericFormatConfiguration": {"shape": "S3q"}}}, "S4b": {"type": "structure", "members": {"DateTimeFormat": {}, "NullValueFormatConfiguration": {"shape": "S42"}, "NumericFormatConfiguration": {"shape": "S3q"}}}, "S4c": {"type": "list", "member": {"shape": "S4d"}}, "S4d": {"type": "structure", "members": {"NumericalMeasureField": {"type": "structure", "required": ["FieldId", "Column"], "members": {"FieldId": {}, "Column": {"shape": "S2d"}, "AggregationFunction": {"shape": "S4f"}, "FormatConfiguration": {"shape": "S3p"}}}, "CategoricalMeasureField": {"type": "structure", "required": ["FieldId", "Column"], "members": {"FieldId": {}, "Column": {"shape": "S2d"}, "AggregationFunction": {}, "FormatConfiguration": {"shape": "S48"}}}, "DateMeasureField": {"type": "structure", "required": ["FieldId", "Column"], "members": {"FieldId": {}, "Column": {"shape": "S2d"}, "AggregationFunction": {}, "FormatConfiguration": {"shape": "S4b"}}}, "CalculatedMeasureField": {"type": "structure", "required": ["FieldId", "Expression"], "members": {"FieldId": {}, "Expression": {"shape": "S4o"}}}}}, "S4f": {"type": "structure", "members": {"SimpleNumericalAggregation": {}, "PercentileAggregation": {"type": "structure", "members": {"PercentileValue": {"type": "double"}}}}}, "S4o": {"type": "string", "sensitive": true}, "S4s": {"type": "structure", "members": {"StringFormatConfiguration": {"shape": "S48"}, "NumberFormatConfiguration": {"shape": "S3p"}, "DateTimeFormatConfiguration": {"shape": "S4b"}}}, "S4v": {"type": "structure", "members": {"FieldSort": {"shape": "S4w"}, "ColumnSort": {"shape": "S4y"}}}, "S4w": {"type": "structure", "required": ["FieldId", "Direction"], "members": {"FieldId": {}, "Direction": {}}}, "S4y": {"type": "structure", "required": ["SortBy", "Direction"], "members": {"SortBy": {"shape": "S2d"}, "Direction": {}, "AggregationFunction": {"shape": "S4z"}}}, "S4z": {"type": "structure", "members": {"NumericalAggregationFunction": {"shape": "S4f"}, "CategoricalAggregationFunction": {}, "DateAggregationFunction": {}}}, "S50": {"type": "structure", "required": ["PageSize", "PageNumber"], "members": {"PageSize": {"type": "long"}, "PageNumber": {"type": "long"}}}, "S55": {"type": "structure", "members": {"Visibility": {}, "FontConfiguration": {"shape": "S1x"}, "TextWrap": {}, "HorizontalTextAlignment": {}, "VerticalTextAlignment": {}, "BackgroundColor": {}, "Height": {"type": "integer"}, "Border": {"type": "structure", "members": {"UniformBorder": {"shape": "S5b"}, "SideSpecificBorder": {"type": "structure", "members": {"InnerVertical": {"shape": "S5b"}, "InnerHorizontal": {"shape": "S5b"}, "Left": {"shape": "S5b"}, "Right": {"shape": "S5b"}, "Top": {"shape": "S5b"}, "Bottom": {"shape": "S5b"}}}}}}}, "S5b": {"type": "structure", "members": {"Color": {}, "Thickness": {"type": "integer"}, "Style": {}}}, "S5f": {"type": "structure", "members": {"Status": {}, "RowAlternateColors": {"type": "list", "member": {}}}}, "S69": {"type": "structure", "members": {"BackgroundColor": {"shape": "S6a"}, "TextColor": {"shape": "S6a"}, "Icon": {"shape": "S6g"}}}, "S6a": {"type": "structure", "members": {"Solid": {"type": "structure", "required": ["Expression"], "members": {"Expression": {"shape": "S4o"}, "Color": {}}}, "Gradient": {"type": "structure", "required": ["Expression", "Color"], "members": {"Expression": {"shape": "S4o"}, "Color": {"type": "structure", "members": {"Stops": {"type": "list", "member": {"type": "structure", "required": ["GradientOffset"], "members": {"GradientOffset": {"type": "double"}, "DataValue": {"type": "double"}, "Color": {}}}}}}}}}}, "S6g": {"type": "structure", "members": {"IconSet": {"type": "structure", "required": ["Expression"], "members": {"Expression": {"shape": "S4o"}, "IconSetType": {}}}, "CustomCondition": {"type": "structure", "required": ["Expression", "IconOptions"], "members": {"Expression": {"shape": "S4o"}, "IconOptions": {"type": "structure", "members": {"Icon": {}, "UnicodeIcon": {}}}, "Color": {}, "DisplayConfiguration": {"type": "structure", "members": {"IconDisplayOption": {}}}}}}}, "S6q": {"type": "list", "member": {"type": "structure", "required": ["CustomActionId", "Name", "<PERSON><PERSON>", "ActionOperations"], "members": {"CustomActionId": {}, "Name": {}, "Status": {}, "Trigger": {}, "ActionOperations": {"type": "list", "member": {"type": "structure", "members": {"FilterOperation": {"type": "structure", "required": ["SelectedFieldsConfiguration", "TargetVisualsConfiguration"], "members": {"SelectedFieldsConfiguration": {"type": "structure", "members": {"SelectedFields": {"type": "list", "member": {}}, "SelectedFieldOptions": {}}}, "TargetVisualsConfiguration": {"type": "structure", "members": {"SameSheetTargetVisualConfiguration": {"type": "structure", "members": {"TargetVisuals": {"type": "list", "member": {}}, "TargetVisualOptions": {}}}}}}}, "NavigationOperation": {"type": "structure", "members": {"LocalNavigationConfiguration": {"type": "structure", "required": ["TargetSheetId"], "members": {"TargetSheetId": {}}}}}, "URLOperation": {"type": "structure", "required": ["URLTemplate", "URLTarget"], "members": {"URLTemplate": {}, "URLTarget": {}}}, "SetParametersOperation": {"type": "structure", "required": ["ParameterValueConfigurations"], "members": {"ParameterValueConfigurations": {"type": "list", "member": {"type": "structure", "required": ["DestinationParameterName", "Value"], "members": {"DestinationParameterName": {}, "Value": {"type": "structure", "members": {"CustomValuesConfiguration": {"type": "structure", "required": ["CustomValues"], "members": {"IncludeNullValue": {"type": "boolean"}, "CustomValues": {"type": "structure", "members": {"StringValues": {"shape": "S7f"}, "IntegerValues": {"shape": "S7h"}, "DecimalValues": {"shape": "S7j"}, "DateTimeValues": {"shape": "S7l"}}}}}, "SelectAllValueOptions": {}, "SourceParameterName": {}, "SourceField": {}}}}}}}}}}}}}}, "S7f": {"type": "list", "member": {"type": "string", "sensitive": true}}, "S7h": {"type": "list", "member": {"type": "long", "sensitive": true}}, "S7j": {"type": "list", "member": {"type": "double", "sensitive": true}}, "S7l": {"type": "list", "member": {"shape": "S17"}}, "S7r": {"type": "list", "member": {"shape": "S3l"}}, "S7y": {"type": "list", "member": {"shape": "S7z"}}, "S7z": {"type": "structure", "required": ["FieldId", "FieldValue"], "members": {"FieldId": {}, "FieldValue": {"shape": "S80"}}}, "S80": {"type": "string", "sensitive": true}, "S84": {"type": "structure", "members": {"TotalsVisibility": {}, "CustomLabel": {}, "FieldLevel": {}, "FieldLevelOptions": {"type": "list", "member": {"type": "structure", "members": {"FieldId": {}}}}, "TotalCellStyle": {"shape": "S55"}, "ValueCellStyle": {"shape": "S55"}, "MetricHeaderCellStyle": {"shape": "S55"}}}, "S88": {"type": "structure", "members": {"TotalsVisibility": {}, "Placement": {}, "ScrollStatus": {}, "CustomLabel": {}, "TotalCellStyle": {"shape": "S55"}, "ValueCellStyle": {"shape": "S55"}, "MetricHeaderCellStyle": {"shape": "S55"}}}, "S8p": {"type": "list", "member": {"shape": "S3l"}}, "S8r": {"type": "list", "member": {"shape": "S4v"}}, "S8s": {"type": "structure", "members": {"ItemsLimit": {"type": "long"}, "OtherCategories": {}}}, "S8w": {"type": "structure", "members": {"ChartColor": {}, "ColorMap": {"type": "list", "member": {"type": "structure", "required": ["Element", "Color"], "members": {"Element": {"shape": "S7z"}, "Color": {}, "TimeGranularity": {}}}}}}, "S8z": {"type": "structure", "members": {"MaxVisibleRows": {"type": "long"}, "MaxVisibleColumns": {"type": "long"}, "PanelConfiguration": {"type": "structure", "members": {"Title": {"type": "structure", "members": {"Visibility": {}, "FontConfiguration": {"shape": "S1x"}, "HorizontalTextAlignment": {}}}, "BorderVisibility": {}, "BorderThickness": {}, "BorderStyle": {}, "BorderColor": {}, "GutterVisibility": {}, "GutterSpacing": {}, "BackgroundVisibility": {}, "BackgroundColor": {}}}}}, "S96": {"type": "structure", "members": {"TickLabelOptions": {"type": "structure", "members": {"LabelOptions": {"shape": "S1v"}, "RotationAngle": {"type": "double"}}}, "AxisLineVisibility": {}, "GridLineVisibility": {}, "DataOptions": {"type": "structure", "members": {"NumericAxisOptions": {"type": "structure", "members": {"Scale": {"type": "structure", "members": {"Linear": {"type": "structure", "members": {"StepCount": {"type": "integer"}, "StepSize": {"type": "double"}}}, "Logarithmic": {"type": "structure", "members": {"Base": {"type": "double"}}}}}, "Range": {"type": "structure", "members": {"MinMax": {"type": "structure", "members": {"Minimum": {"type": "double"}, "Maximum": {"type": "double"}}}, "DataDriven": {"type": "structure", "members": {}}}}}}, "DateAxisOptions": {"type": "structure", "members": {"MissingDateVisibility": {}}}}}, "ScrollbarOptions": {"type": "structure", "members": {"Visibility": {}, "VisibleRange": {"type": "structure", "members": {"PercentRange": {"type": "structure", "members": {"From": {"type": "double"}, "To": {"type": "double"}}}}}}}, "AxisOffset": {}}}, "S9m": {"type": "structure", "members": {"Visibility": {}, "SortIconVisibility": {}, "AxisLabelOptions": {"type": "list", "member": {"type": "structure", "members": {"FontConfiguration": {"shape": "S1x"}, "CustomLabel": {}, "ApplyTo": {"type": "structure", "required": ["FieldId", "Column"], "members": {"FieldId": {}, "Column": {"shape": "S2d"}}}}}}}}, "S9q": {"type": "structure", "members": {"Visibility": {}, "Title": {"shape": "S1v"}, "Position": {}, "Width": {}, "Height": {}}}, "S9s": {"type": "structure", "members": {"Visibility": {}, "CategoryLabelVisibility": {}, "MeasureLabelVisibility": {}, "DataLabelTypes": {"type": "list", "member": {"type": "structure", "members": {"FieldLabelType": {"type": "structure", "members": {"FieldId": {}, "Visibility": {}}}, "DataPathLabelType": {"type": "structure", "members": {"FieldId": {}, "FieldValue": {"shape": "S80"}, "Visibility": {}}}, "RangeEndsLabelType": {"type": "structure", "members": {"Visibility": {}}}, "MinimumLabelType": {"type": "structure", "members": {"Visibility": {}}}, "MaximumLabelType": {"type": "structure", "members": {"Visibility": {}}}}}}, "Position": {}, "LabelContent": {}, "LabelFontConfiguration": {"shape": "S1x"}, "LabelColor": {}, "Overlap": {}}}, "Sa3": {"type": "structure", "members": {"TooltipVisibility": {}, "SelectedTooltipType": {}, "FieldBasedTooltip": {"type": "structure", "members": {"AggregationVisibility": {}, "TooltipTitleType": {}, "TooltipFields": {"type": "list", "member": {"type": "structure", "members": {"FieldTooltipItem": {"type": "structure", "required": ["FieldId"], "members": {"FieldId": {}, "Label": {}, "Visibility": {}}}, "ColumnTooltipItem": {"type": "structure", "required": ["Column"], "members": {"Column": {"shape": "S2d"}, "Label": {}, "Visibility": {}, "Aggregation": {"shape": "S4z"}}}}}}}}}}, "Sab": {"type": "list", "member": {"type": "structure", "required": ["DataConfiguration"], "members": {"Status": {}, "DataConfiguration": {"type": "structure", "members": {"StaticConfiguration": {"type": "structure", "required": ["Value"], "members": {"Value": {"shape": "S13"}}}, "DynamicConfiguration": {"type": "structure", "required": ["Column", "MeasureAggregationFunction", "Calculation"], "members": {"Column": {"shape": "S2d"}, "MeasureAggregationFunction": {"shape": "S4z"}, "Calculation": {"shape": "S4f"}}}, "AxisBinding": {}}}, "StyleConfiguration": {"type": "structure", "members": {"Pattern": {}, "Color": {}}}, "LabelConfiguration": {"type": "structure", "members": {"ValueLabelConfiguration": {"type": "structure", "members": {"RelativePosition": {}, "FormatConfiguration": {"shape": "S3q"}}}, "CustomLabelConfiguration": {"type": "structure", "required": ["CustomLabel"], "members": {"CustomLabel": {}}}, "FontConfiguration": {"shape": "S1x"}, "FontColor": {}, "HorizontalPosition": {}, "VerticalPosition": {}}}}}}, "Sap": {"type": "list", "member": {"type": "structure", "required": ["MeasureFieldId", "ContributorDimensions"], "members": {"MeasureFieldId": {}, "ContributorDimensions": {"type": "list", "member": {"shape": "S2d"}}}}}, "Sas": {"type": "list", "member": {"type": "structure", "members": {"ExplicitHierarchy": {"type": "structure", "required": ["HierarchyId", "Columns"], "members": {"HierarchyId": {}, "Columns": {"type": "list", "member": {"shape": "S2d"}}, "DrillDownFilters": {"shape": "<PERSON>"}}}, "DateTimeHierarchy": {"type": "structure", "required": ["HierarchyId"], "members": {"HierarchyId": {}, "DrillDownFilters": {"shape": "<PERSON>"}}}, "PredefinedHierarchy": {"type": "structure", "required": ["HierarchyId", "Columns"], "members": {"HierarchyId": {}, "Columns": {"type": "list", "member": {"shape": "S2d"}}, "DrillDownFilters": {"shape": "<PERSON>"}}}}}}, "Saw": {"type": "list", "member": {"type": "structure", "members": {"NumericEqualityFilter": {"type": "structure", "required": ["Column", "Value"], "members": {"Column": {"shape": "S2d"}, "Value": {"type": "double"}}}, "CategoryFilter": {"type": "structure", "required": ["Column", "CategoryValues"], "members": {"Column": {"shape": "S2d"}, "CategoryValues": {"shape": "Sb0"}}}, "TimeRangeFilter": {"type": "structure", "required": ["Column", "RangeMinimum", "RangeMaximum", "TimeGranularity"], "members": {"Column": {"shape": "S2d"}, "RangeMinimum": {"type": "timestamp"}, "RangeMaximum": {"type": "timestamp"}, "TimeGranularity": {}}}}}}, "Sb0": {"type": "list", "member": {}}, "Sbf": {"type": "structure", "members": {"ComparisonMethod": {}, "ComparisonFormat": {"type": "structure", "members": {"NumberDisplayFormatConfiguration": {"shape": "S3r"}, "PercentageDisplayFormatConfiguration": {"shape": "S46"}}}}}, "Scq": {"type": "structure", "members": {"AxisOptions": {"shape": "S96"}, "MissingDataConfigurations": {"type": "list", "member": {"type": "structure", "members": {"TreatmentOption": {}}}}}}, "Scv": {"type": "structure", "members": {"LineVisibility": {}, "LineInterpolation": {}, "LineStyle": {}, "LineWidth": {}}}, "Scy": {"type": "structure", "members": {"MarkerVisibility": {}, "MarkerShape": {}, "MarkerSize": {}, "MarkerColor": {}}}, "Sd3": {"type": "structure", "members": {"LineStyleSettings": {"shape": "Scv"}, "MarkerStyleSettings": {"shape": "<PERSON><PERSON>"}}}, "Sd9": {"type": "list", "member": {"shape": "S3l"}}, "Sdc": {"type": "structure", "required": ["Colors", "ColorFillType"], "members": {"Colors": {"type": "list", "member": {"shape": "Sde"}}, "ColorFillType": {}, "NullValueColor": {"shape": "Sde"}}}, "Sde": {"type": "structure", "members": {"Color": {}, "DataValue": {"type": "double"}}}, "Sdl": {"type": "list", "member": {"shape": "S4d"}}, "Sdr": {"type": "structure", "members": {"Bounds": {"type": "structure", "required": ["North", "South", "West", "East"], "members": {"North": {"type": "double"}, "South": {"type": "double"}, "West": {"type": "double"}, "East": {"type": "double"}}}, "MapZoomMode": {}}}, "Sdw": {"type": "structure", "members": {"BaseMapStyle": {}}}, "Shk": {"type": "structure", "required": ["Elements"], "members": {"Elements": {"type": "list", "member": {"type": "structure", "required": ["ElementId", "ElementType", "ColumnSpan", "RowSpan"], "members": {"ElementId": {}, "ElementType": {}, "ColumnIndex": {"type": "integer"}, "ColumnSpan": {"type": "integer"}, "RowIndex": {"type": "integer"}, "RowSpan": {"type": "integer"}}}}, "CanvasSizeOptions": {"shape": "Shs"}}}, "Shs": {"type": "structure", "members": {"ScreenCanvasSizeOptions": {"type": "structure", "required": ["ResizeOption"], "members": {"ResizeOption": {}, "OptimizedViewPortWidth": {}}}}}, "Shw": {"type": "list", "member": {"type": "structure", "required": ["ElementId", "ElementType", "XAxisLocation", "YAxisLocation", "<PERSON><PERSON><PERSON>", "Height"], "members": {"ElementId": {}, "ElementType": {}, "XAxisLocation": {}, "YAxisLocation": {}, "Width": {}, "Height": {}, "Visibility": {}, "RenderingRules": {"type": "list", "member": {"type": "structure", "required": ["Expression", "ConfigurationOverrides"], "members": {"Expression": {"shape": "S4o"}, "ConfigurationOverrides": {"type": "structure", "members": {"Visibility": {}}}}}}, "BorderStyle": {"shape": "Si2"}, "SelectedBorderStyle": {"shape": "Si2"}, "BackgroundStyle": {"type": "structure", "members": {"Visibility": {}, "Color": {}}}, "LoadingAnimation": {"type": "structure", "members": {"Visibility": {}}}}}}, "Si2": {"type": "structure", "members": {"Visibility": {}, "Color": {}}}, "Si5": {"type": "structure", "members": {"ScreenCanvasSizeOptions": {"type": "structure", "required": ["OptimizedViewPortWidth"], "members": {"OptimizedViewPortWidth": {}}}}}, "Si8": {"type": "list", "member": {"type": "structure", "required": ["SectionId", "Layout"], "members": {"SectionId": {}, "Layout": {"shape": "Sia"}, "Style": {"shape": "Sic"}}}}, "Sia": {"type": "structure", "required": ["FreeFormLayout"], "members": {"FreeFormLayout": {"type": "structure", "required": ["Elements"], "members": {"Elements": {"shape": "Shw"}}}}}, "Sic": {"type": "structure", "members": {"Height": {}, "Padding": {"shape": "Sid"}}}, "Sid": {"type": "structure", "members": {"Top": {}, "Bottom": {}, "Left": {}, "Right": {}}}, "Sil": {"type": "structure", "members": {"PaperCanvasSizeOptions": {"type": "structure", "members": {"PaperSize": {}, "PaperOrientation": {}, "PaperMargin": {"shape": "Sid"}}}}}, "Sit": {"type": "list", "member": {"type": "structure", "required": ["DataSetIdentifier", "Name", "Expression"], "members": {"DataSetIdentifier": {}, "Name": {}, "Expression": {"shape": "S4o"}}}}, "Siv": {"type": "list", "member": {"type": "structure", "members": {"StringParameterDeclaration": {"type": "structure", "required": ["ParameterValueType", "Name"], "members": {"ParameterValueType": {}, "Name": {}, "DefaultValues": {"type": "structure", "members": {"DynamicValue": {"shape": "Sj0"}, "StaticValues": {"shape": "S7f"}}}, "ValueWhenUnset": {"type": "structure", "members": {"ValueWhenUnsetOption": {}, "CustomValue": {"shape": "Sv"}}}}}, "DecimalParameterDeclaration": {"type": "structure", "required": ["ParameterValueType", "Name"], "members": {"ParameterValueType": {}, "Name": {}, "DefaultValues": {"type": "structure", "members": {"DynamicValue": {"shape": "Sj0"}, "StaticValues": {"shape": "S7j"}}}, "ValueWhenUnset": {"type": "structure", "members": {"ValueWhenUnsetOption": {}, "CustomValue": {"shape": "S13"}}}}}, "IntegerParameterDeclaration": {"type": "structure", "required": ["ParameterValueType", "Name"], "members": {"ParameterValueType": {}, "Name": {}, "DefaultValues": {"type": "structure", "members": {"DynamicValue": {"shape": "Sj0"}, "StaticValues": {"shape": "S7h"}}}, "ValueWhenUnset": {"type": "structure", "members": {"ValueWhenUnsetOption": {}, "CustomValue": {"shape": "Sz"}}}}}, "DateTimeParameterDeclaration": {"type": "structure", "required": ["Name"], "members": {"Name": {}, "DefaultValues": {"type": "structure", "members": {"DynamicValue": {"shape": "Sj0"}, "StaticValues": {"shape": "S7l"}, "RollingDate": {"shape": "Sjb"}}}, "TimeGranularity": {}, "ValueWhenUnset": {"type": "structure", "members": {"ValueWhenUnsetOption": {}, "CustomValue": {"shape": "S17"}}}}}}}}, "Sj0": {"type": "structure", "required": ["DefaultValueColumn"], "members": {"UserNameColumn": {"shape": "S2d"}, "GroupNameColumn": {"shape": "S2d"}, "DefaultValueColumn": {"shape": "S2d"}}}, "Sjb": {"type": "structure", "required": ["Expression"], "members": {"DataSetIdentifier": {}, "Expression": {"shape": "S4o"}}}, "Sjd": {"type": "list", "member": {"type": "structure", "required": ["FilterGroupId", "Filters", "ScopeConfiguration", "CrossDataset"], "members": {"FilterGroupId": {}, "Filters": {"type": "list", "member": {"type": "structure", "members": {"CategoryFilter": {"type": "structure", "required": ["FilterId", "Column", "Configuration"], "members": {"FilterId": {}, "Column": {"shape": "S2d"}, "Configuration": {"type": "structure", "members": {"FilterListConfiguration": {"type": "structure", "required": ["MatchOperator"], "members": {"MatchOperator": {}, "CategoryValues": {"shape": "Sb0"}, "SelectAllOptions": {}}}, "CustomFilterListConfiguration": {"type": "structure", "required": ["MatchOperator", "NullOption"], "members": {"MatchOperator": {}, "CategoryValues": {"shape": "Sb0"}, "SelectAllOptions": {}, "NullOption": {}}}, "CustomFilterConfiguration": {"type": "structure", "required": ["MatchOperator", "NullOption"], "members": {"MatchOperator": {}, "CategoryValue": {}, "SelectAllOptions": {}, "ParameterName": {}, "NullOption": {}}}}}}}, "NumericRangeFilter": {"type": "structure", "required": ["FilterId", "Column", "NullOption"], "members": {"FilterId": {}, "Column": {"shape": "S2d"}, "IncludeMinimum": {"type": "boolean"}, "IncludeMaximum": {"type": "boolean"}, "RangeMinimum": {"shape": "Sjq"}, "RangeMaximum": {"shape": "Sjq"}, "SelectAllOptions": {}, "AggregationFunction": {"shape": "S4z"}, "NullOption": {}}}, "NumericEqualityFilter": {"type": "structure", "required": ["FilterId", "Column", "MatchOperator", "NullOption"], "members": {"FilterId": {}, "Column": {"shape": "S2d"}, "Value": {"type": "double"}, "SelectAllOptions": {}, "MatchOperator": {}, "AggregationFunction": {"shape": "S4z"}, "ParameterName": {}, "NullOption": {}}}, "TimeEqualityFilter": {"type": "structure", "required": ["FilterId", "Column"], "members": {"FilterId": {}, "Column": {"shape": "S2d"}, "Value": {"type": "timestamp"}, "ParameterName": {}, "TimeGranularity": {}}}, "TimeRangeFilter": {"type": "structure", "required": ["FilterId", "Column", "NullOption"], "members": {"FilterId": {}, "Column": {"shape": "S2d"}, "IncludeMinimum": {"type": "boolean"}, "IncludeMaximum": {"type": "boolean"}, "RangeMinimumValue": {"shape": "Sjw"}, "RangeMaximumValue": {"shape": "Sjw"}, "NullOption": {}, "ExcludePeriodConfiguration": {"shape": "Sjx"}, "TimeGranularity": {}}}, "RelativeDatesFilter": {"type": "structure", "required": ["FilterId", "Column", "AnchorDateConfiguration", "TimeGranularity", "RelativeDateType", "NullOption"], "members": {"FilterId": {}, "Column": {"shape": "S2d"}, "AnchorDateConfiguration": {"type": "structure", "members": {"AnchorOption": {}, "ParameterName": {}}}, "MinimumGranularity": {}, "TimeGranularity": {}, "RelativeDateType": {}, "RelativeDateValue": {"type": "integer"}, "ParameterName": {}, "NullOption": {}, "ExcludePeriodConfiguration": {"shape": "Sjx"}}}, "TopBottomFilter": {"type": "structure", "required": ["FilterId", "Column", "AggregationSortConfigurations"], "members": {"FilterId": {}, "Column": {"shape": "S2d"}, "Limit": {"type": "integer"}, "AggregationSortConfigurations": {"type": "list", "member": {"type": "structure", "required": ["Column", "SortDirection", "AggregationFunction"], "members": {"Column": {"shape": "S2d"}, "SortDirection": {}, "AggregationFunction": {"shape": "S4z"}}}}, "TimeGranularity": {}, "ParameterName": {}}}}}}, "ScopeConfiguration": {"type": "structure", "members": {"SelectedSheets": {"type": "structure", "members": {"SheetVisualScopingConfigurations": {"type": "list", "member": {"type": "structure", "required": ["SheetId", "<PERSON><PERSON>"], "members": {"SheetId": {}, "Scope": {}, "VisualIds": {"type": "list", "member": {}}}}}}}}}, "Status": {}, "CrossDataset": {}}}}, "Sjq": {"type": "structure", "members": {"StaticValue": {"type": "double"}, "Parameter": {}}}, "Sjw": {"type": "structure", "members": {"StaticValue": {"type": "timestamp"}, "RollingDate": {"shape": "Sjb"}, "Parameter": {}}}, "Sjx": {"type": "structure", "required": ["Amount", "Granularity"], "members": {"Amount": {"type": "integer"}, "Granularity": {}, "Status": {}}}, "Skc": {"type": "list", "member": {"type": "structure", "required": ["Column"], "members": {"Column": {"shape": "S2d"}, "FormatConfiguration": {"shape": "S4s"}, "Role": {}}}}, "Skf": {"type": "structure", "required": ["DefaultNewSheetConfiguration"], "members": {"DefaultNewSheetConfiguration": {"type": "structure", "members": {"InteractiveLayoutConfiguration": {"type": "structure", "members": {"Grid": {"type": "structure", "required": ["CanvasSizeOptions"], "members": {"CanvasSizeOptions": {"shape": "Shs"}}}, "FreeForm": {"type": "structure", "required": ["CanvasSizeOptions"], "members": {"CanvasSizeOptions": {"shape": "Si5"}}}}}, "PaginatedLayoutConfiguration": {"type": "structure", "members": {"SectionBased": {"type": "structure", "required": ["CanvasSizeOptions"], "members": {"CanvasSizeOptions": {"shape": "Sil"}}}}}, "SheetContentType": {}}}}}, "Skq": {"type": "structure", "members": {"SourceTemplate": {"type": "structure", "required": ["DataSetReferences", "<PERSON><PERSON>"], "members": {"DataSetReferences": {"shape": "S1e"}, "Arn": {}}}}}, "Skt": {"type": "structure", "members": {"AdHocFilteringOption": {"type": "structure", "members": {"AvailabilityStatus": {}}}, "ExportToCSVOption": {"type": "structure", "members": {"AvailabilityStatus": {}}}, "SheetControlsOption": {"type": "structure", "members": {"VisibilityState": {}}}, "VisualPublishOptions": {"deprecated": true, "deprecatedMessage": "VisualPublishOptions property will reach its end of standard support in a future release. To perform this action, use ExportWithHiddenFields.", "type": "structure", "members": {"ExportHiddenFieldsOption": {"type": "structure", "members": {"AvailabilityStatus": {}}}}}, "SheetLayoutElementMaximizationOption": {"type": "structure", "members": {"AvailabilityStatus": {}}}, "VisualMenuOption": {"type": "structure", "members": {"AvailabilityStatus": {}}}, "VisualAxisSortOption": {"type": "structure", "members": {"AvailabilityStatus": {}}}, "ExportWithHiddenFieldsOption": {"type": "structure", "members": {"AvailabilityStatus": {}}}, "DataPointDrillUpDownOption": {"type": "structure", "members": {"AvailabilityStatus": {}}}, "DataPointMenuLabelOption": {"type": "structure", "members": {"AvailabilityStatus": {}}}, "DataPointTooltipOption": {"type": "structure", "members": {"AvailabilityStatus": {}}}}}, "Sl8": {"type": "structure", "required": ["DataSetIdentifierDeclarations"], "members": {"DataSetIdentifierDeclarations": {"shape": "S1h"}, "Sheets": {"shape": "S1k"}, "CalculatedFields": {"shape": "Sit"}, "ParameterDeclarations": {"shape": "Siv"}, "FilterGroups": {"shape": "Sjd"}, "ColumnConfigurations": {"shape": "Skc"}, "AnalysisDefaults": {"shape": "Skf"}}}, "Sld": {"type": "map", "key": {}, "value": {"type": "structure", "members": {"RelationalTable": {"type": "structure", "required": ["DataSourceArn", "Name", "InputColumns"], "members": {"DataSourceArn": {}, "Catalog": {}, "Schema": {}, "Name": {}, "InputColumns": {"shape": "Slk"}}}, "CustomSql": {"type": "structure", "required": ["DataSourceArn", "Name", "SqlQuery"], "members": {"DataSourceArn": {}, "Name": {}, "SqlQuery": {}, "Columns": {"shape": "Slk"}}}, "S3Source": {"type": "structure", "required": ["DataSourceArn", "InputColumns"], "members": {"DataSourceArn": {}, "UploadSettings": {"type": "structure", "members": {"Format": {}, "StartFromRow": {"type": "integer"}, "ContainsHeader": {"type": "boolean"}, "TextQualifier": {}, "Delimiter": {}}}, "InputColumns": {"shape": "Slk"}}}}}}, "Slk": {"type": "list", "member": {"type": "structure", "required": ["Name", "Type"], "members": {"Name": {}, "Type": {}}}}, "Slw": {"type": "map", "key": {}, "value": {"type": "structure", "required": ["<PERSON><PERSON>", "Source"], "members": {"Alias": {}, "DataTransforms": {"type": "list", "member": {"type": "structure", "members": {"ProjectOperation": {"type": "structure", "required": ["ProjectedColumns"], "members": {"ProjectedColumns": {"type": "list", "member": {}}}}, "FilterOperation": {"type": "structure", "required": ["ConditionExpression"], "members": {"ConditionExpression": {"shape": "S4o"}}}, "CreateColumnsOperation": {"type": "structure", "required": ["Columns"], "members": {"Columns": {"type": "list", "member": {"type": "structure", "required": ["ColumnName", "ColumnId", "Expression"], "members": {"ColumnName": {}, "ColumnId": {}, "Expression": {"shape": "S4o"}}}}}}, "RenameColumnOperation": {"type": "structure", "required": ["ColumnName", "NewColumnName"], "members": {"ColumnName": {}, "NewColumnName": {}}}, "CastColumnTypeOperation": {"type": "structure", "required": ["ColumnName", "NewColumnType"], "members": {"ColumnName": {}, "NewColumnType": {}, "Format": {}}}, "TagColumnOperation": {"type": "structure", "required": ["ColumnName", "Tags"], "members": {"ColumnName": {}, "Tags": {"type": "list", "member": {"type": "structure", "members": {"ColumnGeographicRole": {}, "ColumnDescription": {"type": "structure", "members": {"Text": {}}}}}}}}, "UntagColumnOperation": {"type": "structure", "required": ["ColumnName", "TagNames"], "members": {"ColumnName": {}, "TagNames": {"type": "list", "member": {}}}}}}}, "Source": {"type": "structure", "members": {"JoinInstruction": {"type": "structure", "required": ["LeftOperand", "RightOperand", "Type", "OnClause"], "members": {"LeftOperand": {}, "RightOperand": {}, "LeftJoinKeyProperties": {"shape": "Smo"}, "RightJoinKeyProperties": {"shape": "Smo"}, "Type": {}, "OnClause": {}}}, "PhysicalTableId": {}, "DataSetArn": {}}}}}}, "Smo": {"type": "structure", "members": {"UniqueKey": {"type": "boolean"}}}, "Sms": {"type": "list", "member": {"type": "structure", "members": {"GeoSpatialColumnGroup": {"type": "structure", "required": ["Name", "Columns"], "members": {"Name": {}, "CountryCode": {}, "Columns": {"type": "list", "member": {}}}}}}}, "Smy": {"type": "map", "key": {}, "value": {"type": "structure", "members": {"description": {}, "columns": {"type": "list", "member": {}}}}}, "Sn3": {"type": "structure", "required": ["<PERSON><PERSON>", "PermissionPolicy"], "members": {"Namespace": {}, "Arn": {}, "PermissionPolicy": {}, "FormatVersion": {}, "Status": {}}}, "Sn7": {"type": "structure", "required": ["TagRules"], "members": {"Status": {}, "TagRules": {"type": "list", "member": {"type": "structure", "required": ["TagKey", "ColumnName"], "members": {"TagKey": {}, "ColumnName": {}, "TagMultiValueDelimiter": {}, "MatchAllValue": {"shape": "Snc"}}}}}}, "Snc": {"type": "string", "sensitive": true}, "Snd": {"type": "list", "member": {"type": "structure", "members": {"Principals": {"type": "list", "member": {}}, "ColumnNames": {"type": "list", "member": {}}}}}, "Snh": {"type": "structure", "members": {"DisableUseAsDirectQuerySource": {"type": "boolean"}, "DisableUseAsImportedSource": {"type": "boolean"}}}, "Snl": {"type": "structure", "members": {"AmazonElasticsearchParameters": {"type": "structure", "required": ["Domain"], "members": {"Domain": {}}}, "AthenaParameters": {"type": "structure", "members": {"WorkGroup": {}, "RoleArn": {}}}, "AuroraParameters": {"type": "structure", "required": ["Host", "Port", "Database"], "members": {"Host": {}, "Port": {"type": "integer"}, "Database": {}}}, "AuroraPostgreSqlParameters": {"type": "structure", "required": ["Host", "Port", "Database"], "members": {"Host": {}, "Port": {"type": "integer"}, "Database": {}}}, "AwsIotAnalyticsParameters": {"type": "structure", "required": ["DataSetName"], "members": {"DataSetName": {}}}, "JiraParameters": {"type": "structure", "required": ["SiteBaseUrl"], "members": {"SiteBaseUrl": {}}}, "MariaDbParameters": {"type": "structure", "required": ["Host", "Port", "Database"], "members": {"Host": {}, "Port": {"type": "integer"}, "Database": {}}}, "MySqlParameters": {"type": "structure", "required": ["Host", "Port", "Database"], "members": {"Host": {}, "Port": {"type": "integer"}, "Database": {}}}, "OracleParameters": {"type": "structure", "required": ["Host", "Port", "Database"], "members": {"Host": {}, "Port": {"type": "integer"}, "Database": {}}}, "PostgreSqlParameters": {"type": "structure", "required": ["Host", "Port", "Database"], "members": {"Host": {}, "Port": {"type": "integer"}, "Database": {}}}, "PrestoParameters": {"type": "structure", "required": ["Host", "Port", "Catalog"], "members": {"Host": {}, "Port": {"type": "integer"}, "Catalog": {}}}, "RdsParameters": {"type": "structure", "required": ["InstanceId", "Database"], "members": {"InstanceId": {}, "Database": {}}}, "RedshiftParameters": {"type": "structure", "required": ["Database"], "members": {"Host": {}, "Port": {"type": "integer"}, "Database": {}, "ClusterId": {}}}, "S3Parameters": {"type": "structure", "required": ["ManifestFileLocation"], "members": {"ManifestFileLocation": {"type": "structure", "required": ["Bucket", "Key"], "members": {"Bucket": {}, "Key": {}}}}}, "ServiceNowParameters": {"type": "structure", "required": ["SiteBaseUrl"], "members": {"SiteBaseUrl": {}}}, "SnowflakeParameters": {"type": "structure", "required": ["Host", "Database", "Warehouse"], "members": {"Host": {}, "Database": {}, "Warehouse": {}}}, "SparkParameters": {"type": "structure", "required": ["Host", "Port"], "members": {"Host": {}, "Port": {"type": "integer"}}}, "SqlServerParameters": {"type": "structure", "required": ["Host", "Port", "Database"], "members": {"Host": {}, "Port": {"type": "integer"}, "Database": {}}}, "TeradataParameters": {"type": "structure", "required": ["Host", "Port", "Database"], "members": {"Host": {}, "Port": {"type": "integer"}, "Database": {}}}, "TwitterParameters": {"type": "structure", "required": ["Query", "MaxRows"], "members": {"Query": {}, "MaxRows": {"type": "integer"}}}, "AmazonOpenSearchParameters": {"type": "structure", "required": ["Domain"], "members": {"Domain": {}}}, "ExasolParameters": {"type": "structure", "required": ["Host", "Port"], "members": {"Host": {}, "Port": {"type": "integer"}}}, "DatabricksParameters": {"type": "structure", "required": ["Host", "Port", "SqlEndpointPath"], "members": {"Host": {}, "Port": {"type": "integer"}, "SqlEndpointPath": {}}}}}, "Sor": {"type": "structure", "members": {"CredentialPair": {"type": "structure", "required": ["Username", "Password"], "members": {"Username": {}, "Password": {}, "AlternateDataSourceParameters": {"shape": "Sov"}}}, "CopySourceArn": {}, "SecretArn": {}}, "sensitive": true}, "Sov": {"type": "list", "member": {"shape": "Snl"}}, "Soy": {"type": "structure", "required": ["VpcConnectionArn"], "members": {"VpcConnectionArn": {}}}, "Soz": {"type": "structure", "members": {"DisableSsl": {"type": "boolean"}}}, "Spe": {"type": "structure", "members": {"Arn": {}, "GroupName": {}, "Description": {}, "PrincipalId": {}}}, "Spi": {"type": "structure", "members": {"Arn": {}, "MemberName": {}}}, "Spm": {"type": "map", "key": {}, "value": {"type": "list", "member": {}}}, "Sq0": {"type": "structure", "members": {"SourceAnalysis": {"type": "structure", "required": ["<PERSON><PERSON>", "DataSetReferences"], "members": {"Arn": {}, "DataSetReferences": {"shape": "S1e"}}}, "SourceTemplate": {"type": "structure", "required": ["<PERSON><PERSON>"], "members": {"Arn": {}}}}}, "Sq3": {"type": "structure", "required": ["DataSetConfigurations"], "members": {"DataSetConfigurations": {"shape": "Sq4"}, "Sheets": {"shape": "S1k"}, "CalculatedFields": {"shape": "Sit"}, "ParameterDeclarations": {"shape": "Siv"}, "FilterGroups": {"shape": "Sjd"}, "ColumnConfigurations": {"shape": "Skc"}, "AnalysisDefaults": {"shape": "Skf"}}}, "Sq4": {"type": "list", "member": {"type": "structure", "members": {"Placeholder": {}, "DataSetSchema": {"type": "structure", "members": {"ColumnSchemaList": {"type": "list", "member": {"type": "structure", "members": {"Name": {}, "DataType": {}, "GeographicRole": {}}}}}}, "ColumnGroupSchemaList": {"type": "list", "member": {"type": "structure", "members": {"Name": {}, "ColumnGroupColumnSchemaList": {"type": "list", "member": {"type": "structure", "members": {"Name": {}}}}}}}}}}, "Sqi": {"type": "structure", "members": {"AliasName": {}, "Arn": {}, "TemplateVersionNumber": {"type": "long"}}}, "Sql": {"type": "structure", "members": {"DataColorPalette": {"type": "structure", "members": {"Colors": {"shape": "Sqn"}, "MinMaxGradient": {"shape": "Sqn"}, "EmptyFillColor": {}}}, "UIColorPalette": {"type": "structure", "members": {"PrimaryForeground": {}, "PrimaryBackground": {}, "SecondaryForeground": {}, "SecondaryBackground": {}, "Accent": {}, "AccentForeground": {}, "Danger": {}, "DangerForeground": {}, "Warning": {}, "WarningForeground": {}, "Success": {}, "SuccessForeground": {}, "Dimension": {}, "DimensionForeground": {}, "Measure": {}, "MeasureForeground": {}}}, "Sheet": {"type": "structure", "members": {"Tile": {"type": "structure", "members": {"Border": {"type": "structure", "members": {"Show": {"type": "boolean"}}}}}, "TileLayout": {"type": "structure", "members": {"Gutter": {"type": "structure", "members": {"Show": {"type": "boolean"}}}, "Margin": {"type": "structure", "members": {"Show": {"type": "boolean"}}}}}}}, "Typography": {"type": "structure", "members": {"FontFamilies": {"type": "list", "member": {"type": "structure", "members": {"FontFamily": {}}}}}}}}, "Sqn": {"type": "list", "member": {}}, "Sr1": {"type": "structure", "members": {"Arn": {}, "AliasName": {}, "ThemeVersionNumber": {"type": "long"}}}, "Ssg": {"type": "list", "member": {"type": "structure", "members": {"Type": {}, "Message": {}, "ViolatedEntities": {"shape": "Ssj"}}}}, "Ssj": {"type": "list", "member": {"type": "structure", "members": {"Path": {}}}}, "Ssl": {"type": "list", "member": {}}, "Ssm": {"type": "list", "member": {"type": "structure", "members": {"SheetId": {}, "Name": {}}}}, "Ssw": {"type": "list", "member": {"type": "structure", "members": {"Type": {}, "Message": {}, "ViolatedEntities": {"shape": "Ssj"}}}}, "St3": {"type": "structure", "members": {"Permissions": {"shape": "S18"}}}, "Std": {"type": "structure", "members": {"Arn": {}, "DataSourceId": {}, "Name": {}, "Type": {}, "Status": {}, "CreatedTime": {"type": "timestamp"}, "LastUpdatedTime": {"type": "timestamp"}, "DataSourceParameters": {"shape": "Snl"}, "AlternateDataSourceParameters": {"shape": "Sov"}, "VpcConnectionProperties": {"shape": "Soy"}, "SslProperties": {"shape": "Soz"}, "ErrorInfo": {"type": "structure", "members": {"Type": {}, "Message": {}}}, "SecretArn": {}}}, "Stz": {"type": "structure", "required": ["<PERSON><PERSON>", "IngestionStatus", "CreatedTime"], "members": {"Arn": {}, "IngestionId": {}, "IngestionStatus": {}, "ErrorInfo": {"type": "structure", "members": {"Type": {}, "Message": {}}}, "RowInfo": {"type": "structure", "members": {"RowsIngested": {"type": "long"}, "RowsDropped": {"type": "long"}, "TotalRowsInDataset": {"type": "long"}}}, "QueueInfo": {"type": "structure", "required": ["WaitingOnIngestion", "QueuedIngestion"], "members": {"WaitingOnIngestion": {}, "QueuedIngestion": {}}}, "CreatedTime": {"type": "timestamp"}, "IngestionTimeInSeconds": {"type": "long"}, "IngestionSizeInBytes": {"type": "long"}, "RequestSource": {}, "RequestType": {}}}, "Su8": {"type": "map", "key": {}, "value": {}}, "Sue": {"type": "structure", "members": {"Name": {}, "Arn": {}, "CapacityRegion": {}, "CreationStatus": {}, "IdentityStore": {}, "NamespaceError": {"type": "structure", "members": {"Type": {}, "Message": {}}}}}, "Sul": {"type": "list", "member": {"type": "structure", "members": {"Type": {}, "Message": {}, "ViolatedEntities": {"shape": "Ssj"}}}}, "Sv9": {"type": "structure", "members": {"Arn": {}, "UserName": {}, "Email": {}, "Role": {}, "IdentityType": {}, "Active": {"type": "boolean"}, "PrincipalId": {}, "CustomPermissionsName": {}, "ExternalLoginFederationProviderType": {}, "ExternalLoginFederationProviderUrl": {}, "ExternalLoginId": {}}}, "Svl": {"type": "structure", "required": ["DashboardId", "SheetId", "VisualId"], "members": {"DashboardId": {}, "SheetId": {}, "VisualId": {}}}, "Svn": {"type": "list", "member": {}}, "Svp": {"type": "string", "sensitive": true}, "Sw8": {"type": "list", "member": {"type": "structure", "members": {"Arn": {}, "AnalysisId": {}, "Name": {}, "Status": {}, "CreatedTime": {"type": "timestamp"}, "LastUpdatedTime": {"type": "timestamp"}}}}, "Swg": {"type": "list", "member": {"type": "structure", "members": {"Arn": {}, "DashboardId": {}, "Name": {}, "CreatedTime": {"type": "timestamp"}, "LastUpdatedTime": {"type": "timestamp"}, "PublishedVersionNumber": {"type": "long"}, "LastPublishedTime": {"type": "timestamp"}}}}, "Swk": {"type": "list", "member": {"type": "structure", "members": {"Arn": {}, "DataSetId": {}, "Name": {}, "CreatedTime": {"type": "timestamp"}, "LastUpdatedTime": {"type": "timestamp"}, "ImportMode": {}, "RowLevelPermissionDataSet": {"shape": "Sn3"}, "RowLevelPermissionTagConfigurationApplied": {"type": "boolean"}, "ColumnLevelPermissionRulesApplied": {"type": "boolean"}}}}, "Swv": {"type": "list", "member": {"type": "structure", "members": {"Arn": {}, "FolderId": {}, "Name": {}, "FolderType": {}, "CreatedTime": {"type": "timestamp"}, "LastUpdatedTime": {"type": "timestamp"}}}}, "Sx2": {"type": "list", "member": {"shape": "Spe"}}, "Szq": {"type": "list", "member": {"shape": "S19"}}, "Szv": {"type": "list", "member": {"shape": "S19"}}}}