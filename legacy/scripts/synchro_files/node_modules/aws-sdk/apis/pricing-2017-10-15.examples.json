{"version": "1.0", "examples": {"DescribeServices": [{"input": {"FormatVersion": "aws_v1", "MaxResults": 1, "ServiceCode": "AmazonEC2"}, "output": {"FormatVersion": "aws_v1", "NextToken": "abcdefg123", "Services": [{"AttributeNames": ["volumeType", "maxIopsvolume", "instanceCapacity10xlarge", "locationType", "operation"], "ServiceCode": "AmazonEC2"}]}, "comments": {"input": {}, "output": {}}, "description": "Retrieves the service for the given Service Code.", "id": "to-retrieve-service-metadata", "title": "To retrieve a list of services and service codes"}], "GetAttributeValues": [{"input": {"AttributeName": "volumeType", "MaxResults": 2, "ServiceCode": "AmazonEC2"}, "output": {"AttributeValues": [{"Value": "Throughput Optimized HDD"}, {"Value": "Provisioned IOPS"}], "NextToken": "GpgauEXAMPLEezucl5LV0w==:7GzYJ0nw0DBTJ2J66EoTIIynE6O1uXwQtTRqioJzQadBnDVgHPzI1en4BUQnPCLpzeBk9RQQAWaFieA4+DapFAGLgk+Z/9/cTw9GldnPOHN98+FdmJP7wKU3QQpQ8MQr5KOeBkIsAqvAQYdL0DkL7tHwPtE5iCEByAmg9gcC/yBU1vAOsf7R3VaNN4M5jMDv3woSWqASSIlBVB6tgW78YL22KhssoItM/jWW+aP6Jqtq4mldxp/ct6DWAl+xLFwHU/CbketimPPXyqHF3/UXDw=="}, "comments": {"input": {}, "output": {}}, "description": "This operation returns a list of values available for the given attribute.", "id": "to-retreive-attribute-values", "title": "To retrieve a list of attribute values"}]}}