{"_args": [["assertion-error@1.1.0", "/home/<USER>/scripts/synchro_files"]], "_development": true, "_from": "assertion-error@1.1.0", "_id": "assertion-error@1.1.0", "_inBundle": false, "_integrity": "sha512-jgsaNduz+ndvGyFt3uSuWqvy4lCnIJiovtouQN5JZHOKCS2QuhEdbcQHFhVksz2N2U9hXJo8odG7ETyWlEeuDw==", "_location": "/assertion-error", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "assertion-error@1.1.0", "name": "assertion-error", "escapedName": "assertion-error", "rawSpec": "1.1.0", "saveSpec": null, "fetchSpec": "1.1.0"}, "_requiredBy": ["/chai"], "_resolved": "https://registry.npmjs.org/assertion-error/-/assertion-error-1.1.0.tgz", "_spec": "1.1.0", "_where": "/home/<USER>/scripts/synchro_files", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://qualiancy.com"}, "bugs": {"url": "https://github.com/chaijs/assertion-error/issues"}, "dependencies": {}, "description": "Error constructor for test and validation frameworks that implements standardized AssertionError specification.", "devDependencies": {"component": "*", "typescript": "^2.6.1"}, "engines": {"node": "*"}, "homepage": "https://github.com/chaijs/assertion-error#readme", "keywords": ["test", "assertion", "assertion-error"], "license": "MIT", "main": "./index", "name": "assertion-error", "repository": {"type": "git", "url": "git+ssh://**************/chaijs/assertion-error.git"}, "scripts": {"test": "make test"}, "types": "./index.d.ts", "version": "1.1.0"}