{"_args": [["@sinonjs/text-encoding@0.7.2", "/home/<USER>/scripts/synchro_files"]], "_development": true, "_from": "@sinonjs/text-encoding@0.7.2", "_id": "@sinonjs/text-encoding@0.7.2", "_inBundle": false, "_integrity": "sha512-sXXKG+uL9IrKqViTtao2Ws6dy0znu9sOaP1di/jKGW1M6VssO8vlpXCQcpZ+jisQ1tTFAC5Jo/EOzFbggBagFQ==", "_location": "/@sinonjs/text-encoding", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "@sinonjs/text-encoding@0.7.2", "name": "@sinonjs/text-encoding", "escapedName": "@sinonjs%2ftext-encoding", "scope": "@sinonjs", "rawSpec": "0.7.2", "saveSpec": null, "fetchSpec": "0.7.2"}, "_requiredBy": ["/nise"], "_resolved": "https://registry.npmjs.org/@sinonjs/text-encoding/-/text-encoding-0.7.2.tgz", "_spec": "0.7.2", "_where": "/home/<USER>/scripts/synchro_files", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "bugs": {"url": "https://github.com/sinonjs/text-encoding/issues"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "filip.du<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Author: <PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Polyfill for the Encoding Living Standard's API.", "files": ["index.js", "lib/encoding.js", "lib/encoding-indexes.js"], "homepage": "https://github.com/sinonjs/text-encoding", "keywords": ["encoding", "decoding", "living standard"], "license": "(Unlicense OR Apache-2.0)", "main": "index.js", "name": "@sinonjs/text-encoding", "repository": {"type": "git", "url": "git+https://github.com/sinonjs/text-encoding.git"}, "version": "0.7.2"}