{"_args": [["@sinonjs/formatio@2.0.0", "/home/<USER>/scripts/synchro_files"]], "_development": true, "_from": "@sinonjs/formatio@2.0.0", "_id": "@sinonjs/formatio@2.0.0", "_inBundle": false, "_integrity": "sha512-ls6CAMA6/5gG+O/IdsBcblvnd8qcO/l1TYoNeAzp3wcISOxlPXQEus0mLcdwazEkWjaBdaJ3TaxmNgCLWwvWzg==", "_location": "/@sinonjs/formatio", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "@sinonjs/formatio@2.0.0", "name": "@sinonjs/formatio", "escapedName": "@sinonjs%2fformatio", "scope": "@sinonjs", "rawSpec": "2.0.0", "saveSpec": null, "fetchSpec": "2.0.0"}, "_requiredBy": ["/sinon"], "_resolved": "https://registry.npmjs.org/@sinonjs/formatio/-/formatio-2.0.0.tgz", "_spec": "2.0.0", "_where": "/home/<USER>/scripts/synchro_files", "author": {"name": "<PERSON>"}, "bugs": {"url": "https://github.com/sinonjs/formatio/issues"}, "dependencies": {"samsam": "1.3.0"}, "description": "Human-readable object formatting", "devDependencies": {"eslint": "4.16.0", "eslint-config-sinon": "1.0.3", "eslint-plugin-ie11": "1.0.0", "eslint-plugin-mocha": "4.11.0", "mocha": "5.0.0", "nyc": "11.4.1", "referee": "1.2.0"}, "files": ["lib/**/*[^test].js"], "homepage": "http://busterjs.org/docs/formatio/", "license": "BSD-3-<PERSON><PERSON>", "main": "./lib/formatio", "name": "@sinonjs/formatio", "repository": {"type": "git", "url": "git+https://github.com/sinonjs/formatio.git"}, "scripts": {"lint": "eslint .", "test": "mocha 'lib/**/*.test.js'", "test-coverage": "nyc --reporter text --reporter html --reporter lcovonly npm run test"}, "version": "2.0.0"}