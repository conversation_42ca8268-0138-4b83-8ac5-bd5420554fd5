{"_args": [["@sinonjs/commons@1.8.6", "/home/<USER>/scripts/synchro_files"]], "_development": true, "_from": "@sinonjs/commons@1.8.6", "_id": "@sinonjs/commons@1.8.6", "_inBundle": false, "_integrity": "sha512-Ky+XkAkqPZSm3NLBeUng77EBQl3cmeJhITaGHdYH8kjVB+aun3S4XBRti2zt17mtt0mIUDiNxYeoJm6drVvBJQ==", "_location": "/@sinonjs/commons", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "@sinonjs/commons@1.8.6", "name": "@sinonjs/commons", "escapedName": "@sinonjs%2fcommons", "scope": "@sinonjs", "rawSpec": "1.8.6", "saveSpec": null, "fetchSpec": "1.8.6"}, "_requiredBy": ["/@sinonjs/samsam", "/nise/@sinonjs/formatio", "/nise/lolex"], "_resolved": "https://registry.npmjs.org/@sinonjs/commons/-/commons-1.8.6.tgz", "_spec": "1.8.6", "_where": "/home/<USER>/scripts/synchro_files", "author": "", "bugs": {"url": "https://github.com/sinonjs/commons/issues"}, "dependencies": {"type-detect": "4.0.8"}, "description": "Simple functions shared among the sinon end user libraries", "devDependencies": {"@sinonjs/eslint-config": "^4.0.6", "@sinonjs/eslint-plugin-no-prototype-methods": "^0.1.0", "@sinonjs/referee-sinon": "^10.1.0", "@studio/changes": "^2.2.0", "husky": "^6.0.0", "jsverify": "0.8.4", "knuth-shuffle": "^1.0.8", "lint-staged": "^13.0.3", "mocha": "^10.1.0", "nyc": "^15.1.0", "prettier": "^2.7.1", "typescript": "^4.8.4"}, "files": ["lib", "types"], "homepage": "https://github.com/sinonjs/commons#readme", "license": "BSD-3-<PERSON><PERSON>", "lint-staged": {"*.{js,css,md}": "prettier --check", "*.js": "eslint"}, "main": "lib/index.js", "name": "@sinonjs/commons", "repository": {"type": "git", "url": "git+https://github.com/sinonjs/commons.git"}, "scripts": {"build": "rm -rf types && tsc", "lint": "eslint .", "postversion": "git push --follow-tags && npm publish", "precommit": "lint-staged", "prepare": "husky install", "prepublishOnly": "npm run build", "prettier:check": "prettier --check '**/*.{js,css,md}'", "prettier:write": "prettier --write '**/*.{js,css,md}'", "preversion": "npm run test-check-coverage", "test": "mocha --recursive -R dot \"lib/**/*.test.js\"", "test-check-coverage": "npm run test-coverage && nyc check-coverage --branches 100 --functions 100 --lines 100", "test-coverage": "nyc --reporter text --reporter html --reporter lcovonly npm run test", "version": "changes --commits --footer"}, "types": "./types/index.d.ts", "version": "1.8.6"}