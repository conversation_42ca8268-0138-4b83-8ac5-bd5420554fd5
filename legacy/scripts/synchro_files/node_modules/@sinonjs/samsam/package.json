{"_args": [["@sinonjs/samsam@3.3.3", "/home/<USER>/scripts/synchro_files"]], "_development": true, "_from": "@sinonjs/samsam@3.3.3", "_id": "@sinonjs/samsam@3.3.3", "_inBundle": false, "_integrity": "sha512-bKCMKZvWIjYD0BLGnNrxVuw4dkWCYsLqFOUWw8VgKF/+5Y+mE7LfHWPIYoDXowH+3a9LsWDMo0uAP8YDosPvHQ==", "_location": "/@sinonjs/samsam", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "@sinonjs/samsam@3.3.3", "name": "@sinonjs/samsam", "escapedName": "@sinonjs%2fsamsam", "scope": "@sinonjs", "rawSpec": "3.3.3", "saveSpec": null, "fetchSpec": "3.3.3"}, "_requiredBy": ["/nise/@sinonjs/formatio"], "_resolved": "https://registry.npmjs.org/@sinonjs/samsam/-/samsam-3.3.3.tgz", "_spec": "3.3.3", "_where": "/home/<USER>/scripts/synchro_files", "author": {"name": "<PERSON>"}, "bugs": {"url": "https://github.com/sinonjs/samsam/issues"}, "dependencies": {"@sinonjs/commons": "^1.3.0", "array-from": "^2.1.1", "lodash": "^4.17.15"}, "description": "Value identification and comparison functions", "devDependencies": {"@sinonjs/referee": "^2.0.0", "benchmark": "2.1.4", "eslint": "^4.19.1", "eslint-config-prettier": "2.9.0", "eslint-config-sinon": "^1.0.3", "eslint-plugin-ie11": "^1.0.0", "eslint-plugin-mocha": "^4.11.0", "eslint-plugin-prettier": "2.6.2", "husky": "^0.14.3", "jsdom": "^13.0.0", "jsdom-global": "^3.0.2", "lint-staged": "^6.1.0", "microtime": "2.1.8", "mkdirp": "^0.5.1", "mocha": "^6.2.0", "mochify": "^6.4.1", "npm-run-all": "^4.1.2", "nyc": "^14.1.1", "prettier": "1.13.7", "rollup": "^0.57.1", "rollup-plugin-commonjs": "^9.1.0"}, "files": ["dist/", "docs/", "lib/", "!lib/**/*.test.js"], "homepage": "http://sinonjs.github.io/samsam/", "license": "BSD-3-<PERSON><PERSON>", "lint-staged": {"*.js": "eslint"}, "main": "./lib/samsam", "name": "@sinonjs/samsam", "repository": {"type": "git", "url": "git+https://github.com/sinonjs/samsam.git"}, "scripts": {"benchmark": "node lib/deep-equal-benchmark.js", "build": "run-s build:dist-folder build:bundle", "build:bundle": "rollup -c > dist/samsam.js", "build:dist-folder": "mkdirp dist", "lint": "eslint .", "prepublishOnly": "npm run build && mkdocs gh-deploy -r upstream || mkdocs gh-deploy -r origin", "test": "mocha ./lib/*.test.js", "test-cloud": "npm run test-headless -- --wd", "test-coverage": "nyc --reporter text --reporter html --reporter lcovonly npm run test", "test-headless": "mochify lib/*.test.js"}, "version": "3.3.3"}