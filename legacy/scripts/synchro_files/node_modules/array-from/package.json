{"_args": [["array-from@2.1.1", "/home/<USER>/scripts/synchro_files"]], "_development": true, "_from": "array-from@2.1.1", "_id": "array-from@2.1.1", "_inBundle": false, "_integrity": "sha512-GQTc6Uupx1FCavi5mPzBvVT7nEOeWMmUA9P95wpfpW1XwMSKs+KaymD5C2Up7KAUKg/mYwbsUYzdZWcoajlNZg==", "_location": "/array-from", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "array-from@2.1.1", "name": "array-from", "escapedName": "array-from", "rawSpec": "2.1.1", "saveSpec": null, "fetchSpec": "2.1.1"}, "_requiredBy": ["/@sinonjs/samsam"], "_resolved": "https://registry.npmjs.org/array-from/-/array-from-2.1.1.tgz", "_spec": "2.1.1", "_where": "/home/<USER>/scripts/synchro_files", "bugs": {"url": "https://github.com/studio-b12/array-from/issues"}, "contributors": [{"name": "<PERSON><PERSON>", "email": "t.wisz<PERSON><PERSON>@gmail.com"}, {"name": "<PERSON>", "url": "http://github.com/barberboy"}], "dependencies": {}, "description": "A ponyfill for the ES 2015 (ES6) `Array.from()`.", "devDependencies": {"1-liners": "0.2.2", "core-js": "^1.0.0", "coveralls": "2.11.2", "istanbul": "0.3.14", "jshint": "2.7.0", "lodash.isnative": "^3.0.4", "nodangel": "1.3.8", "tap-spec": "2.2.2", "tape": "4.2.2", "tape-catch": "1.0.4"}, "files": ["/*.js", "/Readme.md", "/License.md"], "homepage": "https://github.com/studio-b12/array-from#readme", "keywords": ["Array.from", "ponyfill", "polyfill", "convert", "to", "array", "es-2015", "es2015", "es6"], "license": "MIT", "name": "array-from", "repository": {"type": "git", "url": "git+ssh://**************/studio-b12/array-from.git"}, "scripts": {"coverage": "istanbul cover test.js", "coveralls": "npm run coverage && cat ./coverage/lcov.info | coveralls", "develop": "nodangel --ignore node_modules --ignore coverage --exec 'npm run --silent test:lite'", "test": "jshint . && npm run test:lite", "test:lite": "node test.js | tap-spec", "view-coverage": "echo 'Generating coverage reports...'; npm run coverage >/dev/null && echo '...done.' && xdg-open ./coverage/lcov-report/index.html >/dev/null"}, "version": "2.1.1"}