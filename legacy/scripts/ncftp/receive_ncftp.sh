# The "file in use" case can be tested using: flock -w 7 /home/<USER>/fromAgatha/test.err -c "sleep 5"

#!/bin/bash

#Output date
DATE=`date '+%Y-%m-%d %H:%M:%S'`
echo "===== Agatha ncftp get ${DATE}"

# Check and read script parameters:
if test "$#" -ne 3; then
  echo "Illegal number of parameters. Please provide the config file, remote dir, and local path as script parameters"
  echo "For example: bash receive_ncftp.sh /home/<USER>/.ncftp_lacroix.cfg outbound /home/<USER>/fromTtis"
  exit 1
fi

CONFIG_FILE=$1
REMOTE_DIR=$2
LOCAL_DIR=$3

if [ -e $LOCAL_DIR*_receive.lock ] ; then
  echo "=== Skipping receive ncftp " $(date +"%Y-%m-%d %H:%M:%S") "========================================="
  exit
fi

# End the script if the local folder doesn't exist:
if [ ! -d "$LOCAL_DIR" ]; then
  echo "===== Ending because the local folder "$LOCAL_DIR" does NOT exist !"
  exit 1
fi
# Create lockFile (with timestamp in millisecond rounded to the previous second)
touch $LOCAL_DIR$(date +%s)000_receive.lock
# Retreive remote files
ncftpget -f $CONFIG_FILE -DD $LOCAL_DIR ''"$REMOTE_DIR"'*' #$2>&1 > /dev/null
GET_RESULT=$?

if [ "${GET_RESULT}" -eq 0 ] ; then
  # Delete lockFile
  rm $LOCAL_DIR*_receive.lock
  echo "===== Get finished successfuly"
elif [ "${GET_RESULT}" -eq 3 ] || [ "${GET_RESULT}" -eq 15 ] ; then
  # Not an error only no data. Also delete lockFile
  rm $LOCAL_DIR*_receive.lock
  echo "===== Get finished with no data to retrieve (code ${GET_RESULT})"
else
  echo "===== Get failed with error code ${GET_RESULT}."
  exit 1
fi

# Exit with code 0 in case of success
exit 0
