#!/bin/bash
if [ -z "$1" ]; then
  echo "Please provide the connection folder as parameter. For example: arrow_eolane"
  echo "Configuration for connection ${connection} interrupted!"
  exit -1
fi

if [ -z "$2" ]; then
  echo "Please provide the FTP host as parameter. For example: ftp.eng.arrow.com"
  echo "Configuration for connection ${connection} interrupted!"
  exit -1
fi

if [ -z "$3" ]; then
  echo "Please provide the FTP login as parameter. For example: eolane"
  echo "Configuration for connection ${connection} interrupted!"
  exit -1
fi

if [ -z "$4" ]; then
  echo "Please provide the inbound FTP folder as parameter. For example: inbound/prod/ or inbound/"
  echo "Configuration for connection ${connection} interrupted!"
  exit -1
fi

if [ -z "$5" ]; then
  echo "Please provide the outbound FTP folder as parameter. For example: outbound/prod/ or outbound/"
  echo "Configuration for connection ${connection} interrupted!"
  exit -1
fi

connection=$1
ftphost=$2
ftplogin=$3
ftpinboundfolder=$4
ftpoutboundfolder=$5

echo ""
echo "Start configuration for connection ${connection}"
echo ""

# Create the .ncftp_xxx.cfg file:
if [ ! -f /home/<USER>/.ncftp_${connection}.cfg ]; then
  echo "host ${ftphost}" > /home/<USER>/.ncftp_${connection}.cfg
  echo "user ${ftplogin}" >> /home/<USER>/.ncftp_${connection}.cfg
  echo "pass XXX" >> /home/<USER>/.ncftp_${connection}.cfg
  echo "/home/<USER>/.ncftp_${connection}.cfg created."
  echo ">> Don't forget to specify the right password!"
else
  echo "/home/<USER>/.ncftp_${connection}.cfg already exists. File creation skipped."
fi

echo ""

# Create folders:
if [ ! -d /home/<USER>/rsync_tmp/production/${connection}/fromAgatha ]; then
  mkdir -p /home/<USER>/rsync_tmp/production/${connection}/fromAgatha
  echo "Folder /home/<USER>/rsync_tmp/production/${connection}/fromAgatha created."
else
  echo "Folder /home/<USER>/rsync_tmp/production/${connection}/fromAgatha already exists. Creation skipped."
fi
if [ ! -d /home/<USER>/rsync_tmp/production/${connection}/toAgatha ]; then
  mkdir -p /home/<USER>/rsync_tmp/production/${connection}/toAgatha
  echo "Folder /home/<USER>/rsync_tmp/production/${connection}/toAgatha created."
else
  echo "Folder /home/<USER>/rsync_tmp/production/${connection}/toAgatha already exists. Creation skipped."
fi
if [ ! -d /home/<USER>/${connection}/production/agatha_to_sap ]; then
  mkdir -p /home/<USER>/${connection}/production/agatha_to_sap
  echo "Folder /home/<USER>/${connection}/production/agatha_to_sap created."
else
  echo "Folder /home/<USER>/${connection}/production/agatha_to_sap already exists. Creation skipped."
fi

echo ""

# Add job lines in the crontab:
if ! crontab -l | grep -q "# SYNCHRO CONFIG FOR ${connection}"; then
  line="" && (crontab -u agatha -l; echo "$line" ) | crontab -u agatha -
  line="# SYNCHRO CONFIG FOR ${connection}" && (crontab -u agatha -l; echo "$line" ) | crontab -u agatha -
  line="#TMP COMMENT */10 * * * *  bash /home/<USER>/scripts/ncftp/send_ncftp.sh     /home/<USER>/.ncftp_${connection}.cfg  /home/<USER>/rsync_tmp/production/${connection}/fromAgatha  ${ftpinboundfolder}   >> /tmp/${connection}_send.log.txt     2>&1" && (crontab -u agatha -l; echo "$line" ) | crontab -u agatha -
  line="#TMP COMMENT */10 * * * *  bash /home/<USER>/scripts/ncftp/receive_ncftp.sh  /home/<USER>/.ncftp_${connection}.cfg  ${ftpoutboundfolder}  /home/<USER>/rsync_tmp/production/${connection}/toAgatha/   >> /tmp/${connection}_receive.log.txt  2>&1" && (crontab -u agatha -l; echo "$line" ) | crontab -u agatha -
  line="#TMP COMMENT */10 * * * *  s3cmd get --delete-after-fetch --preserve  s3://csv-backups/sync_with_file_proxy/production/${connection}/*  /home/<USER>/${connection}/production/agatha_to_sap  >> /tmp/s3rsync_${connection}.log.txt  2>&1" && (crontab -u agatha -l; echo "$line" ) | crontab -u agatha -
  line="#TMP COMMENT */10 * * * *  node /home/<USER>/scripts/synchro_files/app.js ${connection}.config.js >> /tmp/synchro_files.log.txt 2>&1" && (crontab -u agatha -l; echo "$line" ) | crontab -u agatha -
  line="" && (crontab -u agatha -l; echo "$line" ) | crontab -u agatha -
  echo "Synchro cron jobs were added"
  echo ">> Don't forget to un-comment the cron jobs"
  echo ""
else
  echo echo "Synchro cron jobs already exist. Creation skipped."
fi

echo ""
echo "Configuration for connection ${connection} ended"
echo ""
