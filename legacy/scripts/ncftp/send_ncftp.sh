# The "file in use" case can be tested using: flock -w 7 /home/<USER>/toAgatha/test.err -c "sleep 5"

#!/bin/bash

#Output date
DATE=`date '+%Y-%m-%d %H:%M:%S'`
echo "===== Agatha ncftp put ${DATE}"

# Check and read script parameters:
if test "$#" -ne 3; then
  echo "Illegal number of parameters. Please provide the config file, local path, and remote dir as script parameters"
  echo "For example: bash send_ncftp.sh /home/<USER>/.ncftp_lacroix.cfg /home/<USER>/toTtis inbound"
  exit 1
fi

CONFIG_FILE=$1
LOCAL_DIR=$2
REMOTE_DIR=$3

# End the script if the local folder doesn't exist:
if [ ! -d "$LOCAL_DIR" ]; then
  echo "===== Ending because the local folder "$LOCAL_DIR" does NOT exist !"
  exit 1
fi

# End the script if the folder is empty:
if [ "$(ls -A "$LOCAL_DIR/" 2> /dev/null)" == "" ]; then
  echo "===== Ending because no file is pending to be uploaded"
  # Exit with code 0
  exit 0
fi

IN_USE_ERRORS=0
UPLOAD_ERRORS=0
# Avoid sending an error if no file is found
shopt -s nullglob
for local_file in $LOCAL_DIR/* ; do
  # Check that the file in question is not open.
  # lsof returns non-zero return value for file not in use
  # -S option is for timeout
  lsof -S 15 "${local_file}" 2>&1 > /dev/null
  FILE_IN_USE=$?

  if [ "${FILE_IN_USE}" -eq 0 ] ; then
    IN_USE_ERRORS=$((IN_USE_ERRORS + 1))
    echo "===== $local_file skipped because the file is in use"
  else
    echo "Upload $local_file to Remote..."
    ncftpput -f $CONFIG_FILE -DD ''"$REMOTE_DIR"'/' $local_file #2>&1 > /dev/null
    UPLOAD_RESULT=$?

    ## Check upload result
    if  [ "$UPLOAD_RESULT" -eq 0 ] ; then
      echo "===== Upload succeeded"
    else
      UPLOAD_ERRORS=$((UPLOAD_ERRORS + 1))
      echo "===== Upload failed with error code ${UPLOAD_RESULT}."
    fi
  fi
done

echo "===== Upload finished"

# Display number of errors, if any
if [ $UPLOAD_ERRORS -gt 0 ] ; then
  echo "!!    Encountered ${UPLOAD_ERRORS} upload errors !"
  # Exit with code 1 in case of upload failure
  exit 1
fi

if [ $IN_USE_ERRORS -gt 0 ] ; then
  echo "!!    Encountered ${IN_USE_ERRORS} files in use !"
fi

# Exit with code 0 in case of success
exit 0
