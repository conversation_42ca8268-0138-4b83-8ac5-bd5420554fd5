version: '3.8'

services:
  processor:
    build: .
    container_name: file-processor
    restart: unless-stopped
    environment:
      - NODE_ENV=production
      - LOG_LEVEL=info
      - AWS_ACCESS_KEY_ID=${AWS_ACCESS_KEY_ID}
      - AWS_SECRET_ACCESS_KEY=${AWS_SECRET_ACCESS_KEY}
      - AWS_REGION=${AWS_REGION:-us-east-1}
      - POSTGRES_URL=${POSTGRES_URL}
      - SLACK_WEBHOOK_URL=${SLACK_WEBHOOK_URL}
      - CONFIG_FILE=${CONFIG_FILE:-allcircuits_tis.yml}
    volumes:
      # Mount configuration
      - ./config:/app/config:ro
      # Mount data directories (adjust paths as needed for your system)
      - /home/<USER>/rsync_tmp:/app/data/downloads
      - /home/<USER>/allcircuits_tis:/app/data/uploads
      - /home/<USER>/rsync_backups:/app/data/backups
      - ./data/processing:/app/data/processing
      # Mount logs
      - ./logs:/app/logs
    ports:
      - "3000:3000"
    networks:
      - file-processor-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # Optional: Local PostgreSQL for development
  postgres:
    image: postgres:15-alpine
    container_name: file-processor-db
    restart: unless-stopped
    environment:
      - POSTGRES_DB=file_processor
      - POSTGRES_USER=processor
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD:-processor123}
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./sql/init.sql:/docker-entrypoint-initdb.d/init.sql:ro
    ports:
      - "5432:5432"
    networks:
      - file-processor-network
    profiles:
      - dev

  # Optional: Redis for caching and job queues
  redis:
    image: redis:7-alpine
    container_name: file-processor-redis
    restart: unless-stopped
    volumes:
      - redis_data:/data
    ports:
      - "6379:6379"
    networks:
      - file-processor-network
    profiles:
      - dev

networks:
  file-processor-network:
    driver: bridge

volumes:
  postgres_data:
  redis_data:
