# AllCircuits TIS Configuration
# Migrated from legacy/scripts/synchro_files/allcircuits_tis.config.js

client:
  name: "allcircuits_tis"
  environment: "production"
  description: "AllCircuits TIS file processing configuration"

# FTP/SFTP Configuration
ftp:
  type: "ftp"  # or "sftp"
  host: "${FTP_HOST}"
  port: 21
  username: "${FTP_USERNAME}"
  password: "${FTP_PASSWORD}"
  secure: false
  timeout: 30000
  retry:
    attempts: 3
    delay: 5000

# Directory Paths (maintain backward compatibility)
paths:
  # Incoming files from SAP (FTP download target)
  download: "/app/data/downloads/production/allcircuits_tis/toAgatha"
  # Outgoing files to SAP (FTP upload source)  
  upload: "/app/data/downloads/production/allcircuits_tis/fromAgatha"
  # Local backup directory
  backup: "/app/data/backups/production/allcircuits_tis"
  # Processing staging directory
  processing: "/app/data/processing/allcircuits_tis"
  # Final destination for processed files
  destination: "/app/data/uploads/production"

# S3 Configuration
s3:
  bucket: "csv-backups"
  region: "us-east-1"
  prefix: "allcircuits_tis/production"
  encryption:
    enabled: true
    kms_key_id: "96228f3e-9d7f-41a9-8d08-5e20a294f838"

# Database Configuration
database:
  url: "${POSTGRES_URL}"
  pool:
    min: 2
    max: 10
  ssl: true

# Monitoring Configuration
monitoring:
  slack:
    webhook_url: "${SLACK_WEBHOOK_URL}"
    channel: "#prod-operations"
    error_channel: "#prod-op-errors"
    enabled: true
  
  health_check:
    port: 3000
    enabled: true

# File Processing Workflows
workflows:
  # Incoming files from SAP
  inbound:
    # Audit files - backup only
    - pattern: "(.*)_aleaud\\.csv$"
      description: "Audit files from SAP"
      actions:
        - type: "lock_file"
        - type: "validate_not_empty"
        - type: "backup_local"
          config:
            destination: "sap_to_agatha"
        - type: "backup_s3"
          config:
            subfolder: "sap_to_agatha"
        - type: "unlock_file"

    # Detail files - backup with delay
    - pattern: "DETAIL_(.*)\\.txt$"
      description: "Detail files from SAP"
      actions:
        - type: "delay"
          config:
            duration: 45000  # 45 seconds
        - type: "lock_file"
        - type: "validate_not_empty"
        - type: "backup_local"
          config:
            destination: "sap_to_agatha/archives"
        - type: "backup_s3"
          config:
            subfolder: "sap_to_agatha"
        - type: "unlock_file"

    # Purchasing groups - validate + backup
    - pattern: "(.*)_purchasing_groups\\.csv$"
      description: "Purchasing groups data"
      actions:
        - type: "lock_file"
        - type: "validate_not_empty"
        - type: "validate_csv"
          config:
            schema: "purchasing_groups"
        - type: "backup_local"
          config:
            destination: "sap_to_agatha"
        - type: "backup_s3"
          config:
            subfolder: "sap_to_agatha"
        - type: "unlock_file"

    # Unit ISO codes - validate + backup
    - pattern: "(.*)_unit_iso\\.csv$"
      description: "Unit ISO codes"
      actions:
        - type: "lock_file"
        - type: "validate_not_empty"
        - type: "validate_csv"
          config:
            schema: "unit_iso"
        - type: "backup_local"
          config:
            destination: "sap_to_agatha"
        - type: "backup_s3"
          config:
            subfolder: "sap_to_agatha"
        - type: "unlock_file"

    # Purchase receipts - validate + backup
    - pattern: "(.*)_purchasing_receipt\\.csv$"
      description: "Purchase receipts"
      actions:
        - type: "lock_file"
        - type: "validate_not_empty"
        - type: "validate_csv"
          config:
            schema: "purchasing_receipt"
        - type: "backup_local"
          config:
            destination: "sap_to_agatha"
        - type: "backup_s3"
          config:
            subfolder: "sap_to_agatha"
        - type: "unlock_file"

    # Materials - validate + backup + import to Ruby format
    - pattern: "(.*)_materials\\.csv$"
      description: "Materials data for Ruby import"
      actions:
        - type: "lock_file"
        - type: "validate_not_empty"
        - type: "validate_csv"
          config:
            schema: "materials"
        - type: "backup_local"
          config:
            destination: "sap_to_agatha"
        - type: "backup_s3"
          config:
            subfolder: "sap_to_agatha"
        - type: "transform_copy"
          config:
            destination: "sap_to_agatha"
            filename_pattern: "8_8_{match}.csv"
            pattern_extract: "[0-9]*_[0-9]*_(.*)\\.csv"
        - type: "unlock_file"

    # MRP data - validate + backup + import to Ruby format
    - pattern: "(.*)_mrp\\.csv$"
      description: "MRP data for Ruby import"
      actions:
        - type: "lock_file"
        - type: "validate_not_empty"
        - type: "validate_csv"
          config:
            schema: "mrp"
        - type: "backup_local"
          config:
            destination: "sap_to_agatha"
        - type: "backup_s3"
          config:
            subfolder: "sap_to_agatha"
        - type: "transform_copy"
          config:
            destination: "sap_to_agatha"
            filename_pattern: "8_8_{match}.csv"
            pattern_extract: "[0-9]*_[0-9]*_(.*)\\.csv"
        - type: "unlock_file"

    # Suppliers - validate + backup + import to Ruby format
    - pattern: "(.*)_suppliers\\.csv$"
      description: "Suppliers data for Ruby import"
      actions:
        - type: "lock_file"
        - type: "validate_not_empty"
        - type: "validate_csv"
          config:
            schema: "suppliers"
        - type: "backup_local"
          config:
            destination: "sap_to_agatha"
        - type: "backup_s3"
          config:
            subfolder: "sap_to_agatha"
        - type: "transform_copy"
          config:
            destination: "sap_to_agatha"
            filename_pattern: "8_8_{match}.csv"
            pattern_extract: "[0-9]*_[0-9]*_(.*)\\.csv"
        - type: "unlock_file"

    # Manufacturer references - validate + backup + import to Ruby format
    - pattern: "(.*)_manufacturer_references\\.csv$"
      description: "Manufacturer references for Ruby import"
      actions:
        - type: "lock_file"
        - type: "validate_not_empty"
        - type: "validate_csv"
          config:
            schema: "manufacturer_references"
        - type: "backup_local"
          config:
            destination: "sap_to_agatha"
        - type: "backup_s3"
          config:
            subfolder: "sap_to_agatha"
        - type: "transform_copy"
          config:
            destination: "sap_to_agatha"
            filename_pattern: "8_8_{match}.csv"
            pattern_extract: "[0-9]*_[0-9]*_(.*)\\.csv"
        - type: "unlock_file"

    # Purchasing referents - validate + backup + import as contracts
    - pattern: "(.*)_purchasing_referent\\.csv$"
      description: "Purchasing referents imported as contracts"
      actions:
        - type: "lock_file"
        - type: "validate_not_empty"
        - type: "validate_csv"
          config:
            schema: "purchasing_referent"
        - type: "backup_local"
          config:
            destination: "sap_to_agatha"
        - type: "backup_s3"
          config:
            subfolder: "sap_to_agatha"
        - type: "transform_copy"
          config:
            destination: "sap_to_agatha"
            filename: "8_8_contracts.csv"
        - type: "unlock_file"

    # Contracts - ignore (skip processing)
    - pattern: "(.*)_contracts\\.csv$"
      description: "Contracts files - ignored"
      actions:
        - type: "lock_file"
        - type: "unlock_file"

  # Outgoing files to SAP
  outbound:
    # Order files (create, modify, confirm)
    - pattern: "(.*)(create_order|modify_order|confirm_order)\\.txt$"
      description: "Order files to SAP"
      actions:
        - type: "lock_file"
        - type: "validate_not_empty"
        - type: "backup_local"
          config:
            destination: "agatha_to_sap"
        - type: "copy_to_ftp_staging"
        - type: "backup_s3"
          config:
            subfolder: "agatha_to_sap"
        - type: "unlock_file"

# CSV Schema Definitions for Validation
schemas:
  purchasing_groups:
    required_fields:
      - "purchasing_group_code"
      - "description"
    field_types:
      purchasing_group_code: "string"
      description: "string"
    
  unit_iso:
    required_fields:
      - "unit_code"
      - "iso_code"
    field_types:
      unit_code: "string"
      iso_code: "string"
      
  purchasing_receipt:
    required_fields:
      - "receipt_number"
      - "material_code"
      - "quantity"
    field_types:
      receipt_number: "string"
      material_code: "string"
      quantity: "number"

  materials:
    required_fields:
      - "material_code"
      - "description"
      - "material_type"
    field_types:
      material_code: "string"
      description: "string"
      material_type: "string"

  mrp:
    required_fields:
      - "material_code"
      - "plant"
      - "mrp_type"
    field_types:
      material_code: "string"
      plant: "string"
      mrp_type: "string"

  suppliers:
    required_fields:
      - "supplier_code"
      - "supplier_name"
    field_types:
      supplier_code: "string"
      supplier_name: "string"

  manufacturer_references:
    required_fields:
      - "material_code"
      - "manufacturer"
      - "manufacturer_part_number"
    field_types:
      material_code: "string"
      manufacturer: "string"
      manufacturer_part_number: "string"

  purchasing_referent:
    required_fields:
      - "material_code"
      - "supplier_code"
      - "purchasing_referent"
    field_types:
      material_code: "string"
      supplier_code: "string"
      purchasing_referent: "string"

# File Processing Configuration
processing:
  # Time to wait before checking if file size has changed (in milliseconds)
  file_stability_check_delay: 30000  # 30 seconds
  # Skip files modified within this threshold (in seconds)
  recent_modification_threshold: 30
  # Maximum file size for processing (in bytes, 0 = no limit)
  max_file_size: 0
  # Parallel processing settings
  parallel:
    enabled: false  # Set to true for parallel processing where safe
    max_concurrent: 3

# Scheduling Configuration
schedule:
  enabled: true
  cron: "*/20 * * * *"  # Every 20 minutes
  timezone: "Europe/Paris"

# Logging Configuration
logging:
  level: "info"
  format: "json"
  correlation_id: true
  database_logging: true
