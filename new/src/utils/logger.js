/**
 * Logger Utility
 * 
 * Structured logging with correlation IDs and multiple output formats
 */

const pino = require('pino')
const { v4: uuidv4 } = require('uuid')

/**
 * Create a logger instance with the specified configuration
 */
function createLogger(config = {}) {
  const {
    level = 'info',
    format = 'json',
    correlation_id = true
  } = config

  // Base logger configuration
  const loggerConfig = {
    level,
    timestamp: pino.stdTimeFunctions.isoTime,
    formatters: {
      level: (label) => ({ level: label })
    },
    base: {
      pid: process.pid,
      hostname: require('os').hostname(),
      service: 'file-processor'
    }
  }

  // Configure output format
  if (format === 'pretty' || process.env.NODE_ENV === 'development') {
    loggerConfig.transport = {
      target: 'pino-pretty',
      options: {
        colorize: true,
        translateTime: 'SYS:standard',
        ignore: 'pid,hostname'
      }
    }
  }

  const logger = pino(loggerConfig)

  // Add correlation ID support
  if (correlation_id) {
    const originalChild = logger.child.bind(logger)
    
    logger.child = function(bindings = {}) {
      if (!bindings.correlationId) {
        bindings.correlationId = uuidv4()
      }
      return originalChild(bindings)
    }

    // Add method to create child logger with correlation ID
    logger.withCorrelationId = function(correlationId = uuidv4()) {
      return this.child({ correlationId })
    }
  }

  // Add custom methods for common use cases
  logger.logFileOperation = function(operation, filename, metadata = {}) {
    this.info(`File operation: ${operation}`, {
      operation,
      filename,
      ...metadata
    })
  }

  logger.logError = function(error, context = {}) {
    this.error('Error occurred', {
      error: {
        message: error.message,
        stack: error.stack,
        name: error.name
      },
      ...context
    })
  }

  logger.logPerformance = function(operation, duration, metadata = {}) {
    this.info(`Performance: ${operation}`, {
      operation,
      duration_ms: duration,
      ...metadata
    })
  }

  return logger
}

/**
 * Create a child logger with correlation ID
 */
function createChildLogger(parentLogger, correlationId = uuidv4()) {
  return parentLogger.child({ correlationId })
}

/**
 * Middleware to add correlation ID to requests (for Express)
 */
function correlationMiddleware(logger) {
  return (req, res, next) => {
    const correlationId = req.headers['x-correlation-id'] || uuidv4()
    req.correlationId = correlationId
    req.logger = logger.child({ correlationId })
    
    res.setHeader('x-correlation-id', correlationId)
    next()
  }
}

/**
 * Performance timing utility
 */
class PerformanceTimer {
  constructor(logger, operation, metadata = {}) {
    this.logger = logger
    this.operation = operation
    this.metadata = metadata
    this.startTime = process.hrtime.bigint()
  }

  end(additionalMetadata = {}) {
    const endTime = process.hrtime.bigint()
    const duration = Number(endTime - this.startTime) / 1000000 // Convert to milliseconds
    
    this.logger.logPerformance(this.operation, duration, {
      ...this.metadata,
      ...additionalMetadata
    })
    
    return duration
  }
}

/**
 * Create a performance timer
 */
function createTimer(logger, operation, metadata = {}) {
  return new PerformanceTimer(logger, operation, metadata)
}

module.exports = {
  createLogger,
  createChildLogger,
  correlationMiddleware,
  createTimer,
  PerformanceTimer
}
