/**
 * Configuration Loader
 * 
 * Loads and validates YAML configuration files with environment variable substitution
 */

const fs = require('fs-extra')
const path = require('path')
const YAML = require('yaml')
const Joi = require('joi')

/**
 * Configuration schema for validation
 */
const configSchema = Joi.object({
  client: Joi.object({
    name: Joi.string().required(),
    environment: Joi.string().required(),
    description: Joi.string().optional()
  }).required(),

  ftp: Joi.object({
    type: Joi.string().valid('ftp', 'sftp').required(),
    host: Joi.string().required(),
    port: Joi.number().integer().min(1).max(65535).default(21),
    username: Joi.string().required(),
    password: Joi.string().required(),
    secure: Joi.boolean().default(false),
    timeout: Joi.number().integer().min(1000).default(30000),
    retry: Joi.object({
      attempts: Joi.number().integer().min(1).default(3),
      delay: Joi.number().integer().min(1000).default(5000)
    }).default()
  }).required(),

  paths: Joi.object({
    download: Joi.string().required(),
    upload: Joi.string().required(),
    backup: Joi.string().required(),
    processing: Joi.string().required(),
    destination: Joi.string().required()
  }).required(),

  s3: Joi.object({
    bucket: Joi.string().required(),
    region: Joi.string().default('us-east-1'),
    prefix: Joi.string().required(),
    encryption: Joi.object({
      enabled: Joi.boolean().default(true),
      kms_key_id: Joi.string().when('enabled', {
        is: true,
        then: Joi.required(),
        otherwise: Joi.optional()
      })
    }).default()
  }).required(),

  database: Joi.object({
    url: Joi.string().required(),
    pool: Joi.object({
      min: Joi.number().integer().min(0).default(2),
      max: Joi.number().integer().min(1).default(10)
    }).default(),
    ssl: Joi.boolean().default(true)
  }).required(),

  monitoring: Joi.object({
    slack: Joi.object({
      webhook_url: Joi.string().uri().required(),
      channel: Joi.string().required(),
      error_channel: Joi.string().required(),
      enabled: Joi.boolean().default(true)
    }).required(),
    health_check: Joi.object({
      port: Joi.number().integer().min(1).max(65535).default(3000),
      enabled: Joi.boolean().default(true)
    }).default()
  }).required(),

  workflows: Joi.object({
    inbound: Joi.array().items(
      Joi.object({
        pattern: Joi.string().required(),
        description: Joi.string().optional(),
        actions: Joi.array().items(
          Joi.object({
            type: Joi.string().required(),
            config: Joi.object().optional()
          })
        ).required()
      })
    ).default([]),
    outbound: Joi.array().items(
      Joi.object({
        pattern: Joi.string().required(),
        description: Joi.string().optional(),
        actions: Joi.array().items(
          Joi.object({
            type: Joi.string().required(),
            config: Joi.object().optional()
          })
        ).required()
      })
    ).default([])
  }).required(),

  schemas: Joi.object().pattern(
    Joi.string(),
    Joi.object({
      required_fields: Joi.array().items(Joi.string()).required(),
      field_types: Joi.object().pattern(
        Joi.string(),
        Joi.string().valid('string', 'number', 'boolean', 'date')
      ).required()
    })
  ).default({}),

  schedule: Joi.object({
    enabled: Joi.boolean().default(false),
    cron: Joi.string().when('enabled', {
      is: true,
      then: Joi.required(),
      otherwise: Joi.optional()
    }),
    timezone: Joi.string().default('UTC')
  }).default(),

  logging: Joi.object({
    level: Joi.string().valid('error', 'warn', 'info', 'debug').default('info'),
    format: Joi.string().valid('json', 'pretty').default('json'),
    correlation_id: Joi.boolean().default(true),
    database_logging: Joi.boolean().default(true)
  }).default()
})

/**
 * Substitute environment variables in a string
 * Supports ${VAR_NAME} and ${VAR_NAME:-default_value} syntax
 */
function substituteEnvVars(str) {
  if (typeof str !== 'string') {
    return str
  }

  return str.replace(/\$\{([^}]+)\}/g, (match, varExpr) => {
    const [varName, defaultValue] = varExpr.split(':-')
    const value = process.env[varName]
    
    if (value !== undefined) {
      return value
    }
    
    if (defaultValue !== undefined) {
      return defaultValue
    }
    
    throw new Error(`Environment variable ${varName} is not set and no default value provided`)
  })
}

/**
 * Recursively substitute environment variables in an object
 */
function substituteEnvVarsRecursive(obj) {
  if (typeof obj === 'string') {
    return substituteEnvVars(obj)
  }
  
  if (Array.isArray(obj)) {
    return obj.map(substituteEnvVarsRecursive)
  }
  
  if (obj && typeof obj === 'object') {
    const result = {}
    for (const [key, value] of Object.entries(obj)) {
      result[key] = substituteEnvVarsRecursive(value)
    }
    return result
  }
  
  return obj
}

/**
 * Load and validate configuration from YAML file
 */
async function loadConfig(configPath) {
  try {
    // Check if config file exists
    if (!await fs.pathExists(configPath)) {
      throw new Error(`Configuration file not found: ${configPath}`)
    }

    // Read and parse YAML file
    const configContent = await fs.readFile(configPath, 'utf8')
    let config = YAML.parse(configContent)

    if (!config) {
      throw new Error('Configuration file is empty or invalid')
    }

    // Substitute environment variables
    config = substituteEnvVarsRecursive(config)

    // Validate configuration against schema
    const { error, value } = configSchema.validate(config, {
      allowUnknown: false,
      abortEarly: false
    })

    if (error) {
      const details = error.details.map(detail => detail.message).join(', ')
      throw new Error(`Configuration validation failed: ${details}`)
    }

    // Ensure required directories exist
    const directories = [
      value.paths.download,
      value.paths.upload,
      value.paths.backup,
      value.paths.processing,
      value.paths.destination
    ]

    for (const dir of directories) {
      await fs.ensureDir(dir)
    }

    return value
  } catch (error) {
    if (error.name === 'YAMLParseError') {
      throw new Error(`YAML parsing error in ${configPath}: ${error.message}`)
    }
    throw error
  }
}

/**
 * Validate configuration object
 */
function validateConfig(config) {
  const { error, value } = configSchema.validate(config, {
    allowUnknown: false,
    abortEarly: false
  })

  if (error) {
    const details = error.details.map(detail => detail.message).join(', ')
    throw new Error(`Configuration validation failed: ${details}`)
  }

  return value
}

module.exports = {
  loadConfig,
  validateConfig,
  substituteEnvVars,
  configSchema
}
