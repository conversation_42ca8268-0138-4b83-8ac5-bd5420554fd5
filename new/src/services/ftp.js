/**
 * FTP/SFTP Service
 * 
 * Unified interface for FTP and SFTP operations using Node.js modules
 */

const { Client: FtpClient } = require('basic-ftp')
const SftpClient = require('ssh2-sftp-client')
const fs = require('fs-extra')
const path = require('path')
const { createTimer } = require('../utils/logger')

class FtpService {
  constructor(config, logger) {
    this.config = config
    this.logger = logger
    this.client = null
    this.isConnected = false
    this.connectionAttempts = 0
  }

  async connect() {
    const timer = createTimer(this.logger, 'ftp_connect')

    try {
      this.logger.info('Connecting to FTP/SFTP server', {
        type: this.config.type,
        host: this.config.host,
        port: this.config.port,
        username: this.config.username
      })

      if (this.config.type === 'sftp') {
        this.client = new SftpClient()
        await this.client.connect({
          host: this.config.host,
          port: this.config.port || 22,
          username: this.config.username,
          password: this.config.password,
          readyTimeout: this.config.timeout || 30000,
          algorithms: {
            kex: ['diffie-hellman-group14-sha256', 'diffie-hellman-group14-sha1'],
            cipher: ['aes128-ctr', 'aes192-ctr', 'aes256-ctr'],
            serverHostKey: ['ssh-rsa', 'ssh-dss'],
            hmac: ['hmac-sha2-256', 'hmac-sha2-512', 'hmac-sha1']
          }
        })
      } else {
        this.client = new FtpClient(this.config.timeout || 30000)
        await this.client.access({
          host: this.config.host,
          port: this.config.port || 21,
          user: this.config.username,
          password: this.config.password,
          secure: this.config.secure || false
        })
      }

      this.isConnected = true
      this.connectionAttempts = 0
      
      this.logger.info('Successfully connected to FTP/SFTP server')
      
    } catch (error) {
      this.isConnected = false
      this.connectionAttempts++
      
      this.logger.logError(error, {
        operation: 'ftp_connect',
        type: this.config.type,
        host: this.config.host,
        attempts: this.connectionAttempts
      })
      
      throw new Error(`Failed to connect to ${this.config.type.toUpperCase()} server: ${error.message}`)
    } finally {
      timer.end()
    }
  }

  async disconnect() {
    if (!this.client || !this.isConnected) {
      return
    }

    try {
      this.logger.info('Disconnecting from FTP/SFTP server')
      
      if (this.config.type === 'sftp') {
        await this.client.end()
      } else {
        this.client.close()
      }
      
      this.isConnected = false
      this.client = null
      
      this.logger.info('Disconnected from FTP/SFTP server')
    } catch (error) {
      this.logger.logError(error, {
        operation: 'ftp_disconnect'
      })
    }
  }

  async ensureConnected() {
    if (!this.isConnected) {
      await this.connect()
    }
  }

  async testConnection() {
    const timer = createTimer(this.logger, 'ftp_test_connection')

    try {
      await this.ensureConnected()
      
      // Test by listing root directory
      if (this.config.type === 'sftp') {
        await this.client.list('.')
      } else {
        await this.client.list()
      }
      
      this.logger.info('FTP/SFTP connection test successful')
      
    } catch (error) {
      this.logger.logError(error, {
        operation: 'ftp_test_connection'
      })
      throw new Error(`FTP/SFTP connection test failed: ${error.message}`)
    } finally {
      timer.end()
    }
  }

  async listFiles(remotePath = '.') {
    const timer = createTimer(this.logger, 'ftp_list_files')

    try {
      await this.ensureConnected()
      
      this.logger.info('Listing remote files', { remotePath })
      
      let files
      if (this.config.type === 'sftp') {
        const list = await this.client.list(remotePath)
        files = list
          .filter(item => item.type === '-') // Only files, not directories
          .map(item => ({
            name: item.name,
            size: item.size,
            path: path.posix.join(remotePath, item.name),
            modifiedTime: new Date(item.modifyTime)
          }))
      } else {
        const list = await this.client.list(remotePath)
        files = list
          .filter(item => item.type === 1) // Only files
          .map(item => ({
            name: item.name,
            size: item.size,
            path: path.posix.join(remotePath, item.name),
            modifiedTime: item.modifiedAt
          }))
      }
      
      this.logger.info(`Found ${files.length} files`, {
        remotePath,
        files: files.map(f => ({ name: f.name, size: f.size }))
      })
      
      return files
      
    } catch (error) {
      this.logger.logError(error, {
        operation: 'ftp_list_files',
        remotePath
      })
      throw new Error(`Failed to list files in ${remotePath}: ${error.message}`)
    } finally {
      timer.end()
    }
  }

  async downloadFile(remotePath, localPath) {
    const timer = createTimer(this.logger, 'ftp_download_file')

    try {
      await this.ensureConnected()
      
      // Ensure local directory exists
      await fs.ensureDir(path.dirname(localPath))
      
      this.logger.info('Downloading file', {
        remotePath,
        localPath
      })
      
      if (this.config.type === 'sftp') {
        await this.client.fastGet(remotePath, localPath)
      } else {
        await this.client.downloadTo(localPath, remotePath)
      }
      
      // Verify file was downloaded
      const stat = await fs.stat(localPath)
      
      this.logger.info('File downloaded successfully', {
        remotePath,
        localPath,
        size: stat.size
      })
      
      return { size: stat.size, path: localPath }
      
    } catch (error) {
      this.logger.logError(error, {
        operation: 'ftp_download_file',
        remotePath,
        localPath
      })
      
      // Clean up partial download
      try {
        await fs.remove(localPath)
      } catch (cleanupError) {
        this.logger.logError(cleanupError, {
          operation: 'cleanup_partial_download',
          localPath
        })
      }
      
      throw new Error(`Failed to download ${remotePath}: ${error.message}`)
    } finally {
      timer.end()
    }
  }

  async uploadFile(localPath, remotePath) {
    const timer = createTimer(this.logger, 'ftp_upload_file')

    try {
      await this.ensureConnected()
      
      // Verify local file exists
      const stat = await fs.stat(localPath)
      
      this.logger.info('Uploading file', {
        localPath,
        remotePath,
        size: stat.size
      })
      
      // Ensure remote directory exists
      const remoteDir = path.posix.dirname(remotePath)
      if (remoteDir !== '.') {
        await this.ensureRemoteDirectory(remoteDir)
      }
      
      if (this.config.type === 'sftp') {
        await this.client.fastPut(localPath, remotePath)
      } else {
        await this.client.uploadFrom(localPath, remotePath)
      }
      
      this.logger.info('File uploaded successfully', {
        localPath,
        remotePath,
        size: stat.size
      })
      
      return { size: stat.size, path: remotePath }
      
    } catch (error) {
      this.logger.logError(error, {
        operation: 'ftp_upload_file',
        localPath,
        remotePath
      })
      throw new Error(`Failed to upload ${localPath}: ${error.message}`)
    } finally {
      timer.end()
    }
  }

  async ensureRemoteDirectory(remotePath) {
    try {
      if (this.config.type === 'sftp') {
        await this.client.mkdir(remotePath, true) // recursive
      } else {
        await this.client.ensureDir(remotePath)
      }
    } catch (error) {
      // Directory might already exist, which is fine
      if (!error.message.includes('exists') && !error.message.includes('550')) {
        throw error
      }
    }
  }

  async deleteRemoteFile(remotePath) {
    const timer = createTimer(this.logger, 'ftp_delete_file')

    try {
      await this.ensureConnected()
      
      this.logger.info('Deleting remote file', { remotePath })
      
      if (this.config.type === 'sftp') {
        await this.client.delete(remotePath)
      } else {
        await this.client.remove(remotePath)
      }
      
      this.logger.info('Remote file deleted successfully', { remotePath })
      
    } catch (error) {
      this.logger.logError(error, {
        operation: 'ftp_delete_file',
        remotePath
      })
      throw new Error(`Failed to delete ${remotePath}: ${error.message}`)
    } finally {
      timer.end()
    }
  }

  async withRetry(operation, maxAttempts = null) {
    const attempts = maxAttempts || this.config.retry?.attempts || 3
    const delay = this.config.retry?.delay || 5000

    for (let attempt = 1; attempt <= attempts; attempt++) {
      try {
        return await operation()
      } catch (error) {
        if (attempt === attempts) {
          throw error
        }

        this.logger.warn(`Operation failed, retrying in ${delay}ms`, {
          attempt,
          maxAttempts: attempts,
          error: error.message
        })

        // Reset connection on failure
        this.isConnected = false
        
        await new Promise(resolve => setTimeout(resolve, delay))
      }
    }
  }
}

module.exports = { FtpService }
