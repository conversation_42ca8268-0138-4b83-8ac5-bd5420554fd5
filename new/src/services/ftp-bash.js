/**
 * FTP/SFTP Service using Bash Commands
 * 
 * Alternative implementation using command-line tools like ncftp, lftp, sftp
 * This maintains compatibility with existing ncftp configurations
 */

const { spawn, exec } = require('child_process')
const fs = require('fs-extra')
const path = require('path')
const { promisify } = require('util')
const execAsync = promisify(exec)

class FtpBashService {
  constructor(config, logger) {
    this.config = config
    this.logger = logger
  }

  async testConnection() {
    this.logger.info('Testing FTP/SFTP connection using bash commands')

    try {
      if (this.config.type === 'sftp') {
        await this.testSftpConnection()
      } else {
        await this.testFtpConnection()
      }
      
      this.logger.info('Connection test successful')
    } catch (error) {
      this.logger.logError(error, { operation: 'connection_test' })
      throw new Error(`Connection test failed: ${error.message}`)
    }
  }

  async testFtpConnection() {
    // Use ncftp or lftp to test connection
    const command = this.config.use_ncftp 
      ? `ncftpls -u ${this.config.username} -p ${this.config.password} ftp://${this.config.host}/`
      : `lftp -u ${this.config.username},${this.config.password} -e "ls; quit" ${this.config.host}`

    const { stdout, stderr } = await execAsync(command, { timeout: 30000 })
    
    if (stderr && !stderr.includes('Warning')) {
      throw new Error(`FTP test failed: ${stderr}`)
    }
  }

  async testSftpConnection() {
    // Use sftp command with batch mode
    const batchFile = `/tmp/sftp_test_${Date.now()}.batch`
    await fs.writeFile(batchFile, 'ls\nquit\n')

    try {
      const command = `sshpass -p '${this.config.password}' sftp -b ${batchFile} -o StrictHostKeyChecking=no ${this.config.username}@${this.config.host}`
      const { stdout, stderr } = await execAsync(command, { timeout: 30000 })
      
      if (stderr && stderr.includes('Permission denied')) {
        throw new Error(`SFTP authentication failed`)
      }
    } finally {
      await fs.remove(batchFile)
    }
  }

  async listFiles(remotePath = '.') {
    this.logger.info('Listing remote files', { remotePath })

    try {
      if (this.config.type === 'sftp') {
        return await this.listFilesSftp(remotePath)
      } else {
        return await this.listFilesFtp(remotePath)
      }
    } catch (error) {
      this.logger.logError(error, { operation: 'list_files', remotePath })
      throw new Error(`Failed to list files: ${error.message}`)
    }
  }

  async listFilesFtp(remotePath) {
    const command = this.config.use_ncftp
      ? `ncftpls -u ${this.config.username} -p ${this.config.password} ftp://${this.config.host}/${remotePath}`
      : `lftp -u ${this.config.username},${this.config.password} -e "ls ${remotePath}; quit" ${this.config.host}`

    const { stdout } = await execAsync(command, { timeout: 30000 })
    
    // Parse output to extract file information
    const files = []
    const lines = stdout.trim().split('\n')
    
    for (const line of lines) {
      if (line.trim() && !line.startsWith('d')) { // Skip directories
        const parts = line.trim().split(/\s+/)
        if (parts.length >= 9) {
          const filename = parts.slice(8).join(' ')
          const size = parseInt(parts[4]) || 0
          
          files.push({
            name: filename,
            size: size,
            path: path.posix.join(remotePath, filename),
            modifiedTime: new Date() // Would need more parsing for actual date
          })
        }
      }
    }
    
    return files
  }

  async listFilesSftp(remotePath) {
    const batchFile = `/tmp/sftp_list_${Date.now()}.batch`
    await fs.writeFile(batchFile, `ls -la ${remotePath}\nquit\n`)

    try {
      const command = `sshpass -p '${this.config.password}' sftp -b ${batchFile} -o StrictHostKeyChecking=no ${this.config.username}@${this.config.host}`
      const { stdout } = await execAsync(command, { timeout: 30000 })
      
      // Parse SFTP ls output
      const files = []
      const lines = stdout.trim().split('\n')
      
      for (const line of lines) {
        if (line.startsWith('-')) { // Regular file
          const parts = line.trim().split(/\s+/)
          if (parts.length >= 9) {
            const filename = parts.slice(8).join(' ')
            const size = parseInt(parts[4]) || 0
            
            files.push({
              name: filename,
              size: size,
              path: path.posix.join(remotePath, filename),
              modifiedTime: new Date() // Would need date parsing
            })
          }
        }
      }
      
      return files
    } finally {
      await fs.remove(batchFile)
    }
  }

  async downloadFile(remotePath, localPath) {
    this.logger.info('Downloading file', { remotePath, localPath })

    try {
      // Ensure local directory exists
      await fs.ensureDir(path.dirname(localPath))

      if (this.config.type === 'sftp') {
        await this.downloadFileSftp(remotePath, localPath)
      } else {
        await this.downloadFileFtp(remotePath, localPath)
      }

      // Verify download
      const stat = await fs.stat(localPath)
      this.logger.info('File downloaded successfully', {
        remotePath,
        localPath,
        size: stat.size
      })

      return { size: stat.size, path: localPath }
    } catch (error) {
      this.logger.logError(error, { operation: 'download_file', remotePath, localPath })
      
      // Clean up partial download
      try {
        await fs.remove(localPath)
      } catch (cleanupError) {
        // Ignore cleanup errors
      }
      
      throw new Error(`Failed to download ${remotePath}: ${error.message}`)
    }
  }

  async downloadFileFtp(remotePath, localPath) {
    if (this.config.use_ncftp) {
      // Use ncftpget (like legacy scripts)
      const configFile = this.config.ncftp_config_file
      const command = `ncftpget -f ${configFile} -DD ${path.dirname(localPath)} '${remotePath}'`
      
      const { stderr } = await execAsync(command, { timeout: 300000 }) // 5 min timeout
      
      if (stderr) {
        throw new Error(`ncftpget failed: ${stderr}`)
      }
    } else {
      // Use lftp
      const command = `lftp -u ${this.config.username},${this.config.password} -e "get ${remotePath} -o ${localPath}; quit" ${this.config.host}`
      
      const { stderr } = await execAsync(command, { timeout: 300000 })
      
      if (stderr && !stderr.includes('bytes transferred')) {
        throw new Error(`lftp download failed: ${stderr}`)
      }
    }
  }

  async downloadFileSftp(remotePath, localPath) {
    const batchFile = `/tmp/sftp_download_${Date.now()}.batch`
    await fs.writeFile(batchFile, `get ${remotePath} ${localPath}\nquit\n`)

    try {
      const command = `sshpass -p '${this.config.password}' sftp -b ${batchFile} -o StrictHostKeyChecking=no ${this.config.username}@${this.config.host}`
      
      const { stderr } = await execAsync(command, { timeout: 300000 })
      
      if (stderr && stderr.includes('No such file')) {
        throw new Error(`Remote file not found: ${remotePath}`)
      }
    } finally {
      await fs.remove(batchFile)
    }
  }

  async uploadFile(localPath, remotePath) {
    this.logger.info('Uploading file', { localPath, remotePath })

    try {
      // Verify local file exists
      const stat = await fs.stat(localPath)

      if (this.config.type === 'sftp') {
        await this.uploadFileSftp(localPath, remotePath)
      } else {
        await this.uploadFileFtp(localPath, remotePath)
      }

      this.logger.info('File uploaded successfully', {
        localPath,
        remotePath,
        size: stat.size
      })

      return { size: stat.size, path: remotePath }
    } catch (error) {
      this.logger.logError(error, { operation: 'upload_file', localPath, remotePath })
      throw new Error(`Failed to upload ${localPath}: ${error.message}`)
    }
  }

  async uploadFileFtp(localPath, remotePath) {
    if (this.config.use_ncftp) {
      // Use ncftpput (like legacy scripts)
      const configFile = this.config.ncftp_config_file
      const remoteDir = path.posix.dirname(remotePath)
      const command = `ncftpput -f ${configFile} -DD '${remoteDir}/' ${localPath}`
      
      const { stderr } = await execAsync(command, { timeout: 300000 })
      
      if (stderr) {
        throw new Error(`ncftpput failed: ${stderr}`)
      }
    } else {
      // Use lftp
      const command = `lftp -u ${this.config.username},${this.config.password} -e "put ${localPath} -o ${remotePath}; quit" ${this.config.host}`
      
      const { stderr } = await execAsync(command, { timeout: 300000 })
      
      if (stderr && !stderr.includes('bytes transferred')) {
        throw new Error(`lftp upload failed: ${stderr}`)
      }
    }
  }

  async uploadFileSftp(localPath, remotePath) {
    const batchFile = `/tmp/sftp_upload_${Date.now()}.batch`
    const remoteDir = path.posix.dirname(remotePath)
    
    await fs.writeFile(batchFile, `mkdir ${remoteDir}\nput ${localPath} ${remotePath}\nquit\n`)

    try {
      const command = `sshpass -p '${this.config.password}' sftp -b ${batchFile} -o StrictHostKeyChecking=no ${this.config.username}@${this.config.host}`
      
      const { stderr } = await execAsync(command, { timeout: 300000 })
      
      if (stderr && stderr.includes('Permission denied')) {
        throw new Error(`Upload permission denied: ${remotePath}`)
      }
    } finally {
      await fs.remove(batchFile)
    }
  }

  async disconnect() {
    // No persistent connection to close
    this.logger.info('FTP/SFTP bash service disconnected')
  }
}

module.exports = { FtpBashService }
