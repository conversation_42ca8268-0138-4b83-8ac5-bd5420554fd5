/**
 * File Processor
 * 
 * Main orchestrator for file processing workflows
 */

const fs = require('fs-extra')
const path = require('path')
const cron = require('node-cron')
const { createTimer } = require('../utils/logger')
const { LockManager } = require('./lock-manager')
const { WorkflowEngine } = require('./workflow-engine')
const { FtpService } = require('../services/ftp')

class FileProcessor {
  constructor(config, logger, database, slack) {
    this.config = config
    this.logger = logger
    this.database = database
    this.slack = slack
    
    this.lockManager = new LockManager(config.paths.processing, logger)
    this.workflowEngine = new WorkflowEngine(config, logger, database, slack)
    this.ftpService = new FtpService(config.ftp, logger)
    
    this.isRunning = false
    this.cronJob = null
  }

  async initialize() {
    this.logger.info('Initializing File Processor', {
      client: this.config.client.name,
      environment: this.config.client.environment
    })

    // Initialize workflow engine
    await this.workflowEngine.initialize()

    // Test FTP connection
    await this.ftpService.testConnection()

    this.logger.info('File Processor initialized successfully')
  }

  async startScheduled() {
    if (!this.config.schedule?.enabled) {
      throw new Error('Scheduled processing is not enabled in configuration')
    }

    const { cron: cronExpression, timezone } = this.config.schedule

    this.logger.info('Starting scheduled processing', {
      cron: cronExpression,
      timezone
    })

    this.cronJob = cron.schedule(cronExpression, async () => {
      if (this.isRunning) {
        this.logger.warn('Previous processing cycle still running, skipping this cycle')
        return
      }

      try {
        await this.processOnce()
      } catch (error) {
        this.logger.logError(error, {
          operation: 'scheduled_processing'
        })
        
        await this.slack.sendError(
          `Scheduled processing failed: ${error.message}`,
          { error: error.stack }
        )
      }
    }, {
      scheduled: true,
      timezone
    })

    this.logger.info('Scheduled processing started')
  }

  async processOnce() {
    if (this.isRunning) {
      this.logger.warn('Processing already in progress')
      return
    }

    const timer = createTimer(this.logger, 'full_processing_cycle')
    const correlationId = require('uuid').v4()
    const logger = this.logger.withCorrelationId(correlationId)

    this.isRunning = true

    try {
      logger.info('Starting processing cycle')

      // Check for global lock
      if (await this.lockManager.hasGlobalLock(this.config.client.name)) {
        logger.warn('Global lock detected, skipping processing cycle')
        return
      }

      // Create global lock
      const globalLockId = await this.lockManager.createGlobalLock(this.config.client.name)

      try {
        // Download files from FTP
        await this.downloadFiles(logger)

        // Process inbound files
        await this.processInboundFiles(logger)

        // Process outbound files
        await this.processOutboundFiles(logger)

        // Upload files to FTP
        await this.uploadFiles(logger)

        logger.info('Processing cycle completed successfully')

      } finally {
        // Always remove global lock
        await this.lockManager.removeGlobalLock(globalLockId)
      }

    } catch (error) {
      logger.logError(error, {
        operation: 'processing_cycle',
        correlationId
      })
      throw error
    } finally {
      this.isRunning = false
      timer.end({ correlationId })
    }
  }

  async downloadFiles(logger) {
    const timer = createTimer(logger, 'ftp_download')

    try {
      logger.info('Starting FTP download')

      const remoteFiles = await this.ftpService.listFiles('From_SAP/PRD/')
      logger.info(`Found ${remoteFiles.length} files to download`, {
        files: remoteFiles.map(f => f.name)
      })

      for (const file of remoteFiles) {
        const localPath = path.join(this.config.paths.download, file.name)
        
        try {
          await this.ftpService.downloadFile(file.path, localPath)
          logger.logFileOperation('downloaded', file.name, {
            size: file.size,
            remotePath: file.path,
            localPath
          })
        } catch (error) {
          logger.logError(error, {
            operation: 'file_download',
            filename: file.name,
            remotePath: file.path
          })
          
          await this.slack.sendError(
            `Failed to download file: ${file.name}`,
            { error: error.message }
          )
        }
      }

    } finally {
      timer.end()
    }
  }

  async processInboundFiles(logger) {
    const timer = createTimer(logger, 'inbound_processing')

    try {
      logger.info('Processing inbound files')

      const files = await fs.readdir(this.config.paths.download)
      logger.info(`Found ${files.length} inbound files to process`, { files })

      for (const filename of files) {
        const filePath = path.join(this.config.paths.download, filename)
        const stat = await fs.stat(filePath)

        // Skip directories and recently modified files
        if (stat.isDirectory() || this.isRecentlyModified(stat)) {
          continue
        }

        await this.processFile(filename, filePath, this.config.workflows.inbound, logger)
      }

    } finally {
      timer.end()
    }
  }

  async processOutboundFiles(logger) {
    const timer = createTimer(logger, 'outbound_processing')

    try {
      logger.info('Processing outbound files')

      const outboundPath = path.join(this.config.paths.destination, 'agatha_to_sap')
      
      if (!await fs.pathExists(outboundPath)) {
        logger.info('Outbound directory does not exist, skipping')
        return
      }

      const files = await fs.readdir(outboundPath)
      logger.info(`Found ${files.length} outbound files to process`, { files })

      for (const filename of files) {
        const filePath = path.join(outboundPath, filename)
        const stat = await fs.stat(filePath)

        // Skip directories and recently modified files
        if (stat.isDirectory() || this.isRecentlyModified(stat)) {
          continue
        }

        await this.processFile(filename, filePath, this.config.workflows.outbound, logger)
      }

    } finally {
      timer.end()
    }
  }

  async uploadFiles(logger) {
    const timer = createTimer(logger, 'ftp_upload')

    try {
      logger.info('Starting FTP upload')

      const uploadPath = this.config.paths.upload
      
      if (!await fs.pathExists(uploadPath)) {
        logger.info('Upload directory does not exist, skipping')
        return
      }

      const files = await fs.readdir(uploadPath)
      logger.info(`Found ${files.length} files to upload`, { files })

      for (const filename of files) {
        const localPath = path.join(uploadPath, filename)
        const stat = await fs.stat(localPath)

        // Skip directories and files in use
        if (stat.isDirectory() || await this.isFileInUse(localPath)) {
          continue
        }

        try {
          const remotePath = `To_SAP/PRD/${filename}`
          await this.ftpService.uploadFile(localPath, remotePath)
          
          // Remove file after successful upload
          await fs.remove(localPath)
          
          logger.logFileOperation('uploaded', filename, {
            size: stat.size,
            localPath,
            remotePath
          })
        } catch (error) {
          logger.logError(error, {
            operation: 'file_upload',
            filename,
            localPath
          })
          
          await this.slack.sendError(
            `Failed to upload file: ${filename}`,
            { error: error.message }
          )
        }
      }

    } finally {
      timer.end()
    }
  }

  async processFile(filename, filePath, workflows, logger) {
    const fileLogger = logger.child({ filename })

    try {
      // Find matching workflow
      const workflow = workflows.find(w => new RegExp(w.pattern).test(filename))
      
      if (!workflow) {
        fileLogger.info('No matching workflow found, skipping file')
        return
      }

      fileLogger.info('Processing file with workflow', {
        pattern: workflow.pattern,
        description: workflow.description,
        actionsCount: workflow.actions.length
      })

      // Execute workflow
      await this.workflowEngine.executeWorkflow(workflow, filename, filePath, fileLogger)

      fileLogger.info('File processed successfully')

    } catch (error) {
      fileLogger.logError(error, {
        operation: 'file_processing',
        filename
      })
      
      await this.slack.sendError(
        `Failed to process file: ${filename}`,
        { 
          error: error.message,
          client: this.config.client.name
        }
      )
    }
  }

  isRecentlyModified(stat, thresholdSeconds = 30) {
    const now = Date.now()
    const modifiedTime = stat.mtime.getTime()
    return (now - modifiedTime) < (thresholdSeconds * 1000)
  }

  async isFileInUse(filePath) {
    // Simple check: try to open file for writing
    try {
      const fd = await fs.open(filePath, 'r+')
      await fs.close(fd)
      return false
    } catch (error) {
      return true
    }
  }

  async stop() {
    this.logger.info('Stopping File Processor')

    if (this.cronJob) {
      this.cronJob.destroy()
      this.cronJob = null
    }

    // Wait for current processing to complete
    while (this.isRunning) {
      await new Promise(resolve => setTimeout(resolve, 1000))
    }

    await this.ftpService.disconnect()
    
    this.logger.info('File Processor stopped')
  }
}

module.exports = { FileProcessor }
