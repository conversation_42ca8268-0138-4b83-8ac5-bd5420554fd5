#!/usr/bin/env node

/**
 * File Processing Gateway - Main Entry Point
 * 
 * Modernized file processing system with FTP/SFTP transfers,
 * CSV validation, S3 backup, and PostgreSQL logging.
 */

const path = require('path')
const { createLogger } = require('./utils/logger')
const { loadConfig } = require('./config/loader')
const { FileProcessor } = require('./core/processor')
const { HealthServer } = require('./services/health')
const { DatabaseService } = require('./services/database')
const { SlackService } = require('./services/slack')

// Global error handlers
process.on('uncaughtException', (error) => {
  console.error('Uncaught Exception:', error)
  process.exit(1)
})

process.on('unhandledRejection', (reason, promise) => {
  console.error('Unhandled Rejection at:', promise, 'reason:', reason)
  process.exit(1)
})

async function main() {
  let logger
  let processor
  let healthServer
  let database

  try {
    // Load configuration
    const configFile = process.env.CONFIG_FILE || 'allcircuits_tis.yml'
    const configPath = path.join(__dirname, '..', 'config', configFile)
    const config = await loadConfig(configPath)

    // Initialize logger
    logger = createLogger(config.logging || {})
    logger.info('Starting File Processing Gateway', {
      version: require('../package.json').version,
      config: configFile,
      environment: process.env.NODE_ENV || 'development'
    })

    // Initialize database service
    database = new DatabaseService(config.database, logger)
    await database.initialize()

    // Initialize Slack service
    const slack = new SlackService(config.monitoring.slack, logger)

    // Initialize file processor
    processor = new FileProcessor(config, logger, database, slack)
    await processor.initialize()

    // Start health server
    if (config.monitoring.health_check?.enabled) {
      healthServer = new HealthServer(
        config.monitoring.health_check.port || 3000,
        logger,
        {
          processor,
          database,
          slack
        }
      )
      await healthServer.start()
    }

    // Handle graceful shutdown
    const shutdown = async (signal) => {
      logger.info(`Received ${signal}, shutting down gracefully...`)
      
      try {
        if (healthServer) {
          await healthServer.stop()
        }
        
        if (processor) {
          await processor.stop()
        }
        
        if (database) {
          await database.close()
        }
        
        logger.info('Shutdown complete')
        process.exit(0)
      } catch (error) {
        logger.error('Error during shutdown', { error: error.message })
        process.exit(1)
      }
    }

    // Register shutdown handlers
    process.on('SIGTERM', () => shutdown('SIGTERM'))
    process.on('SIGINT', () => shutdown('SIGINT'))

    // Start processing
    if (config.schedule?.enabled) {
      logger.info('Starting scheduled processing', {
        cron: config.schedule.cron,
        timezone: config.schedule.timezone
      })
      await processor.startScheduled()
    } else {
      logger.info('Starting one-time processing')
      await processor.processOnce()
    }

    logger.info('File Processing Gateway started successfully')

  } catch (error) {
    if (logger) {
      logger.error('Failed to start File Processing Gateway', {
        error: error.message,
        stack: error.stack
      })
    } else {
      console.error('Failed to start File Processing Gateway:', error)
    }
    
    // Cleanup on startup failure
    try {
      if (healthServer) await healthServer.stop()
      if (processor) await processor.stop()
      if (database) await database.close()
    } catch (cleanupError) {
      console.error('Error during cleanup:', cleanupError)
    }
    
    process.exit(1)
  }
}

// Start the application
if (require.main === module) {
  main()
}

module.exports = { main }
