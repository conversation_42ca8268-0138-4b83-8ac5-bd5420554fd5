# File Processing Gateway - Modernization Specs

## Overview
Modernization of legacy file processing scripts into a containerized Node.js solution with YAML configuration, supporting FTP/SFTP transfers, file validation, and database integration.

## Architecture

### Core Components
- **Docker Container**: Isolated environment with Node.js runtime
- **Configuration Engine**: YAML-based configuration management
- **File Transfer Module**: FTP/SFTP client with retry logic
- **Validation Engine**: CSV format validation and field checking
- **Storage Manager**: S3 backup and local file management
- **Database Integration**: DuckDB for validation, PostgreSQL for storage
- **Monitoring**: Slack notifications and PostgreSQL logging

### Technology Stack
- **Runtime**: Node.js 18+ (LTS)
- **Container**: Docker with Alpine Linux base
- **Configuration**: YAML files
- **Database**: DuckDB (validation), PostgreSQL (storage/logs)
- **Storage**: AWS S3 (backup)
- **Monitoring**: Slack API
- **File Transfer**: node-ftp, ssh2-sftp-client
- **CSV Processing**: csv-parser, fast-csv

## AllCircuits TIS Case Study

### Current Legacy Setup
Based on analysis of `allcircuits_tis.config.js`:

#### File Sources
1. **From SAP** (`/home/<USER>/rsync_tmp/production/allcircuits_tis/toAgatha/`):
   - `*_aleaud.csv` - Audit files (backup only)
   - `DETAIL_*.txt` - Detail files (backup with 45s delay)
   - `*_purchasing_groups.csv` - Purchasing groups (validate + backup + import)
   - `*_unit_iso.csv` - Unit ISO codes (validate + backup)
   - `*_manufacturer_references.csv` - Manufacturer refs (validate + backup + import to Ruby format)
   - `*_materials.csv` - Materials (validate + backup + import to Ruby format)
   - `*_mrp.csv` - MRP data (validate + backup + import to Ruby format)
   - `*_purchasing_referent.csv` - Purchasing referents (validate + backup + import as contracts)
   - `*_suppliers.csv` - Suppliers (validate + backup + import to Ruby format)
   - `*_contracts.csv` - Contracts (ignore/skip processing)
   - `*_purchasing_receipt.csv` - Purchase receipts (validate + backup)

2. **To SAP** (`/home/<USER>/allcircuits_tis/production/agatha_to_sap/`):
   - `*create_order.txt` - Order creation files
   - `*modify_order.txt` - Order modification files
   - `*confirm_order.txt` - Order confirmation files

#### Processing Workflows

**Type 1: Backup Only** (`*_aleaud.csv`)
- Lock file → Check empty → Backup to local → Upload to S3 → Unlock

**Type 2: Delayed Backup** (`DETAIL_*.txt`)
- 45s delay → Lock → Check empty → Backup → S3 upload → Unlock

**Type 3: Validate + Backup** (`*_purchasing_groups.csv`, `*_unit_iso.csv`, `*_purchasing_receipt.csv`)
- Lock → Check empty → CSV validation → Backup → S3 upload → Unlock

**Type 4: Validate + Backup + Import** (`*_materials.csv`, `*_mrp.csv`, `*_suppliers.csv`, `*_manufacturer_references.csv`)
- Lock → Check empty → CSV validation → Backup → S3 upload → Copy to Ruby import format → Unlock

**Type 5: Validate + Backup + Import as Contracts** (`*_purchasing_referent.csv`)
- Lock → Check empty → CSV validation → Backup → S3 upload → Copy as contracts.csv → Unlock

**Type 6: Outbound Processing** (order files)
- Lock → Check empty → Backup → Copy to FTP staging → S3 upload → Unlock

### Backward Compatibility Requirements

#### Directory Structure
- **Keep**: `/home/<USER>/rsync_tmp/production/allcircuits_tis/toAgatha/` (FTP download target)
- **Keep**: `/home/<USER>/rsync_tmp/production/allcircuits_tis/fromAgatha/` (FTP upload source)
- **Keep**: `/home/<USER>/allcircuits_tis/production/agatha_to_sap/` (outbound staging)
- **Keep**: `/home/<USER>/allcircuits_tis/production/sap_to_agatha/` (processed files destination)
- **Keep**: `/home/<USER>/rsync_backups/production/allcircuits_tis/` (local backup)

#### File Formats
- **Keep**: All CSV field structures and naming conventions
- **Keep**: TXT file formats for DETAIL files
- **Keep**: Ruby import format naming (`8_8_*.csv`)
- **Keep**: S3 bucket structure and naming

#### Integration Points
- **Keep**: S3 bucket `csv-backups` with existing folder structure
- **Keep**: Slack channels `#prod-operations` and `#prod-op-errors`
- **Keep**: Lock file mechanisms for concurrency control

### Modernization Enhancements

#### New Features
1. **Enhanced Validation**:
   - DuckDB-based CSV structure validation
   - Field name validation per customer
   - Data type and constraint checking
   - Row count and completeness validation

2. **Database Integration**:
   - PostgreSQL logging of all operations
   - Audit trail of file processing
   - Error tracking and reporting
   - Performance metrics

3. **Improved Monitoring**:
   - Structured logging with correlation IDs
   - Detailed Slack notifications with context
   - Health checks and status endpoints
   - Metrics collection

4. **Configuration Management**:
   - YAML-based configuration
   - Environment-specific settings
   - Hot-reload capability
   - Validation of configuration files

#### Technical Improvements
- **Error Handling**: Comprehensive error recovery and retry logic
- **Performance**: Parallel processing where safe
- **Security**: Secure credential management
- **Observability**: Detailed logging and metrics
- **Testing**: Unit and integration test coverage

## Implementation Plan

### Phase 1: Core Infrastructure
1. Docker container setup
2. YAML configuration engine
3. Basic file operations
4. Logging framework

### Phase 2: File Transfer
1. FTP/SFTP client implementation
2. Connection pooling and retry logic
3. Transfer monitoring

### Phase 3: Validation Engine
1. DuckDB integration
2. CSV validation framework
3. Field validation rules

### Phase 4: Storage & Database
1. S3 integration
2. PostgreSQL logging
3. Backup management

### Phase 5: AllCircuits TIS Implementation
1. Configuration migration
2. Workflow implementation
3. Testing and validation
4. Deployment

### Phase 6: Monitoring & Operations
1. Slack integration
2. Health checks
3. Metrics and alerting
4. Documentation