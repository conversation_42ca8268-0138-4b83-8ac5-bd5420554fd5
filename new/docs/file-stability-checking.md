# File Stability Checking

## Overview

The file processing system implements a robust file stability checking mechanism to prevent processing files that are still being written. This is critical for ensuring data integrity and preventing corruption of incomplete files.

## How It Works

### Two-Level Protection

The system uses a two-level approach to detect files that are still being written:

1. **Recent Modification Check**: Skip files modified within a configurable threshold (default: 30 seconds)
2. **Size Stability Check**: Compare file size before and after a configurable delay (default: 30 seconds)

### Implementation Details

#### 1. Recent Modification Check

```javascript
isRecentlyModified(stat, thresholdSeconds = 30) {
  const now = Date.now()
  const modifiedTime = stat.mtime.getTime()
  return (now - modifiedTime) < (thresholdSeconds * 1000)
}
```

This check is performed first and quickly filters out files that were recently modified. It's a fast operation that doesn't require waiting.

#### 2. Size Stability Check

```javascript
async isFileBeingWritten(filePath, logger, waitTimeMs = 30000) {
  // Get initial file size
  const initialStat = await fs.stat(filePath)
  const initialSize = initialStat.size
  
  // Wait for the specified time
  await new Promise(resolve => setTimeout(resolve, waitTimeMs))
  
  // Get file size after waiting
  const finalStat = await fs.stat(filePath)
  const finalSize = finalStat.size
  
  // If size changed, file is still being written
  return finalSize !== initialSize
}
```

This is the primary safety mechanism that:
1. Records the initial file size
2. Waits for a configurable period (default: 30 seconds)
3. Checks the file size again
4. If the size changed, the file is considered "still being written"

## Configuration

The file stability checking behavior can be configured in the YAML configuration file:

```yaml
processing:
  # Time to wait before checking if file size has changed (in milliseconds)
  file_stability_check_delay: 30000  # 30 seconds
  
  # Skip files modified within this threshold (in seconds)
  recent_modification_threshold: 30
  
  # Maximum file size for processing (in bytes, 0 = no limit)
  max_file_size: 0
```

### Configuration Options

- **`file_stability_check_delay`**: Time in milliseconds to wait between size checks
  - Default: 30000 (30 seconds)
  - Minimum: 1000 (1 second)
  - Recommended: 30000-60000 for most use cases

- **`recent_modification_threshold`**: Skip files modified within this many seconds
  - Default: 30 seconds
  - Minimum: 1 second
  - This provides a quick first-pass filter

- **`max_file_size`**: Maximum file size to process (0 = no limit)
  - Default: 0 (no limit)
  - Can be used to skip unexpectedly large files

## Processing Flow

```mermaid
flowchart TD
    A[File Found] --> B{Recently Modified?}
    B -->|Yes| C[Skip File]
    B -->|No| D[Record Initial Size]
    D --> E[Wait 30 seconds]
    E --> F[Check Final Size]
    F --> G{Size Changed?}
    G -->|Yes| H[Skip File - Still Being Written]
    G -->|No| I[Process File]
    C --> J[Continue to Next File]
    H --> J
    I --> K[File Processing Complete]
```

## Logging

The system provides detailed logging for file stability checks:

```json
{
  "level": "info",
  "message": "Checking if file is still being written",
  "filename": "materials.csv",
  "initialSize": 1048576,
  "waitTime": 30000
}
```

If a file is still being written:

```json
{
  "level": "warn",
  "message": "File size changed during wait period, file is still being written",
  "filename": "materials.csv",
  "initialSize": 1048576,
  "finalSize": 1148576,
  "sizeDifference": 100000
}
```

If a file is stable:

```json
{
  "level": "info",
  "message": "File size stable, file is ready for processing",
  "filename": "materials.csv",
  "size": 1048576
}
```

## Error Handling

If the system cannot check file stability (e.g., file permissions, file deleted during check), it assumes the file is being written and skips it:

```json
{
  "level": "error",
  "message": "Error occurred",
  "operation": "file_size_check",
  "filePath": "/path/to/file.csv",
  "error": {
    "message": "ENOENT: no such file or directory"
  }
}
```

## Best Practices

### For File Producers

1. **Atomic Writes**: Write files to a temporary location and move them when complete
2. **Consistent Naming**: Use predictable naming patterns
3. **Size Indicators**: Consider including file size in metadata or filename

### For System Configuration

1. **Adjust Wait Time**: Increase `file_stability_check_delay` for very large files
2. **Monitor Logs**: Watch for frequent "still being written" messages
3. **Test Thoroughly**: Verify behavior with your specific file transfer patterns

### For Large Files

For files larger than a few hundred MB:
- Consider increasing `file_stability_check_delay` to 60-120 seconds
- Monitor system resources during the wait period
- Consider implementing progress indicators in file producers

## Troubleshooting

### Files Never Get Processed

**Symptoms**: Files appear in the directory but are never processed

**Possible Causes**:
1. Files are continuously being modified
2. Wait time is too short for large files
3. File permissions prevent size checking

**Solutions**:
1. Check file producer behavior
2. Increase `file_stability_check_delay`
3. Verify file permissions

### Processing Delays

**Symptoms**: Long delays before files are processed

**Possible Causes**:
1. Wait time is too long for your use case
2. Many files being skipped due to instability

**Solutions**:
1. Reduce `file_stability_check_delay` if appropriate
2. Improve file producer to write atomically
3. Consider parallel processing for independent files

## Legacy Compatibility

This mechanism replaces the legacy `hasSizeChanged` method from the original scripts while providing:
- Better error handling
- Configurable timing
- Detailed logging
- Integration with the modern workflow system

The behavior is equivalent to the legacy system but with improved reliability and observability.
