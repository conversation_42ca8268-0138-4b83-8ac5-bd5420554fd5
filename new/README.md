# File Processing Gateway

A modernized, containerized Node.js solution for file processing, validation, and transfer operations.

## Overview

This project replaces legacy bash/Node.js scripts with a modern, Docker-based solution that provides:

- **FTP/SFTP file transfers** with retry logic
- **CSV validation** using DuckDB
- **S3 backup** with compression and encryption
- **PostgreSQL logging** for audit trails
- **Slack notifications** for monitoring
- **YAML configuration** for easy management

## Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   FTP/SFTP      │    │  File Processor │    │   PostgreSQL    │
│   Servers       │◄──►│                 │◄──►│   (Logging)     │
└─────────────────┘    │                 │    └─────────────────┘
                       │                 │    
┌─────────────────┐    │                 │    ┌─────────────────┐
│   Local Files   │◄──►│                 │◄──►│   AWS S3        │
│   System        │    │                 │    │   (Backup)      │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                              │
                              ▼
                       ┌─────────────────┐
                       │   DuckDB        │
                       │   (Validation)  │
                       └─────────────────┘
```

## Quick Start

### Prerequisites

- Docker and Docker Compose
- Access to FTP/SFTP servers
- AWS S3 credentials
- PostgreSQL database
- Slack webhook URL

### Configuration

1. Copy the example configuration:
```bash
cp config/allcircuits_tis.example.yml config/allcircuits_tis.yml
```

2. Edit the configuration file with your settings:
```yaml
client:
  name: "allcircuits_tis"
  environment: "production"

ftp:
  host: "your-ftp-server.com"
  username: "your-username"
  password: "your-password"
  # ... other settings
```

3. Set environment variables:
```bash
export AWS_ACCESS_KEY_ID="your-access-key"
export AWS_SECRET_ACCESS_KEY="your-secret-key"
export POSTGRES_URL="********************************/db"
export SLACK_WEBHOOK_URL="https://hooks.slack.com/..."
```

### Running

#### Development
```bash
npm install
npm run dev
```

#### Production (Docker)
```bash
docker-compose up -d
```

## Configuration

### YAML Structure

```yaml
client:
  name: string              # Client identifier
  environment: string       # Environment (production, staging, etc.)
  
ftp:
  type: "ftp" | "sftp"     # Connection type
  host: string             # FTP server hostname
  port: number             # Port (21 for FTP, 22 for SFTP)
  username: string         # Username
  password: string         # Password (use env vars in production)
  
paths:
  download: string         # Local download directory
  upload: string           # Local upload directory
  backup: string           # Local backup directory
  processing: string       # Processing staging directory

workflows:
  - pattern: string        # Regex pattern for filename matching
    actions:               # List of actions to perform
      - type: string       # Action type
        config: object     # Action-specific configuration

s3:
  bucket: string           # S3 bucket name
  region: string           # AWS region
  prefix: string           # Key prefix

database:
  url: string              # PostgreSQL connection URL
  
monitoring:
  slack:
    webhook_url: string    # Slack webhook URL
    channel: string        # Default channel
    error_channel: string  # Error notifications channel
```

### Workflow Actions

Available action types:

- `validate_csv`: Validate CSV structure and content
- `backup_local`: Copy file to local backup directory
- `backup_s3`: Upload file to S3 with compression
- `transform`: Apply transformations (rename, modify content)
- `import_database`: Import data to PostgreSQL via DuckDB
- `transfer_ftp`: Upload file via FTP/SFTP
- `delay`: Wait for specified duration
- `notify`: Send Slack notification

## Development

### Project Structure

```
new/
├── src/
│   ├── config/           # Configuration management
│   ├── core/             # Core business logic
│   ├── services/         # External service integrations
│   ├── utils/            # Utility functions
│   └── index.js          # Application entry point
├── config/               # Configuration files
├── tests/                # Test files
├── docker/               # Docker configuration
└── docs/                 # Documentation
```

### Testing

```bash
# Run all tests
npm test

# Run with coverage
npm run test:coverage

# Run specific test file
npm test -- tests/validation.test.js
```

### Logging

The application uses structured logging with correlation IDs:

```javascript
logger.info('Processing file', {
  correlationId: 'req-123',
  filename: 'materials.csv',
  client: 'allcircuits_tis',
  action: 'validate_csv'
});
```

## Migration from Legacy

### AllCircuits TIS Migration

The legacy configuration in `legacy/scripts/synchro_files/allcircuits_tis.config.js` has been migrated to `config/allcircuits_tis.yml`.

Key changes:
- JavaScript config → YAML config
- Hardcoded paths → Environment-specific paths
- Basic CSV validation → DuckDB-powered validation
- Simple logging → Structured logging with PostgreSQL storage

### Backward Compatibility

The new system maintains compatibility with:
- Existing directory structures
- File naming conventions
- S3 bucket organization
- Slack notification channels
- Lock file mechanisms

## Monitoring

### Health Checks

The application exposes health check endpoints:

- `GET /health` - Basic health status
- `GET /health/detailed` - Detailed component status

### Metrics

Key metrics tracked:
- File processing duration
- Validation success/failure rates
- Transfer success/failure rates
- Error rates by type

### Alerts

Slack notifications are sent for:
- Processing errors
- Validation failures
- Transfer failures
- Long-running operations
- System health issues

## Security

- Credentials stored in environment variables
- S3 uploads encrypted with KMS
- Database connections use SSL
- File permissions properly managed
- Input validation on all user data

## Performance

- Parallel processing where safe
- Connection pooling for databases
- Efficient file streaming for large files
- Configurable retry logic with exponential backoff
- Memory-efficient CSV processing

## Support

For issues and questions:
1. Check the logs: `docker-compose logs -f processor`
2. Review configuration files
3. Check Slack notifications for error details
4. Consult the troubleshooting guide in `docs/troubleshooting.md`
