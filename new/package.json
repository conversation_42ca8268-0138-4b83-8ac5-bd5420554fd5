{"name": "file-processing-gateway", "version": "1.0.0", "description": "Modernized file processing, validation, and transfer system", "main": "src/index.js", "scripts": {"start": "node src/index.js", "dev": "nodemon src/index.js", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "lint": "eslint src/ tests/", "lint:fix": "eslint src/ tests/ --fix", "docker:build": "docker build -t file-processor .", "docker:run": "docker-compose up -d", "docker:stop": "docker-compose down", "docker:logs": "docker-compose logs -f processor"}, "keywords": ["file-processing", "ftp", "sftp", "csv-validation", "s3", "postgresql", "docker"], "author": "Precogs", "license": "MIT", "dependencies": {"aws-sdk": "^2.1490.0", "axios": "^1.6.0", "basic-ftp": "^5.0.3", "csv-parser": "^3.0.0", "duckdb": "^0.9.1", "express": "^4.18.2", "fast-csv": "^4.3.6", "fs-extra": "^11.1.1", "joi": "^17.11.0", "moment": "^2.29.4", "pg": "^8.11.3", "pino": "^8.16.1", "pino-pretty": "^10.2.3", "ssh2-sftp-client": "^10.0.3", "uuid": "^9.0.1", "yaml": "^2.3.4"}, "devDependencies": {"eslint": "^8.53.0", "eslint-config-standard": "^17.1.0", "eslint-plugin-import": "^2.29.0", "eslint-plugin-n": "^16.3.1", "eslint-plugin-promise": "^6.1.1", "jest": "^29.7.0", "nodemon": "^3.0.1", "supertest": "^6.3.3"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}, "jest": {"testEnvironment": "node", "collectCoverageFrom": ["src/**/*.js", "!src/index.js"], "coverageDirectory": "coverage", "coverageReporters": ["text", "lcov", "html"]}, "eslintConfig": {"extends": ["standard"], "env": {"node": true, "jest": true}, "rules": {"no-console": "warn"}}}